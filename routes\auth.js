const express = require('express');
const router = express.Router();
const { body, validationResult } = require('express-validator');
const db = require('../config/database');
const { verifyPassword, logUserAction } = require('../middleware/auth');

// Validation middleware for login
const validateLogin = [
    body('username').notEmpty().withMessage('Username is required'),
    body('password').notEmpty().withMessage('Password is required')
];

// POST /api/auth/login - User login
router.post('/login', validateLogin, async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }

        const { username, password } = req.body;

        // Get user from database
        const [user] = await db.executeQuery(
            `SELECT u.*, r.name as role_name, r.permissions 
             FROM users u 
             JOIN roles r ON u.role_id = r.id 
             WHERE u.username = ? AND u.is_active = TRUE`,
            [username]
        );

        if (!user) {
            await logUserAction(null, 'login_failed', 'auth', null, `Failed login attempt for username: ${username}`, req);
            return res.status(401).json({ error: 'Invalid credentials' });
        }

        // Verify password
        const isValidPassword = await verifyPassword(password, user.password_hash);
        if (!isValidPassword) {
            await logUserAction(user.id, 'login_failed', 'auth', null, 'Invalid password', req);
            return res.status(401).json({ error: 'Invalid credentials' });
        }

        // Update last login
        await db.executeQuery(
            'UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?',
            [user.id]
        );

        // Create session
        req.session.userId = user.id;
        req.session.username = user.username;
        req.session.role = user.role_name;

        // Log successful login
        await logUserAction(user.id, 'login_success', 'auth', null, 'User logged in successfully', req);

        res.json({
            message: 'Login successful',
            user: {
                id: user.id,
                username: user.username,
                email: user.email,
                first_name: user.first_name,
                last_name: user.last_name,
                role: user.role_name,
                permissions: user.permissions
            }
        });

    } catch (error) {
        console.error('Login error:', error);
        res.status(500).json({ error: 'Login failed' });
    }
});

// POST /api/auth/logout - User logout
router.post('/logout', async (req, res) => {
    try {
        if (req.session && req.session.userId) {
            const userId = req.session.userId;
            
            // Log logout
            await logUserAction(userId, 'logout', 'auth', null, 'User logged out', req);
            
            // Destroy session
            req.session.destroy((err) => {
                if (err) {
                    console.error('Session destroy error:', err);
                    return res.status(500).json({ error: 'Logout failed' });
                }
                
                res.clearCookie('connect.sid'); // Clear session cookie
                res.json({ message: 'Logout successful' });
            });
        } else {
            res.json({ message: 'No active session' });
        }

    } catch (error) {
        console.error('Logout error:', error);
        res.status(500).json({ error: 'Logout failed' });
    }
});

// GET /api/auth/me - Get current user info
router.get('/me', async (req, res) => {
    try {
        if (!req.session || !req.session.userId) {
            return res.status(401).json({ error: 'Not authenticated' });
        }

        // Get user from database
        const [user] = await db.executeQuery(
            `SELECT u.id, u.username, u.email, u.first_name, u.last_name, u.last_login,
                    r.name as role_name, r.permissions 
             FROM users u 
             JOIN roles r ON u.role_id = r.id 
             WHERE u.id = ? AND u.is_active = TRUE`,
            [req.session.userId]
        );

        if (!user) {
            req.session.destroy();
            return res.status(401).json({ error: 'User not found' });
        }

        res.json({
            user: {
                id: user.id,
                username: user.username,
                email: user.email,
                first_name: user.first_name,
                last_name: user.last_name,
                role: user.role_name,
                permissions: user.permissions,
                last_login: user.last_login
            }
        });

    } catch (error) {
        console.error('Get user info error:', error);
        res.status(500).json({ error: 'Failed to get user info' });
    }
});

// GET /api/auth/check - Check if user is authenticated
router.get('/check', (req, res) => {
    if (req.session && req.session.userId) {
        res.json({ 
            authenticated: true,
            user: {
                id: req.session.userId,
                username: req.session.username,
                role: req.session.role
            }
        });
    } else {
        res.json({ authenticated: false });
    }
});

module.exports = router;
