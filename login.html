<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connexion - Système de Gestion de Fourrière</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .login-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .glass-effect {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }
    </style>
</head>
<body class="login-bg min-h-screen flex items-center justify-center p-4">
    <div class="w-full max-w-md">
        <!-- Login Card -->
        <div class="glass-effect rounded-2xl shadow-2xl p-8">
            <!-- Logo and Title -->
            <div class="text-center mb-8">
                <div class="mx-auto w-16 h-16 bg-white rounded-full flex items-center justify-center mb-4">
                    <i class="fas fa-car text-2xl text-blue-600"></i>
                </div>
                <h1 class="text-2xl font-bold text-white mb-2">Système de Fourrière</h1>
                <p class="text-blue-100">Connectez-vous à votre compte</p>
            </div>

            <!-- Login Form -->
            <form id="loginForm" class="space-y-6">
                <!-- Username Field -->
                <div>
                    <label for="username" class="block text-sm font-medium text-white mb-2">
                        <i class="fas fa-user mr-2"></i>Nom d'utilisateur
                    </label>
                    <input 
                        type="text" 
                        id="username" 
                        name="username" 
                        required
                        class="w-full px-4 py-3 bg-white bg-opacity-20 border border-white border-opacity-30 rounded-lg text-white placeholder-blue-200 focus:outline-none focus:ring-2 focus:ring-white focus:border-transparent"
                        placeholder="Entrez votre nom d'utilisateur"
                    >
                </div>

                <!-- Password Field -->
                <div>
                    <label for="password" class="block text-sm font-medium text-white mb-2">
                        <i class="fas fa-lock mr-2"></i>Mot de passe
                    </label>
                    <div class="relative">
                        <input 
                            type="password" 
                            id="password" 
                            name="password" 
                            required
                            class="w-full px-4 py-3 bg-white bg-opacity-20 border border-white border-opacity-30 rounded-lg text-white placeholder-blue-200 focus:outline-none focus:ring-2 focus:ring-white focus:border-transparent pr-12"
                            placeholder="Entrez votre mot de passe"
                        >
                        <button 
                            type="button" 
                            id="togglePassword"
                            class="absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-200 hover:text-white focus:outline-none"
                        >
                            <i class="fas fa-eye" id="eyeIcon"></i>
                        </button>
                    </div>
                </div>

                <!-- Remember Me -->
                <div class="flex items-center">
                    <input 
                        type="checkbox" 
                        id="remember" 
                        name="remember"
                        class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    >
                    <label for="remember" class="ml-2 block text-sm text-blue-100">
                        Se souvenir de moi
                    </label>
                </div>

                <!-- Submit Button -->
                <button 
                    type="submit" 
                    id="loginButton"
                    class="w-full bg-white text-blue-600 py-3 px-4 rounded-lg font-semibold hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-blue-600 transition duration-200"
                >
                    <span id="loginButtonText">
                        <i class="fas fa-sign-in-alt mr-2"></i>Se connecter
                    </span>
                    <span id="loginButtonLoading" class="hidden">
                        <i class="fas fa-spinner fa-spin mr-2"></i>Connexion...
                    </span>
                </button>
            </form>

            <!-- Error Message -->
            <div id="errorMessage" class="hidden mt-4 p-3 bg-red-500 bg-opacity-20 border border-red-400 rounded-lg">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-triangle text-red-300 mr-2"></i>
                    <span id="errorText" class="text-red-100 text-sm"></span>
                </div>
            </div>

            <!-- Footer -->
            <div class="mt-8 text-center">
                <p class="text-blue-100 text-sm">
                    © 2023 Système de Gestion de Fourrière
                </p>
                <p class="text-blue-200 text-xs mt-1">
                    Version 1.0 - Sécurisé et fiable
                </p>
            </div>
        </div>

        <!-- Default Credentials Info (Development Only) -->
        <div class="mt-6 glass-effect rounded-lg p-4 text-center">
            <p class="text-blue-100 text-sm mb-2">
                <i class="fas fa-info-circle mr-2"></i>Compte par défaut (développement)
            </p>
            <div class="text-blue-200 text-xs space-y-1">
                <p><strong>Utilisateur:</strong> admin</p>
                <p><strong>Mot de passe:</strong> admin123</p>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        class LoginManager {
            constructor() {
                this.form = document.getElementById('loginForm');
                this.errorMessage = document.getElementById('errorMessage');
                this.errorText = document.getElementById('errorText');
                this.loginButton = document.getElementById('loginButton');
                this.loginButtonText = document.getElementById('loginButtonText');
                this.loginButtonLoading = document.getElementById('loginButtonLoading');
                
                this.setupEventListeners();
                this.checkExistingSession();
            }

            setupEventListeners() {
                // Form submission
                this.form.addEventListener('submit', (e) => this.handleLogin(e));

                // Password toggle
                document.getElementById('togglePassword').addEventListener('click', () => {
                    this.togglePasswordVisibility();
                });

                // Enter key on form fields
                document.querySelectorAll('input').forEach(input => {
                    input.addEventListener('keypress', (e) => {
                        if (e.key === 'Enter') {
                            this.form.dispatchEvent(new Event('submit'));
                        }
                    });
                });
            }

            togglePasswordVisibility() {
                const passwordInput = document.getElementById('password');
                const eyeIcon = document.getElementById('eyeIcon');

                if (passwordInput.type === 'password') {
                    passwordInput.type = 'text';
                    eyeIcon.classList.remove('fa-eye');
                    eyeIcon.classList.add('fa-eye-slash');
                } else {
                    passwordInput.type = 'password';
                    eyeIcon.classList.remove('fa-eye-slash');
                    eyeIcon.classList.add('fa-eye');
                }
            }

            async checkExistingSession() {
                try {
                    const response = await fetch('/api/auth/check');
                    const data = await response.json();

                    if (data.authenticated) {
                        // User is already logged in, redirect to dashboard
                        window.location.href = '/';
                    }
                } catch (error) {
                    console.log('No existing session');
                }
            }

            async handleLogin(e) {
                e.preventDefault();

                const formData = new FormData(this.form);
                const credentials = {
                    username: formData.get('username'),
                    password: formData.get('password')
                };

                this.setLoading(true);
                this.hideError();

                try {
                    const response = await fetch('/api/auth/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(credentials)
                    });

                    const data = await response.json();

                    if (response.ok) {
                        // Login successful
                        this.showSuccess('Connexion réussie! Redirection...');
                        
                        // Redirect to dashboard after short delay
                        setTimeout(() => {
                            window.location.href = '/';
                        }, 1000);
                    } else {
                        // Login failed
                        this.showError(data.error || 'Erreur de connexion');
                    }

                } catch (error) {
                    console.error('Login error:', error);
                    this.showError('Erreur de connexion au serveur');
                } finally {
                    this.setLoading(false);
                }
            }

            setLoading(loading) {
                this.loginButton.disabled = loading;
                
                if (loading) {
                    this.loginButtonText.classList.add('hidden');
                    this.loginButtonLoading.classList.remove('hidden');
                } else {
                    this.loginButtonText.classList.remove('hidden');
                    this.loginButtonLoading.classList.add('hidden');
                }
            }

            showError(message) {
                this.errorText.textContent = message;
                this.errorMessage.classList.remove('hidden');
            }

            hideError() {
                this.errorMessage.classList.add('hidden');
            }

            showSuccess(message) {
                // Create success message
                const successDiv = document.createElement('div');
                successDiv.className = 'mt-4 p-3 bg-green-500 bg-opacity-20 border border-green-400 rounded-lg';
                successDiv.innerHTML = `
                    <div class="flex items-center">
                        <i class="fas fa-check-circle text-green-300 mr-2"></i>
                        <span class="text-green-100 text-sm">${message}</span>
                    </div>
                `;
                
                this.form.appendChild(successDiv);
            }
        }

        // Initialize login manager when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            new LoginManager();
        });
    </script>
</body>
</html>
