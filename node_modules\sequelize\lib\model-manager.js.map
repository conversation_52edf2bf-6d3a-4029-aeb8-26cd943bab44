{"version": 3, "sources": ["../src/model-manager.js"], "sourcesContent": ["'use strict';\n\nconst Toposort = require('toposort-class');\nconst _ = require('lodash');\n\nclass ModelManager {\n  constructor(sequelize) {\n    this.models = [];\n    this.sequelize = sequelize;\n  }\n\n  addModel(model) {\n    this.models.push(model);\n    this.sequelize.models[model.name] = model;\n\n    return model;\n  }\n\n  removeModel(modelToRemove) {\n    this.models = this.models.filter(model => model.name !== modelToRemove.name);\n\n    delete this.sequelize.models[modelToRemove.name];\n  }\n\n  getModel(against, options) {\n    options = _.defaults(options || {}, {\n      attribute: 'name'\n    });\n\n    return this.models.find(model => model[options.attribute] === against);\n  }\n\n  findModel(callback) {\n    return this.models.find(callback);\n  }\n\n  get all() {\n    return this.models;\n  }\n\n  /**\n   * Returns an array that lists every model, sorted in order\n   * of foreign key references: The first model is a model that is depended upon,\n   * the last model is a model that is not depended upon.\n   *\n   * If there is a cyclic dependency, this returns null.\n   */\n  getModelsTopoSortedByForeignKey() {\n    const models = new Map();\n    const sorter = new Toposort();\n\n    for (const model of this.models) {\n      let deps = [];\n      let tableName = model.getTableName();\n\n      if (_.isObject(tableName)) {\n        tableName = `${tableName.schema}.${tableName.tableName}`;\n      }\n\n      models.set(tableName, model);\n\n      for (const attrName in model.rawAttributes) {\n        if (Object.prototype.hasOwnProperty.call(model.rawAttributes, attrName)) {\n          const attribute = model.rawAttributes[attrName];\n\n          if (attribute.references) {\n            let dep = attribute.references.model;\n\n            if (_.isObject(dep)) {\n              dep = `${dep.schema}.${dep.tableName}`;\n            }\n\n            deps.push(dep);\n          }\n        }\n      }\n\n      deps = deps.filter(dep => tableName !== dep);\n\n      sorter.add(tableName, deps);\n    }\n\n    let sorted;\n    try {\n      sorted = sorter.sort();\n    } catch (e) {\n      if (!e.message.startsWith('Cyclic dependency found.')) {\n        throw e;\n      }\n\n      return null;\n    }\n\n    return sorted\n      .map(modelName => {\n        return models.get(modelName);\n      })\n      .filter(Boolean);\n  }\n\n  /**\n   * Iterate over Models in an order suitable for e.g. creating tables.\n   * Will take foreign key constraints into account so that dependencies are visited before dependents.\n   *\n   * @param {Function} iterator method to execute on each model\n   * @param {object} options\n   * @private\n   *\n   * @deprecated\n   */\n  forEachModel(iterator, options) {\n    const sortedModels = this.getModelsTopoSortedByForeignKey();\n    if (sortedModels == null) {\n      throw new Error('Cyclic dependency found.');\n    }\n\n    options = _.defaults(options || {}, {\n      reverse: true\n    });\n\n    if (options.reverse) {\n      sortedModels.reverse();\n    }\n\n    for (const model of sortedModels) {\n      iterator(model);\n    }\n  }\n}\n\nmodule.exports = ModelManager;\nmodule.exports.ModelManager = ModelManager;\nmodule.exports.default = ModelManager;\n"], "mappings": ";AAEA,MAAM,WAAW,QAAQ;AACzB,MAAM,IAAI,QAAQ;AAElB,mBAAmB;AAAA,EACjB,YAAY,WAAW;AACrB,SAAK,SAAS;AACd,SAAK,YAAY;AAAA;AAAA,EAGnB,SAAS,OAAO;AACd,SAAK,OAAO,KAAK;AACjB,SAAK,UAAU,OAAO,MAAM,QAAQ;AAEpC,WAAO;AAAA;AAAA,EAGT,YAAY,eAAe;AACzB,SAAK,SAAS,KAAK,OAAO,OAAO,WAAS,MAAM,SAAS,cAAc;AAEvE,WAAO,KAAK,UAAU,OAAO,cAAc;AAAA;AAAA,EAG7C,SAAS,SAAS,SAAS;AACzB,cAAU,EAAE,SAAS,WAAW,IAAI;AAAA,MAClC,WAAW;AAAA;AAGb,WAAO,KAAK,OAAO,KAAK,WAAS,MAAM,QAAQ,eAAe;AAAA;AAAA,EAGhE,UAAU,UAAU;AAClB,WAAO,KAAK,OAAO,KAAK;AAAA;AAAA,MAGtB,MAAM;AACR,WAAO,KAAK;AAAA;AAAA,EAUd,kCAAkC;AAChC,UAAM,SAAS,oBAAI;AACnB,UAAM,SAAS,IAAI;AAEnB,eAAW,SAAS,KAAK,QAAQ;AAC/B,UAAI,OAAO;AACX,UAAI,YAAY,MAAM;AAEtB,UAAI,EAAE,SAAS,YAAY;AACzB,oBAAY,GAAG,UAAU,UAAU,UAAU;AAAA;AAG/C,aAAO,IAAI,WAAW;AAEtB,iBAAW,YAAY,MAAM,eAAe;AAC1C,YAAI,OAAO,UAAU,eAAe,KAAK,MAAM,eAAe,WAAW;AACvE,gBAAM,YAAY,MAAM,cAAc;AAEtC,cAAI,UAAU,YAAY;AACxB,gBAAI,MAAM,UAAU,WAAW;AAE/B,gBAAI,EAAE,SAAS,MAAM;AACnB,oBAAM,GAAG,IAAI,UAAU,IAAI;AAAA;AAG7B,iBAAK,KAAK;AAAA;AAAA;AAAA;AAKhB,aAAO,KAAK,OAAO,SAAO,cAAc;AAExC,aAAO,IAAI,WAAW;AAAA;AAGxB,QAAI;AACJ,QAAI;AACF,eAAS,OAAO;AAAA,aACT,GAAP;AACA,UAAI,CAAC,EAAE,QAAQ,WAAW,6BAA6B;AACrD,cAAM;AAAA;AAGR,aAAO;AAAA;AAGT,WAAO,OACJ,IAAI,eAAa;AAChB,aAAO,OAAO,IAAI;AAAA,OAEnB,OAAO;AAAA;AAAA,EAaZ,aAAa,UAAU,SAAS;AAC9B,UAAM,eAAe,KAAK;AAC1B,QAAI,gBAAgB,MAAM;AACxB,YAAM,IAAI,MAAM;AAAA;AAGlB,cAAU,EAAE,SAAS,WAAW,IAAI;AAAA,MAClC,SAAS;AAAA;AAGX,QAAI,QAAQ,SAAS;AACnB,mBAAa;AAAA;AAGf,eAAW,SAAS,cAAc;AAChC,eAAS;AAAA;AAAA;AAAA;AAKf,OAAO,UAAU;AACjB,OAAO,QAAQ,eAAe;AAC9B,OAAO,QAAQ,UAAU;", "names": []}