// Test script for simplified vehicle form
const http = require('http');

function makeRequest(path, method = 'GET', data = null) {
    return new Promise((resolve, reject) => {
        const url = new URL('http://localhost:3000' + path);
        const options = {
            hostname: url.hostname,
            port: url.port,
            path: url.pathname + url.search,
            method: method,
            headers: {
                'Content-Type': 'application/json'
            }
        };

        const req = http.request(options, (res) => {
            let body = '';
            res.on('data', (chunk) => {
                body += chunk;
            });
            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(body);
                    resolve({ status: res.statusCode, data: jsonData });
                } catch (e) {
                    resolve({ status: res.statusCode, data: body });
                }
            });
        });

        req.on('error', (err) => {
            reject(err);
        });

        if (data) {
            req.write(JSON.stringify(data));
        }

        req.end();
    });
}

async function testSimplifiedForm() {
    console.log('🧪 Testing Simplified Vehicle Form...\n');

    try {
        // Test 1: Get depositors
        console.log('1. Testing depositors API...');
        const depositors = await makeRequest('/api/depositors');
        console.log(`   ✅ Depositors API: ${depositors.status === 200 ? 'PASS' : 'FAIL'}`);
        if (depositors.status === 200) {
            console.log(`   📋 Found ${depositors.data.length} depositors:`);
            depositors.data.forEach(dep => {
                console.log(`      - ${dep.name} ${dep.is_active ? '(Actif)' : '(Inactif)'}`);
            });
        }

        // Test 2: Get vehicle types
        console.log('\n2. Testing vehicle types API...');
        const types = await makeRequest('/api/vehicles/types/list');
        console.log(`   ✅ Vehicle types API: ${types.status === 200 ? 'PASS' : 'FAIL'}`);
        if (types.status === 200) {
            console.log(`   🚗 Found ${types.data.length} vehicle types:`);
            types.data.forEach(type => {
                console.log(`      - ${type.name}`);
            });
        }

        // Test 3: Create a simplified vehicle entry
        console.log('\n3. Testing simplified vehicle creation...');
        const newVehicle = {
            depot_number: `SIMPLE${Date.now()}`,
            license_plate: 'SIMPLE-123',
            vehicle_type_id: 1, // Voiture
            depositor: 'Police Municipale',
            brand: 'Toyota',
            entry_date: new Date().toISOString().split('T')[0],
            observations: 'Test du formulaire simplifié'
        };

        const createResult = await makeRequest('/api/vehicles', 'POST', newVehicle);
        console.log(`   ✅ Vehicle creation: ${createResult.status === 201 ? 'PASS' : 'FAIL'}`);
        
        if (createResult.status === 201) {
            const vehicleId = createResult.data.vehicle_id;
            console.log(`   🆕 Created vehicle with ID: ${vehicleId}`);

            // Test 4: Verify the created vehicle
            console.log('\n4. Testing vehicle retrieval...');
            const getVehicle = await makeRequest(`/api/vehicles/${vehicleId}`);
            console.log(`   ✅ Vehicle retrieval: ${getVehicle.status === 200 ? 'PASS' : 'FAIL'}`);
            
            if (getVehicle.status === 200) {
                const vehicle = getVehicle.data;
                console.log(`   📋 Vehicle details:`);
                console.log(`      - Dépôt: ${vehicle.depot_number}`);
                console.log(`      - Plaque: ${vehicle.license_plate}`);
                console.log(`      - Type: ${vehicle.vehicle_type_name}`);
                console.log(`      - Déposant: ${vehicle.depositor}`);
                console.log(`      - Marque: ${vehicle.brand}`);
                console.log(`      - Date: ${vehicle.entry_date}`);
                console.log(`      - Observations: ${vehicle.observations}`);
            }

            // Test 5: Clean up
            console.log('\n5. Cleaning up test vehicle...');
            const deleteResult = await makeRequest(`/api/vehicles/${vehicleId}`, 'DELETE');
            console.log(`   ✅ Vehicle deletion: ${deleteResult.status === 200 ? 'PASS' : 'FAIL'}`);
        }

        // Test 6: Test adding a new depositor
        console.log('\n6. Testing depositor management...');
        const newDepositor = {
            name: `Test Déposant ${Date.now()}`
        };

        const addDepositorResult = await makeRequest('/api/depositors', 'POST', newDepositor);
        console.log(`   ✅ Depositor creation: ${addDepositorResult.status === 201 ? 'PASS' : 'FAIL'}`);
        
        if (addDepositorResult.status === 201) {
            const depositorId = addDepositorResult.data.depositor_id;
            console.log(`   🆕 Created depositor with ID: ${depositorId}`);

            // Clean up test depositor
            const deleteDepositorResult = await makeRequest(`/api/depositors/${depositorId}`, 'DELETE');
            console.log(`   🗑️  Depositor cleanup: ${deleteDepositorResult.status === 200 ? 'PASS' : 'FAIL'}`);
        }

        console.log('\n🎉 All simplified form tests completed successfully!');
        console.log('\n📋 Summary:');
        console.log('   ✅ Formulaire simplifié avec 7 champs seulement');
        console.log('   ✅ Liste des déposants configurable');
        console.log('   ✅ Gestion des déposants dans les paramètres');
        console.log('   ✅ API complète pour les déposants');
        console.log('   ✅ Création de véhicules simplifiée');
        console.log('\n🚀 Le formulaire simplifié est prêt à être utilisé!');
        console.log('   📝 Champs requis: N° dépôt, Immatriculation, Type, Déposant, Date');
        console.log('   📝 Champs optionnels: Marque, Observations');
        console.log('   ⚙️  Gestion des déposants: Page Paramètres');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        process.exit(1);
    }
}

// Run tests if this file is executed directly
if (require.main === module) {
    testSimplifiedForm();
}

module.exports = { testSimplifiedForm };
