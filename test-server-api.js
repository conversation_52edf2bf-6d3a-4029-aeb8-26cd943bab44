// Test server API endpoints
const http = require('http');

function makeRequest(path, method = 'GET', data = null) {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: 'localhost',
            port: 3000,
            path: path,
            method: method,
            headers: {
                'Content-Type': 'application/json'
            }
        };

        const req = http.request(options, (res) => {
            let body = '';
            res.on('data', (chunk) => {
                body += chunk;
            });
            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(body);
                    resolve({ status: res.statusCode, data: jsonData });
                } catch (error) {
                    resolve({ status: res.statusCode, data: body });
                }
            });
        });

        req.on('error', (error) => {
            reject(error);
        });

        if (data) {
            req.write(JSON.stringify(data));
        }

        req.end();
    });
}

async function testServerAPI() {
    console.log('🔧 Testing Server API Endpoints...\n');

    try {
        // Test 1: Basic API test
        console.log('1. Testing basic API endpoint...');
        const testResult = await makeRequest('/api/test');
        console.log(`   📋 API test: ${testResult.status === 200 ? '✅ OK' : '❌ FAIL'} (${testResult.status})`);
        console.log(`   📋 Response:`, testResult.data);

        // Test 2: Test vehicle types (correct endpoint)
        console.log('\n2. Testing vehicle types endpoint...');
        const vehicleTypesResult = await makeRequest('/api/vehicles/types/list');
        console.log(`   📋 Vehicle types: ${vehicleTypesResult.status} - ${vehicleTypesResult.status === 200 ? '✅ OK' : vehicleTypesResult.status === 401 ? '🔒 AUTH REQUIRED' : '❌ FAIL'}`);
        if (vehicleTypesResult.status === 200) {
            console.log(`   📋 Found ${vehicleTypesResult.data.length} vehicle types`);
        }

        // Test 3: Test depositors (might require auth)
        console.log('\n3. Testing depositors endpoint...');
        const depositorsResult = await makeRequest('/api/depositors');
        console.log(`   📋 Depositors: ${depositorsResult.status} - ${depositorsResult.status === 200 ? '✅ OK' : depositorsResult.status === 401 ? '🔒 AUTH REQUIRED' : '❌ FAIL'}`);
        if (depositorsResult.status === 200) {
            console.log(`   📋 Found ${depositorsResult.data.length} depositors`);
        }

        // Test 4: Test vehicle creation (will require auth)
        console.log('\n4. Testing vehicle creation endpoint...');
        const testVehicle = {
            depot_number: `SERVER_TEST_${Date.now()}`,
            license_plate: 'SERVER-TEST',
            vehicle_type_id: 1,
            depositor: 'Police Municipale',
            entry_date: new Date().toISOString().split('T')[0]
        };

        const vehicleResult = await makeRequest('/api/vehicles', 'POST', testVehicle);
        console.log(`   📋 Vehicle creation: ${vehicleResult.status} - ${vehicleResult.status === 201 ? '✅ OK' : vehicleResult.status === 401 ? '🔒 AUTH REQUIRED' : '❌ FAIL'}`);

        if (vehicleResult.status === 201) {
            console.log(`   ✅ Vehicle created with ID: ${vehicleResult.data.vehicle_id}`);
        } else if (vehicleResult.status === 400) {
            console.log(`   ❌ Validation errors:`, vehicleResult.data);
        } else {
            console.log(`   📋 Response:`, vehicleResult.data);
        }

        console.log('\n🎉 Server API testing completed!');

        if (vehicleTypesResult.status === 401 || depositorsResult.status === 401 || vehicleResult.status === 401) {
            console.log('\n🔒 Authentication is required for most endpoints.');
            console.log('   This is expected behavior for a secure application.');
            console.log('   The frontend should handle authentication before making API calls.');
        }

    } catch (error) {
        console.error('❌ Server API test failed:', error.message);
    }
}

// Run the test
testServerAPI();
