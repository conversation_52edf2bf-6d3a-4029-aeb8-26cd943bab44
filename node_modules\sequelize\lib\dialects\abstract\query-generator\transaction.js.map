{"version": 3, "sources": ["../../../../src/dialects/abstract/query-generator/transaction.js"], "sourcesContent": ["'use strict';\n\nconst uuidv4 = require('uuid').v4;\n\nconst TransactionQueries = {\n  /**\n   * Returns a query that sets the transaction isolation level.\n   *\n   * @param  {string} value   The isolation level.\n   * @param  {object} options An object with options.\n   * @returns {string}         The generated sql query.\n   * @private\n   */\n  setIsolationLevelQuery(value, options) {\n    if (options.parent) {\n      return;\n    }\n\n    return `SET TRANSACTION ISOLATION LEVEL ${value};`;\n  },\n\n  generateTransactionId() {\n    return uuidv4();\n  },\n\n  /**\n   * Returns a query that starts a transaction.\n   *\n   * @param  {Transaction} transaction\n   * @returns {string}         The generated sql query.\n   * @private\n   */\n  startTransactionQuery(transaction) {\n    if (transaction.parent) {\n      // force quoting of savepoint identifiers for postgres\n      return `SAVEPOINT ${this.quoteIdentifier(transaction.name, true)};`;\n    }\n\n    return 'START TRANSACTION;';\n  },\n\n  deferConstraintsQuery() {},\n\n  setConstraintQuery() {},\n  setDeferredQuery() {},\n  setImmediateQuery() {},\n\n  /**\n   * Returns a query that commits a transaction.\n   *\n   * @param  {Transaction} transaction An object with options.\n   * @returns {string}         The generated sql query.\n   * @private\n   */\n  commitTransactionQuery(transaction) {\n    if (transaction.parent) {\n      return;\n    }\n\n    return 'COMMIT;';\n  },\n\n  /**\n   * Returns a query that rollbacks a transaction.\n   *\n   * @param  {Transaction} transaction\n   * @returns {string}         The generated sql query.\n   * @private\n   */\n  rollbackTransactionQuery(transaction) {\n    if (transaction.parent) {\n      // force quoting of savepoint identifiers for postgres\n      return `ROLLBACK TO SAVEPOINT ${this.quoteIdentifier(transaction.name, true)};`;\n    }\n\n    return 'ROLLBACK;';\n  }\n};\n\nmodule.exports = TransactionQueries;\n"], "mappings": ";AAEA,MAAM,SAAS,QAAQ,QAAQ;AAE/B,MAAM,qBAAqB;AAAA,EASzB,uBAAuB,OAAO,SAAS;AACrC,QAAI,QAAQ,QAAQ;AAClB;AAAA;AAGF,WAAO,mCAAmC;AAAA;AAAA,EAG5C,wBAAwB;AACtB,WAAO;AAAA;AAAA,EAUT,sBAAsB,aAAa;AACjC,QAAI,YAAY,QAAQ;AAEtB,aAAO,aAAa,KAAK,gBAAgB,YAAY,MAAM;AAAA;AAG7D,WAAO;AAAA;AAAA,EAGT,wBAAwB;AAAA;AAAA,EAExB,qBAAqB;AAAA;AAAA,EACrB,mBAAmB;AAAA;AAAA,EACnB,oBAAoB;AAAA;AAAA,EASpB,uBAAuB,aAAa;AAClC,QAAI,YAAY,QAAQ;AACtB;AAAA;AAGF,WAAO;AAAA;AAAA,EAUT,yBAAyB,aAAa;AACpC,QAAI,YAAY,QAAQ;AAEtB,aAAO,yBAAyB,KAAK,gBAAgB,YAAY,MAAM;AAAA;AAGzE,WAAO;AAAA;AAAA;AAIX,OAAO,UAAU;", "names": []}