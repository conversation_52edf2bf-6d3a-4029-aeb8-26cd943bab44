// Simple test for payment button functionality
console.log('🧪 Testing Payment Button Functionality...\n');

function testPaymentButton() {
    console.log('1. Checking if app is available...');
    if (!window.app) {
        console.log('❌ window.app is not available');
        return;
    }
    
    console.log('✅ window.app is available');
    
    console.log('\n2. Checking processPayment method...');
    if (typeof window.app.processPayment !== 'function') {
        console.log('❌ app.processPayment is not a function');
        return;
    }
    
    console.log('✅ app.processPayment is available');
    
    console.log('\n3. Looking for payment buttons...');
    const paymentButtons = document.querySelectorAll('button[onclick*="processPayment"]');
    console.log(`Found ${paymentButtons.length} payment buttons`);
    
    if (paymentButtons.length === 0) {
        console.log('❌ No payment buttons found. This could mean:');
        console.log('   - No vehicles are loaded');
        console.log('   - Current section has no vehicles with "impounded" status');
        console.log('   - User is not authenticated');
        
        console.log('\n🔧 Let\'s check the current section...');
        const activeNav = document.querySelector('.nav-item.active-nav-item');
        const currentSection = activeNav ? activeNav.dataset.target : 'unknown';
        console.log(`Current section: ${currentSection}`);
        
        console.log('\n🔧 Let\'s check if there are any vehicles in tables...');
        const vehicleRows = document.querySelectorAll('table tbody tr');
        console.log(`Total vehicle rows: ${vehicleRows.length}`);
        
        if (vehicleRows.length === 0) {
            console.log('❌ No vehicles loaded. Try:');
            console.log('   1. Make sure you are logged in');
            console.log('   2. Go to "Véhicules en fourrière" section');
            console.log('   3. Add some test vehicles first');
        }
        
        return;
    }
    
    console.log('✅ Payment buttons found!');
    
    console.log('\n4. Testing first payment button...');
    const firstButton = paymentButtons[0];
    const onclick = firstButton.getAttribute('onclick');
    console.log(`First button onclick: ${onclick}`);
    
    // Extract vehicle ID from onclick
    const match = onclick.match(/processPayment\((\d+)\)/);
    if (match) {
        const vehicleId = match[1];
        console.log(`Vehicle ID: ${vehicleId}`);
        
        console.log('\n5. Testing payment modal...');
        const modal = document.getElementById('paymentModal');
        if (!modal) {
            console.log('❌ Payment modal not found');
            return;
        }
        
        console.log('✅ Payment modal found');
        
        console.log('\n6. Simulating payment button click...');
        try {
            window.app.processPayment(parseInt(vehicleId));
            console.log('✅ Payment process initiated successfully');
            
            // Check if modal opened
            setTimeout(() => {
                const isModalVisible = !modal.classList.contains('hidden');
                console.log(`Modal visible: ${isModalVisible ? '✅ YES' : '❌ NO'}`);
                
                if (isModalVisible) {
                    console.log('\n🎉 SUCCESS! Payment modal opened correctly');
                    console.log('You should now see the payment modal with vehicle details');
                    
                    // Close the modal after test
                    setTimeout(() => {
                        modal.classList.add('hidden');
                        console.log('Test modal closed');
                    }, 3000);
                } else {
                    console.log('\n❌ FAIL! Modal did not open');
                    console.log('Check browser console for errors');
                }
            }, 500);
            
        } catch (error) {
            console.log(`❌ Error calling processPayment: ${error.message}`);
        }
    } else {
        console.log('❌ Could not extract vehicle ID from onclick');
    }
}

// Create a test button for manual testing
function createTestButton() {
    const testBtn = document.createElement('button');
    testBtn.innerHTML = '🧪 Test Payment System';
    testBtn.className = 'bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded m-2';
    testBtn.onclick = testPaymentButton;
    
    // Add to page header
    const header = document.querySelector('h1, h2, .text-2xl');
    if (header && header.parentNode) {
        header.parentNode.insertBefore(testBtn, header.nextSibling);
        console.log('🧪 Test button added to page');
    }
}

// Run tests
setTimeout(() => {
    console.log('🚀 Starting payment button test...');
    createTestButton();
    testPaymentButton();
}, 5000); // Wait 5 seconds for everything to load
