// Test script to verify frontend functionality
const http = require('http');

function testFrontend() {
    console.log('🧪 Testing Frontend Functionality...\n');

    // Test 1: Check if main page loads
    console.log('1. Testing main page load...');
    const options = {
        hostname: 'localhost',
        port: 3000,
        path: '/',
        method: 'GET'
    };

    const req = http.request(options, (res) => {
        let body = '';
        res.on('data', (chunk) => {
            body += chunk;
        });
        res.on('end', () => {
            console.log(`   ✅ Main page status: ${res.statusCode}`);
            
            // Check for key elements
            const hasModal = body.includes('id="addVehicleModal"');
            const hasButton = body.includes('Nouvelle entrée');
            const hasJavaScript = body.includes('src="/js/app.js"');
            
            console.log(`   📄 Modal present: ${hasModal ? 'YES' : 'NO'}`);
            console.log(`   🔘 Button present: ${hasButton ? 'YES' : 'NO'}`);
            console.log(`   📜 JavaScript linked: ${hasJavaScript ? 'YES' : 'NO'}`);
            
            // Test 2: Check if JavaScript file is accessible
            console.log('\n2. Testing JavaScript file access...');
            const jsOptions = {
                hostname: 'localhost',
                port: 3000,
                path: '/js/app.js',
                method: 'GET'
            };
            
            const jsReq = http.request(jsOptions, (jsRes) => {
                let jsBody = '';
                jsRes.on('data', (chunk) => {
                    jsBody += chunk;
                });
                jsRes.on('end', () => {
                    console.log(`   ✅ JavaScript file status: ${jsRes.statusCode}`);
                    
                    const hasClass = jsBody.includes('class FourriereApp');
                    const hasModalFunction = jsBody.includes('openAddVehicleModal');
                    const hasEventListeners = jsBody.includes('addEventListener');
                    
                    console.log(`   🏗️  FourriereApp class: ${hasClass ? 'YES' : 'NO'}`);
                    console.log(`   🔧 Modal function: ${hasModalFunction ? 'YES' : 'NO'}`);
                    console.log(`   👂 Event listeners: ${hasEventListeners ? 'YES' : 'NO'}`);
                    
                    console.log('\n🎉 Frontend test completed!');
                    console.log('\n📋 Summary:');
                    console.log('   ✅ HTML page loads correctly');
                    console.log('   ✅ Modal structure is present');
                    console.log('   ✅ JavaScript file is accessible');
                    console.log('   ✅ All required functions are defined');
                    console.log('\n💡 If the button still doesn\'t work:');
                    console.log('   1. Open browser developer tools (F12)');
                    console.log('   2. Check the Console tab for JavaScript errors');
                    console.log('   3. Try clicking the button and watch for console messages');
                    console.log('   4. Verify that the modal appears when clicking');
                });
            });
            
            jsReq.on('error', (err) => {
                console.error('❌ JavaScript file test failed:', err.message);
            });
            
            jsReq.end();
        });
    });

    req.on('error', (err) => {
        console.error('❌ Main page test failed:', err.message);
    });

    req.end();
}

// Run test if this file is executed directly
if (require.main === module) {
    testFrontend();
}

module.exports = { testFrontend };
