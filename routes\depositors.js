const express = require('express');
const router = express.Router();
const { body, validationResult } = require('express-validator');
const db = require('../config/database');

// Validation middleware
const validateDepositor = [
    body('name').notEmpty().withMessage('Nom du déposant requis').isLength({ min: 2, max: 200 }).withMessage('Le nom doit contenir entre 2 et 200 caractères')
];

// GET /api/depositors - Get all depositors
router.get('/', async (req, res) => {
    try {
        const { active_only = true } = req.query;
        
        let query = 'SELECT * FROM depositors';
        const params = [];
        
        if (active_only === 'true') {
            query += ' WHERE is_active = ?';
            params.push(true);
        }
        
        query += ' ORDER BY name ASC';
        
        const depositors = await db.executeQuery(query, params);
        
        res.json(depositors);
        
    } catch (error) {
        console.error('Error fetching depositors:', error);
        res.status(500).json({ error: 'Erreur lors de la récupération des déposants' });
    }
});

// GET /api/depositors/:id - Get single depositor
router.get('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        
        const [depositor] = await db.executeQuery('SELECT * FROM depositors WHERE id = ?', [id]);
        
        if (!depositor) {
            return res.status(404).json({ error: 'Déposant non trouvé' });
        }
        
        res.json(depositor);
        
    } catch (error) {
        console.error('Error fetching depositor:', error);
        res.status(500).json({ error: 'Erreur lors de la récupération du déposant' });
    }
});

// POST /api/depositors - Create new depositor
router.post('/', validateDepositor, async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }
        
        const { name } = req.body;
        
        const result = await db.executeQuery(
            'INSERT INTO depositors (name) VALUES (?)',
            [name.trim()]
        );
        
        res.status(201).json({
            message: 'Déposant créé avec succès',
            depositor_id: result.insertId
        });
        
    } catch (error) {
        console.error('Error creating depositor:', error);
        if (error.code === 'ER_DUP_ENTRY') {
            res.status(400).json({ error: 'Ce déposant existe déjà' });
        } else {
            res.status(500).json({ error: 'Erreur lors de la création du déposant' });
        }
    }
});

// PUT /api/depositors/:id - Update depositor
router.put('/:id', validateDepositor, async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }
        
        const { id } = req.params;
        const { name, is_active = true } = req.body;
        
        const result = await db.executeQuery(
            'UPDATE depositors SET name = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [name.trim(), is_active, id]
        );
        
        if (result.affectedRows === 0) {
            return res.status(404).json({ error: 'Déposant non trouvé' });
        }
        
        res.json({ message: 'Déposant mis à jour avec succès' });
        
    } catch (error) {
        console.error('Error updating depositor:', error);
        if (error.code === 'ER_DUP_ENTRY') {
            res.status(400).json({ error: 'Ce nom de déposant existe déjà' });
        } else {
            res.status(500).json({ error: 'Erreur lors de la mise à jour du déposant' });
        }
    }
});

// DELETE /api/depositors/:id - Delete depositor (soft delete)
router.delete('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        
        // Check if depositor is used in vehicles
        const [vehicleCount] = await db.executeQuery(
            'SELECT COUNT(*) as count FROM vehicles WHERE depositor = (SELECT name FROM depositors WHERE id = ?)',
            [id]
        );
        
        if (vehicleCount.count > 0) {
            // Soft delete - mark as inactive instead of deleting
            await db.executeQuery(
                'UPDATE depositors SET is_active = FALSE, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                [id]
            );
            
            res.json({ 
                message: 'Déposant désactivé avec succès (utilisé dans des véhicules existants)',
                soft_delete: true
            });
        } else {
            // Hard delete if not used
            const result = await db.executeQuery('DELETE FROM depositors WHERE id = ?', [id]);
            
            if (result.affectedRows === 0) {
                return res.status(404).json({ error: 'Déposant non trouvé' });
            }
            
            res.json({ 
                message: 'Déposant supprimé avec succès',
                soft_delete: false
            });
        }
        
    } catch (error) {
        console.error('Error deleting depositor:', error);
        res.status(500).json({ error: 'Erreur lors de la suppression du déposant' });
    }
});

// PUT /api/depositors/:id/activate - Activate depositor
router.put('/:id/activate', async (req, res) => {
    try {
        const { id } = req.params;
        
        const result = await db.executeQuery(
            'UPDATE depositors SET is_active = TRUE, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [id]
        );
        
        if (result.affectedRows === 0) {
            return res.status(404).json({ error: 'Déposant non trouvé' });
        }
        
        res.json({ message: 'Déposant activé avec succès' });
        
    } catch (error) {
        console.error('Error activating depositor:', error);
        res.status(500).json({ error: 'Erreur lors de l\'activation du déposant' });
    }
});

// GET /api/depositors/stats/usage - Get depositor usage statistics
router.get('/stats/usage', async (req, res) => {
    try {
        const stats = await db.executeQuery(`
            SELECT d.id, d.name, d.is_active,
                   COUNT(v.id) as vehicle_count,
                   COUNT(CASE WHEN v.status = 'impounded' THEN 1 END) as impounded_count,
                   COUNT(CASE WHEN v.status = 'released' THEN 1 END) as released_count
            FROM depositors d
            LEFT JOIN vehicles v ON d.name = v.depositor
            GROUP BY d.id, d.name, d.is_active
            ORDER BY vehicle_count DESC, d.name ASC
        `);
        
        res.json(stats);
        
    } catch (error) {
        console.error('Error fetching depositor stats:', error);
        res.status(500).json({ error: 'Erreur lors de la récupération des statistiques' });
    }
});

module.exports = router;
