<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Modal</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
        <h1 class="text-2xl font-bold mb-4">Test du Modal</h1>
        <button id="testButton" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
            <i class="fas fa-plus mr-2"></i> Nouvelle entrée (Test)
        </button>
        
        <div class="mt-4">
            <p id="status" class="text-gray-600">Cliquez sur le bouton pour tester</p>
        </div>
    </div>

    <!-- Test Modal -->
    <div id="testModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg w-full max-w-md p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Modal de Test</h3>
                <button id="closeButton" class="text-gray-400 hover:text-gray-500">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <p class="text-gray-600 mb-4">Le modal fonctionne correctement!</p>
            <button id="closeButton2" class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">
                Fermer
            </button>
        </div>
    </div>

    <script>
        // Test simple du modal
        document.addEventListener('DOMContentLoaded', function() {
            const testButton = document.getElementById('testButton');
            const modal = document.getElementById('testModal');
            const closeButton = document.getElementById('closeButton');
            const closeButton2 = document.getElementById('closeButton2');
            const status = document.getElementById('status');

            function openModal() {
                console.log('Opening modal...');
                modal.classList.remove('hidden');
                status.textContent = 'Modal ouvert!';
                status.className = 'text-green-600';
            }

            function closeModal() {
                console.log('Closing modal...');
                modal.classList.add('hidden');
                status.textContent = 'Modal fermé!';
                status.className = 'text-blue-600';
            }

            testButton.addEventListener('click', openModal);
            closeButton.addEventListener('click', closeModal);
            closeButton2.addEventListener('click', closeModal);

            // Test si les fonctions sont disponibles
            window.testOpenModal = openModal;
            window.testCloseModal = closeModal;

            status.textContent = 'JavaScript chargé et prêt!';
            status.className = 'text-green-600';
        });
    </script>
</body>
</html>
