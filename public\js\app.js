// Fourrière Management System - Frontend JavaScript
class FourriereApp {
    constructor() {
        console.log('FourriereApp constructor called');
        this.apiBase = '/api';
        this.currentPage = 1;
        this.currentFilters = {};
        this.charts = {};
        this.init();
    }

    async init() {
        this.setupEventListeners();
        await this.loadInitialData();
        this.setupCharts();
    }

    setupEventListeners() {
        console.log('Setting up event listeners...');

        // Navigation
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', (e) => this.handleNavigation(e));
        });

        // Sidebar toggle
        const sidebarToggle = document.getElementById('sidebar-toggle');
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', this.toggleSidebar);
        }

        // Forms
        const addVehicleForm = document.getElementById('addVehicleForm');
        if (addVehicleForm) {
            addVehicleForm.addEventListener('submit', (e) => this.handleAddVehicle(e));
        }

        const paymentForm = document.getElementById('paymentForm');
        if (paymentForm) {
            paymentForm.addEventListener('submit', (e) => this.handlePayment(e));
        }

        // Filters
        const applyFilters = document.getElementById('apply-filters');
        if (applyFilters) {
            applyFilters.addEventListener('click', () => this.applyFilters());
        }

        const vehicleSearch = document.getElementById('vehicle-search');
        if (vehicleSearch) {
            vehicleSearch.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') this.applyFilters();
            });
        }

        // Global search
        const globalSearch = document.getElementById('global-search');
        if (globalSearch) {
            globalSearch.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') this.performGlobalSearch(e.target.value);
            });
        }

        // Settings
        const saveSettings = document.getElementById('save-settings');
        if (saveSettings) {
            saveSettings.addEventListener('click', () => this.saveSettings());
        }

        // Depositors management
        const addDepositor = document.getElementById('add-depositor');
        if (addDepositor) {
            addDepositor.addEventListener('click', () => this.addDepositor());
        }

        const newDepositorInput = document.getElementById('new-depositor');
        if (newDepositorInput) {
            newDepositorInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') this.addDepositor();
            });
        }

        // Stats period change
        const statsPeriod = document.getElementById('stats-period');
        if (statsPeriod) {
            statsPeriod.addEventListener('change', (e) => {
                this.loadMonthlyStats(e.target.value);
            });
        }

        console.log('Event listeners setup complete');
    }

    async loadInitialData() {
        try {
            await Promise.all([
                this.loadDashboardStats(),
                this.loadVehicleTypes(),
                this.loadDepositors(),
                this.loadRecentVehicles(),
                this.loadMonthlyStats(),
                this.loadVehicleTypesDistribution()
            ]);
        } catch (error) {
            console.error('Error loading initial data:', error);
            this.showError('Erreur lors du chargement des données');
        }
    }

    async apiCall(endpoint, options = {}) {
        try {
            const response = await fetch(`${this.apiBase}${endpoint}`, {
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('API call failed:', error);
            throw error;
        }
    }

    async loadDashboardStats() {
        try {
            const stats = await this.apiCall('/dashboard/stats');

            const statImpounded = document.getElementById('stat-impounded');
            const statPending = document.getElementById('stat-pending');
            const statReleased = document.getElementById('stat-released');
            const statOverdue = document.getElementById('stat-overdue');

            if (statImpounded) statImpounded.textContent = stats.impounded;
            if (statPending) statPending.textContent = `${stats.pending_release} ${stats.unpaid > 0 ? `(${stats.unpaid} impayés)` : ''}`;
            if (statReleased) statReleased.textContent = stats.released_this_month;
            if (statOverdue) statOverdue.textContent = stats.overdue;

            // Update notification badge
            const totalAlerts = stats.overdue + stats.unpaid;
            const badge = document.getElementById('notification-badge');
            if (badge) {
                if (totalAlerts > 0) {
                    badge.classList.remove('hidden');
                } else {
                    badge.classList.add('hidden');
                }
            }
        } catch (error) {
            console.error('Error loading dashboard stats:', error);
        }
    }

    async loadVehicleTypes() {
        try {
            console.log('Loading vehicle types...');
            const types = await this.apiCall('/vehicles/types/list');
            console.log('Vehicle types loaded:', types);

            // Populate vehicle type selects
            const selects = document.querySelectorAll('select[name="vehicle_type_id"], #vehicle-type-filter');
            console.log('Found vehicle type selects:', selects.length);

            selects.forEach(select => {
                // Clear existing options (except first one for filters)
                const isFilter = select.id === 'vehicle-type-filter';
                select.innerHTML = isFilter ? '<option value="">Tous les types</option>' : '<option value="">Sélectionner...</option>';

                types.forEach(type => {
                    const option = document.createElement('option');
                    option.value = type.id;
                    option.textContent = type.name;
                    select.appendChild(option);
                });

                console.log(`Populated select with ${types.length} options`);
            });
        } catch (error) {
            console.error('Error loading vehicle types:', error);
        }
    }

    async loadDepositors() {
        try {
            console.log('Loading depositors...');
            const depositors = await this.apiCall('/depositors?active_only=true');
            console.log('Depositors loaded:', depositors);

            // Populate depositor selects
            const selects = document.querySelectorAll('select[name="depositor"]');
            console.log('Found depositor selects:', selects.length);

            selects.forEach(select => {
                select.innerHTML = '<option value="">Sélectionner...</option>';

                depositors.forEach(depositor => {
                    const option = document.createElement('option');
                    option.value = depositor.name;
                    option.textContent = depositor.name;
                    select.appendChild(option);
                });

                console.log(`Populated depositor select with ${depositors.length} options`);
            });

            // Update depositors list in settings if we're on that page
            this.updateDepositorsList(depositors);
        } catch (error) {
            console.error('Error loading depositors:', error);
        }
    }

    updateDepositorsList(depositors) {
        const container = document.getElementById('depositors-list');
        if (!container) return;

        if (depositors.length === 0) {
            container.innerHTML = '<div class="p-4 text-gray-500 text-center">Aucun déposant configuré</div>';
            return;
        }

        container.innerHTML = depositors.map(depositor => `
            <div class="flex items-center justify-between p-3">
                <div class="flex items-center">
                    <span class="text-sm font-medium text-gray-900">${depositor.name}</span>
                    ${!depositor.is_active ? '<span class="ml-2 px-2 py-1 text-xs bg-red-100 text-red-800 rounded">Inactif</span>' : ''}
                </div>
                <div class="flex space-x-2">
                    ${!depositor.is_active ? `
                        <button onclick="app.activateDepositor(${depositor.id})" class="text-green-600 hover:text-green-900" title="Activer">
                            <i class="fas fa-check"></i>
                        </button>
                    ` : ''}
                    <button onclick="app.editDepositor(${depositor.id}, '${depositor.name}')" class="text-blue-600 hover:text-blue-900" title="Modifier">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button onclick="app.deleteDepositor(${depositor.id})" class="text-red-600 hover:text-red-900" title="Supprimer">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    openAddVehicleModal() {
        console.log('Opening add vehicle modal from class method...');
        const modal = document.getElementById('addVehicleModal');
        if (modal) {
            modal.classList.remove('hidden');
            // Set default date to today
            const dateInput = document.querySelector('input[name="entry_date"]');
            if (dateInput) {
                dateInput.value = new Date().toISOString().split('T')[0];
            }

            // Reload data to ensure dropdowns are populated
            this.loadVehicleTypes();
            this.loadDepositors();
        } else {
            console.error('Modal not found!');
        }
    }

    closeModal(modalId) {
        console.log('Closing modal from class method:', modalId);
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('hidden');
            // Reset form if it exists
            const form = modal.querySelector('form');
            if (form) {
                form.reset();
            }
        }
    }

    showError(message) {
        // Create and show error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message fixed top-4 right-4 z-50';
        errorDiv.innerHTML = `
            <div class="flex items-center bg-red-100 text-red-800 p-4 rounded-md">
                <i class="fas fa-exclamation-circle mr-2"></i>
                ${message}
                <button onclick="this.parentElement.parentElement.remove()" class="ml-4">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        document.body.appendChild(errorDiv);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (errorDiv.parentElement) {
                errorDiv.remove();
            }
        }, 5000);
    }

    showSuccess(message) {
        // Create and show success message
        const successDiv = document.createElement('div');
        successDiv.className = 'success-message fixed top-4 right-4 z-50';
        successDiv.innerHTML = `
            <div class="flex items-center bg-green-100 text-green-800 p-4 rounded-md">
                <i class="fas fa-check-circle mr-2"></i>
                ${message}
                <button onclick="this.parentElement.parentElement.remove()" class="ml-4">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        document.body.appendChild(successDiv);

        // Auto remove after 3 seconds
        setTimeout(() => {
            if (successDiv.parentElement) {
                successDiv.remove();
            }
        }, 3000);
    }

    // Placeholder methods for missing functionality
    async loadRecentVehicles() {
        try {
            console.log('Loading recent vehicles...');
            const vehicles = await this.apiCall('/vehicles?limit=5&sort=entry_date&order=desc');
            const tbody = document.getElementById('recent-vehicles-table');

            if (!tbody) return;

            if (vehicles.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" class="px-6 py-4 text-center text-gray-500">Aucun véhicule récent</td></tr>';
                return;
            }

            tbody.innerHTML = vehicles.map(vehicle => `
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${vehicle.depot_number}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="w-10 h-10 photo-placeholder">
                                <i class="fas fa-car"></i>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-900">${vehicle.license_plate}</div>
                                <div class="text-xs text-gray-500">${vehicle.brand || ''} ${vehicle.model || ''}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${vehicle.vehicle_type_name || 'N/A'}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${new Date(vehicle.entry_date).toLocaleDateString('fr-FR')}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="status-badge ${this.getStatusClass(vehicle.status)}">${this.getStatusText(vehicle.status)}</span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="app.viewVehicle(${vehicle.id})">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="text-green-600 hover:text-green-900" onclick="app.editVehicle(${vehicle.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                    </td>
                </tr>
            `).join('');

        } catch (error) {
            console.error('Error loading recent vehicles:', error);
        }
    }

    async loadMonthlyStats() {
        // Implementation for loading monthly stats
        console.log('Loading monthly stats...');
    }

    async loadVehicleTypesDistribution() {
        // Implementation for loading vehicle types distribution
        console.log('Loading vehicle types distribution...');
    }

    setupCharts() {
        // Implementation for setting up charts
        console.log('Setting up charts...');
    }

    handleNavigation(e) {
        e.preventDefault();

        const navItems = document.querySelectorAll('.nav-item');
        const sections = document.querySelectorAll('.page-section');
        const target = e.currentTarget.getAttribute('data-target');

        console.log('Navigating to:', target);

        // Remove active class from all nav items
        navItems.forEach(item => item.classList.remove('active-nav-item'));

        // Add active class to clicked item
        e.currentTarget.classList.add('active-nav-item');

        // Hide all sections
        sections.forEach(section => section.classList.add('hidden'));

        // Show target section
        const targetSection = document.getElementById(target);
        if (targetSection) {
            targetSection.classList.remove('hidden');

            // Update page title
            const itemName = e.currentTarget.querySelector('span').textContent;
            const pageTitle = document.getElementById('page-title');
            if (pageTitle) {
                pageTitle.textContent = itemName;
            }

            // Load section-specific data
            this.loadSectionData(target);
        }
    }

    async loadSectionData(section) {
        console.log('Loading data for section:', section);

        switch (section) {
            case 'impounded':
                await this.loadVehicles({ status: 'impounded' });
                break;
            case 'pending':
                await this.loadVehicles({ status: 'pending_release' });
                break;
            case 'released':
                await this.loadVehicles({ status: 'released' });
                break;
            case 'overdue':
                await this.loadOverdueVehicles();
                break;
            case 'dashboard':
                await this.loadDashboardStats();
                await this.loadRecentVehicles();
                await this.loadMonthlyStats();
                await this.loadVehicleTypesDistribution();
                break;
            case 'settings':
                await this.loadDepositors();
                break;
        }
    }

    async loadVehicles(filters = {}) {
        try {
            console.log('Loading vehicles with filters:', filters);

            // Build query string
            const queryParams = new URLSearchParams();
            Object.keys(filters).forEach(key => {
                if (filters[key]) {
                    queryParams.append(key, filters[key]);
                }
            });

            const vehicles = await this.apiCall(`/vehicles?${queryParams.toString()}`);

            // Determine which table to populate based on current section
            const activeSection = document.querySelector('.nav-item.active-nav-item')?.getAttribute('data-target');
            let tableId = 'vehicles-table';

            if (activeSection === 'pending') {
                tableId = 'pending-vehicles-table';
            } else if (activeSection === 'released') {
                tableId = 'released-vehicles-table';
            } else if (activeSection === 'overdue') {
                tableId = 'overdue-vehicles-table';
            }

            const tbody = document.getElementById(tableId);
            if (!tbody) {
                console.warn(`Table ${tableId} not found`);
                return;
            }

            if (vehicles.length === 0) {
                tbody.innerHTML = '<tr><td colspan="9" class="px-6 py-4 text-center text-gray-500">Aucun véhicule trouvé</td></tr>';
                return;
            }

            tbody.innerHTML = vehicles.map(vehicle => this.renderVehicleRow(vehicle, activeSection)).join('');

        } catch (error) {
            console.error('Error loading vehicles:', error);
            this.showError('Erreur lors du chargement des véhicules');
        }
    }

    renderVehicleRow(vehicle, section) {
        const entryDate = new Date(vehicle.entry_date).toLocaleDateString('fr-FR');
        const daysInPound = Math.floor((new Date() - new Date(vehicle.entry_date)) / (1000 * 60 * 60 * 24));
        const totalCost = (daysInPound * (vehicle.storage_cost_per_day || 25)).toFixed(2);

        if (section === 'pending') {
            return `
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${vehicle.depot_number}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900">${vehicle.license_plate}</div>
                        <div class="text-xs text-gray-500">${vehicle.brand || ''} ${vehicle.model || ''}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${entryDate}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${daysInPound}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-red-600">${totalCost} DH</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <button class="text-green-600 hover:text-green-900 mr-3" onclick="app.processPayment(${vehicle.id})">
                            <i class="fas fa-credit-card"></i> Payer
                        </button>
                        <button class="text-blue-600 hover:text-blue-900" onclick="app.viewVehicle(${vehicle.id})">
                            <i class="fas fa-eye"></i>
                        </button>
                    </td>
                </tr>
            `;
        } else if (section === 'released') {
            const releaseDate = vehicle.release_date ? new Date(vehicle.release_date).toLocaleDateString('fr-FR') : '-';
            return `
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${vehicle.depot_number}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900">${vehicle.license_plate}</div>
                        <div class="text-xs text-gray-500">${vehicle.brand || ''} ${vehicle.model || ''}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${entryDate}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${releaseDate}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${daysInPound} jours</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600">${vehicle.amount_paid || totalCost} DH</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <button class="text-blue-600 hover:text-blue-900" onclick="app.viewVehicle(${vehicle.id})">
                            <i class="fas fa-eye"></i>
                        </button>
                    </td>
                </tr>
            `;
        } else {
            // Default view for impounded and overdue
            return `
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${vehicle.depot_number}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${vehicle.vehicle_type_name || 'N/A'}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${vehicle.license_plate}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${vehicle.brand || ''} ${vehicle.model || ''}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${entryDate}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${daysInPound}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${totalCost} DH</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="status-badge ${this.getStatusClass(vehicle.status)}">${this.getStatusText(vehicle.status)}</span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="app.viewVehicle(${vehicle.id})">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="text-green-600 hover:text-green-900" onclick="app.editVehicle(${vehicle.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                    </td>
                </tr>
            `;
        }
    }

    async loadOverdueVehicles() {
        try {
            const vehicles = await this.apiCall('/vehicles?overdue=true');
            const tbody = document.getElementById('overdue-vehicles-table');

            if (!tbody) return;

            if (vehicles.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" class="px-6 py-4 text-center text-gray-500">Aucun véhicule en retard</td></tr>';
                return;
            }

            tbody.innerHTML = vehicles.map(vehicle => {
                const entryDate = new Date(vehicle.entry_date).toLocaleDateString('fr-FR');
                const daysInPound = Math.floor((new Date() - new Date(vehicle.entry_date)) / (1000 * 60 * 60 * 24));
                const totalCost = (daysInPound * (vehicle.storage_cost_per_day || 25)).toFixed(2);

                return `
                    <tr class="bg-red-50">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${vehicle.depot_number}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">${vehicle.license_plate}</div>
                            <div class="text-xs text-gray-500">${vehicle.brand || ''} ${vehicle.model || ''}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${entryDate}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-red-600">${daysInPound}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-red-600">${totalCost} DH</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="app.viewVehicle(${vehicle.id})">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="text-red-600 hover:text-red-900" onclick="app.handleOverdueVehicle(${vehicle.id})">
                                <i class="fas fa-exclamation-triangle"></i> Action
                            </button>
                        </td>
                    </tr>
                `;
            }).join('');

        } catch (error) {
            console.error('Error loading overdue vehicles:', error);
            this.showError('Erreur lors du chargement des véhicules en retard');
        }
    }

    toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        const toggle = document.getElementById('sidebar-toggle');
        const icon = toggle.querySelector('i');

        sidebar.classList.toggle('sidebar-minimized');

        if (sidebar.classList.contains('sidebar-minimized')) {
            icon.classList.remove('fa-chevron-left');
            icon.classList.add('fa-chevron-right');
        } else {
            icon.classList.remove('fa-chevron-right');
            icon.classList.add('fa-chevron-left');
        }
    }

    // Utility methods
    getStatusClass(status) {
        const classes = {
            'impounded': 'bg-blue-100 text-blue-800',
            'pending_release': 'bg-yellow-100 text-yellow-800',
            'released': 'bg-green-100 text-green-800',
            'overdue': 'bg-red-100 text-red-800'
        };
        return classes[status] || 'bg-gray-100 text-gray-800';
    }

    getStatusText(status) {
        const texts = {
            'impounded': 'En fourrière',
            'pending_release': 'En attente',
            'released': 'Sorti',
            'overdue': 'En retard'
        };
        return texts[status] || status;
    }

    // Placeholder methods for vehicle actions
    viewVehicle(id) {
        console.log('Viewing vehicle:', id);
        this.showSuccess(`Affichage du véhicule ${id}`);
    }

    editVehicle(id) {
        console.log('Editing vehicle:', id);
        this.showSuccess(`Modification du véhicule ${id}`);
    }

    processPayment(id) {
        console.log('Processing payment for vehicle:', id);
        this.showSuccess(`Traitement du paiement pour le véhicule ${id}`);
    }

    handleOverdueVehicle(id) {
        console.log('Handling overdue vehicle:', id);
        this.showSuccess(`Action sur le véhicule en retard ${id}`);
    }

    async handleAddVehicle(e) {
        e.preventDefault();

        try {
            const formData = new FormData(e.target);
            const vehicleData = Object.fromEntries(formData.entries());

            console.log('Submitting vehicle data:', vehicleData);

            await this.apiCall('/vehicles', {
                method: 'POST',
                body: JSON.stringify(vehicleData)
            });

            this.showSuccess('Véhicule enregistré avec succès');
            this.closeModal('addVehicleModal');

            // Refresh dashboard stats
            await this.loadDashboardStats();

        } catch (error) {
            console.error('Error adding vehicle:', error);
            this.showError('Erreur lors de l\'enregistrement du véhicule');
        }
    }

    async handlePayment(e) {
        // Implementation for payment handling
        console.log('Handling payment...');
    }

    applyFilters() {
        // Implementation for applying filters
        console.log('Applying filters...');
    }

    performGlobalSearch(query) {
        // Implementation for global search
        console.log('Performing global search:', query);
    }

    saveSettings() {
        // Implementation for saving settings
        console.log('Saving settings...');
    }

    async addDepositor() {
        const input = document.getElementById('new-depositor');
        const name = input.value.trim();

        if (!name) {
            this.showError('Veuillez saisir un nom de déposant');
            return;
        }

        try {
            await this.apiCall('/depositors', {
                method: 'POST',
                body: JSON.stringify({ name })
            });

            this.showSuccess('Déposant ajouté avec succès');
            input.value = '';

            // Reload depositors
            await this.loadDepositors();

        } catch (error) {
            console.error('Error adding depositor:', error);
            this.showError('Erreur lors de l\'ajout du déposant');
        }
    }

    async deleteDepositor(id) {
        if (!confirm('Êtes-vous sûr de vouloir supprimer ce déposant ?')) {
            return;
        }

        try {
            const result = await this.apiCall(`/depositors/${id}`, { method: 'DELETE' });

            if (result.soft_delete) {
                this.showSuccess('Déposant désactivé (utilisé dans des véhicules existants)');
            } else {
                this.showSuccess('Déposant supprimé avec succès');
            }

            // Reload depositors
            await this.loadDepositors();

        } catch (error) {
            console.error('Error deleting depositor:', error);
            this.showError('Erreur lors de la suppression du déposant');
        }
    }

    async activateDepositor(id) {
        try {
            await this.apiCall(`/depositors/${id}/activate`, { method: 'PUT' });
            this.showSuccess('Déposant activé avec succès');

            // Reload depositors
            await this.loadDepositors();

        } catch (error) {
            console.error('Error activating depositor:', error);
            this.showError('Erreur lors de l\'activation du déposant');
        }
    }

    editDepositor(id, currentName) {
        const newName = prompt('Nouveau nom du déposant:', currentName);
        if (newName && newName.trim() !== currentName) {
            this.updateDepositor(id, newName.trim());
        }
    }

    async updateDepositor(id, name) {
        try {
            await this.apiCall(`/depositors/${id}`, {
                method: 'PUT',
                body: JSON.stringify({ name })
            });

            this.showSuccess('Déposant mis à jour avec succès');

            // Reload depositors
            await this.loadDepositors();

        } catch (error) {
            console.error('Error updating depositor:', error);
            this.showError('Erreur lors de la mise à jour du déposant');
        }
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing FourriereApp...');
    window.app = new FourriereApp();
    console.log('FourriereApp initialized:', window.app);

    // Test if modal functions are available
    console.log('openAddVehicleModal function:', typeof window.openAddVehicleModal);
    console.log('closeModal function:', typeof window.closeModal);
});

// Modal functions (global scope for onclick handlers)
function openAddVehicleModal() {
    console.log('Global openAddVehicleModal called');
    if (window.app && typeof window.app.openAddVehicleModal === 'function') {
        window.app.openAddVehicleModal();
    } else {
        console.log('App not ready, using fallback modal logic');
        const modal = document.getElementById('addVehicleModal');
        if (modal) {
            modal.classList.remove('hidden');
            // Set default date to today
            const dateInput = document.querySelector('input[name="entry_date"]');
            if (dateInput) {
                dateInput.value = new Date().toISOString().split('T')[0];
            }
        } else {
            console.error('Modal not found!');
        }
    }
}

function closeModal(modalId) {
    console.log('Global closeModal called:', modalId);
    if (window.app && typeof window.app.closeModal === 'function') {
        window.app.closeModal(modalId);
    } else {
        console.log('App not ready, using fallback modal logic');
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('hidden');
            // Reset form if it exists
            const form = modal.querySelector('form');
            if (form) {
                form.reset();
            }
        }
    }
}

// Global functions for onclick handlers
function generateReport(type) {
    if (window.app) {
        console.log('Generating report:', type);
    }
}

// Make functions available globally for onclick handlers
window.openAddVehicleModal = openAddVehicleModal;
window.closeModal = closeModal;
window.generateReport = generateReport;
