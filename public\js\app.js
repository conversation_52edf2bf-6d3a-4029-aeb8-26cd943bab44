// Fourrière Management System - Frontend JavaScript
class FourriereApp {
    constructor() {
        console.log('FourriereApp constructor called');
        this.apiBase = '/api';
        this.currentPage = 1;
        this.currentFilters = {};
        this.charts = {};
        this.init();
    }

    async init() {
        this.setupEventListeners();
        await this.loadInitialData();
        this.setupCharts();
    }

    setupEventListeners() {
        console.log('Setting up event listeners...');

        // Navigation
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', (e) => this.handleNavigation(e));
        });

        // Sidebar toggle
        const sidebarToggle = document.getElementById('sidebar-toggle');
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', this.toggleSidebar);
        }

        // Add Vehicle buttons (using class selector to catch all instances)
        document.querySelectorAll('button[onclick="openAddVehicleModal()"]').forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                this.openAddVehicleModal();
            });
        });

        // Forms
        const addVehicleForm = document.getElementById('addVehicleForm');
        if (addVehicleForm) {
            addVehicleForm.addEventListener('submit', (e) => this.handleAddVehicle(e));
        }

        const paymentForm = document.getElementById('paymentForm');
        if (paymentForm) {
            paymentForm.addEventListener('submit', (e) => this.handlePayment(e));
        }

        // Filters
        const applyFilters = document.getElementById('apply-filters');
        if (applyFilters) {
            applyFilters.addEventListener('click', () => this.applyFilters());
        }

        const vehicleSearch = document.getElementById('vehicle-search');
        if (vehicleSearch) {
            vehicleSearch.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') this.applyFilters();
            });
        }

        // Global search
        const globalSearch = document.getElementById('global-search');
        if (globalSearch) {
            globalSearch.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') this.performGlobalSearch(e.target.value);
            });
        }

        // Settings
        const saveSettings = document.getElementById('save-settings');
        if (saveSettings) {
            saveSettings.addEventListener('click', () => this.saveSettings());
        }

        // Depositors management
        const addDepositor = document.getElementById('add-depositor');
        if (addDepositor) {
            addDepositor.addEventListener('click', () => this.addDepositor());
        }

        const newDepositorInput = document.getElementById('new-depositor');
        if (newDepositorInput) {
            newDepositorInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') this.addDepositor();
            });
        }

        // Stats period change
        const statsPeriod = document.getElementById('stats-period');
        if (statsPeriod) {
            statsPeriod.addEventListener('change', (e) => {
                this.loadMonthlyStats(e.target.value);
            });
        }

        console.log('Event listeners setup complete');
    }

    async loadInitialData() {
        try {
            await Promise.all([
                this.loadDashboardStats(),
                this.loadVehicleTypes(),
                this.loadDepositors(),
                this.loadRecentVehicles(),
                this.loadMonthlyStats(),
                this.loadVehicleTypesDistribution()
            ]);
        } catch (error) {
            console.error('Error loading initial data:', error);
            this.showError('Erreur lors du chargement des données');
        }
    }

    async apiCall(endpoint, options = {}) {
        try {
            const response = await fetch(`${this.apiBase}${endpoint}`, {
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('API call failed:', error);
            throw error;
        }
    }

    async loadDashboardStats() {
        try {
            const stats = await this.apiCall('/dashboard/stats');

            document.getElementById('stat-impounded').textContent = stats.impounded;
            document.getElementById('stat-pending').textContent = `${stats.pending_release} ${stats.unpaid > 0 ? `(${stats.unpaid} impayés)` : ''}`;
            document.getElementById('stat-released').textContent = stats.released_this_month;
            document.getElementById('stat-overdue').textContent = stats.overdue;

            // Update notification badge
            const totalAlerts = stats.overdue + stats.unpaid;
            const badge = document.getElementById('notification-badge');
            if (totalAlerts > 0) {
                badge.classList.remove('hidden');
            } else {
                badge.classList.add('hidden');
            }
        } catch (error) {
            console.error('Error loading dashboard stats:', error);
        }
    }

    async loadVehicleTypes() {
        try {
            const types = await this.apiCall('/vehicles/types/list');

            // Populate vehicle type selects
            const selects = document.querySelectorAll('select[name="vehicle_type_id"], #vehicle-type-filter');
            selects.forEach(select => {
                // Clear existing options (except first one for filters)
                const isFilter = select.id === 'vehicle-type-filter';
                select.innerHTML = isFilter ? '<option value="">Tous les types</option>' : '<option value="">Sélectionner...</option>';

                types.forEach(type => {
                    const option = document.createElement('option');
                    option.value = type.id;
                    option.textContent = type.name;
                    select.appendChild(option);
                });
            });
        } catch (error) {
            console.error('Error loading vehicle types:', error);
        }
    }

    async loadDepositors() {
        try {
            const depositors = await this.apiCall('/depositors?active_only=true');

            // Populate depositor selects
            const selects = document.querySelectorAll('select[name="depositor"]');
            selects.forEach(select => {
                select.innerHTML = '<option value="">Sélectionner...</option>';

                depositors.forEach(depositor => {
                    const option = document.createElement('option');
                    option.value = depositor.name;
                    option.textContent = depositor.name;
                    select.appendChild(option);
                });
            });

            // Update depositors list in settings if we're on that page
            this.updateDepositorsList(depositors);
        } catch (error) {
            console.error('Error loading depositors:', error);
        }
    }

    updateDepositorsList(depositors) {
        const container = document.getElementById('depositors-list');
        if (!container) return;

        if (depositors.length === 0) {
            container.innerHTML = '<div class="p-4 text-gray-500 text-center">Aucun déposant configuré</div>';
            return;
        }

        container.innerHTML = depositors.map(depositor => `
            <div class="flex items-center justify-between p-3">
                <div class="flex items-center">
                    <span class="text-sm font-medium text-gray-900">${depositor.name}</span>
                    ${!depositor.is_active ? '<span class="ml-2 px-2 py-1 text-xs bg-red-100 text-red-800 rounded">Inactif</span>' : ''}
                </div>
                <div class="flex space-x-2">
                    ${!depositor.is_active ? `
                        <button onclick="app.activateDepositor(${depositor.id})" class="text-green-600 hover:text-green-900" title="Activer">
                            <i class="fas fa-check"></i>
                        </button>
                    ` : ''}
                    <button onclick="app.editDepositor(${depositor.id}, '${depositor.name}')" class="text-blue-600 hover:text-blue-900" title="Modifier">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button onclick="app.deleteDepositor(${depositor.id})" class="text-red-600 hover:text-red-900" title="Supprimer">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    async loadRecentVehicles() {
        try {
            const vehicles = await this.apiCall('/dashboard/recent-vehicles?limit=5');
            const tbody = document.getElementById('recent-vehicles-table');

            tbody.innerHTML = vehicles.map(vehicle => `
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${vehicle.depot_number}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="w-10 h-10 photo-placeholder">
                                <i class="fas fa-car"></i>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-900">${vehicle.license_plate}</div>
                                <div class="text-xs text-gray-500">${vehicle.brand} ${vehicle.model}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${vehicle.vehicle_type_name}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${this.formatDate(vehicle.entry_date)}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="status-badge ${this.getStatusClass(vehicle.status)}">${this.getStatusText(vehicle.status)}</span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="app.viewVehicle(${vehicle.id})">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="text-green-600 hover:text-green-900" onclick="app.editVehicle(${vehicle.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
        } catch (error) {
            console.error('Error loading recent vehicles:', error);
        }
    }

    async loadMonthlyStats(days = 30) {
        try {
            const stats = await this.apiCall(`/dashboard/monthly-stats?days=${days}`);

            if (this.charts.statsChart) {
                this.charts.statsChart.destroy();
            }

            const ctx = document.getElementById('statsChart').getContext('2d');
            this.charts.statsChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: stats.map(stat => stat.day),
                    datasets: [
                        {
                            label: 'Véhicules entrants',
                            data: stats.map(stat => stat.entries),
                            borderColor: '#3B82F6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            tension: 0.4,
                            fill: true
                        },
                        {
                            label: 'Véhicules sortants',
                            data: stats.map(stat => stat.releases),
                            borderColor: '#10B981',
                            backgroundColor: 'rgba(16, 185, 129, 0.1)',
                            tension: 0.4,
                            fill: true
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        } catch (error) {
            console.error('Error loading monthly stats:', error);
        }
    }

    async loadVehicleTypesDistribution() {
        try {
            const types = await this.apiCall('/dashboard/vehicle-types');

            if (this.charts.vehicleTypesChart) {
                this.charts.vehicleTypesChart.destroy();
            }

            const ctx = document.getElementById('vehicleTypesChart').getContext('2d');
            const colors = ['#3B82F6', '#10B981', '#8B5CF6', '#F59E0B', '#EF4444', '#6B7280'];

            this.charts.vehicleTypesChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: types.map(type => type.name),
                    datasets: [{
                        data: types.map(type => type.count),
                        backgroundColor: colors.slice(0, types.length),
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    cutout: '65%'
                }
            });

            // Update legend
            const legend = document.getElementById('vehicle-types-legend');
            legend.innerHTML = types.map((type, index) => `
                <div class="flex items-center">
                    <div class="w-3 h-3 rounded-full mr-2" style="background-color: ${colors[index]}"></div>
                    <span class="text-sm text-gray-600">${type.name} (${type.percentage}%)</span>
                </div>
            `).join('');
        } catch (error) {
            console.error('Error loading vehicle types distribution:', error);
        }
    }

    setupCharts() {
        // Charts are set up in their respective load functions
    }

    handleNavigation(e) {
        e.preventDefault();

        const navItems = document.querySelectorAll('.nav-item');
        const sections = document.querySelectorAll('.page-section');
        const target = e.currentTarget.getAttribute('data-target');

        // Remove active class from all nav items
        navItems.forEach(item => item.classList.remove('active-nav-item'));

        // Add active class to clicked item
        e.currentTarget.classList.add('active-nav-item');

        // Hide all sections
        sections.forEach(section => section.classList.add('hidden'));

        // Show target section
        const targetSection = document.getElementById(target);
        if (targetSection) {
            targetSection.classList.remove('hidden');

            // Update page title
            const itemName = e.currentTarget.querySelector('span').textContent;
            document.getElementById('page-title').textContent = itemName;

            // Load section-specific data
            this.loadSectionData(target);
        }
    }

    async loadSectionData(section) {
        switch (section) {
            case 'impounded':
                await this.loadVehicles({ status: 'impounded' });
                break;
            case 'pending':
                await this.loadVehicles({ status: 'pending_release' });
                break;
            case 'released':
                await this.loadVehicles({ status: 'released' });
                break;
            case 'overdue':
                await this.loadOverdueVehicles();
                break;
            case 'dashboard':
                await this.loadDashboardStats();
                break;
        }
    }

    toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        const toggle = document.getElementById('sidebar-toggle');
        const icon = toggle.querySelector('i');

        sidebar.classList.toggle('sidebar-minimized');

        if (sidebar.classList.contains('sidebar-minimized')) {
            icon.classList.remove('fa-chevron-left');
            icon.classList.add('fa-chevron-right');
        } else {
            icon.classList.remove('fa-chevron-right');
            icon.classList.add('fa-chevron-left');
        }
    }

    // Utility functions
    formatDate(dateString) {
        return new Date(dateString).toLocaleDateString('fr-FR');
    }

    formatCurrency(amount) {
        return `${parseFloat(amount).toFixed(2)} DH`;
    }

    getStatusClass(status) {
        const classes = {
            'impounded': 'bg-blue-100 text-blue-800',
            'pending_release': 'bg-yellow-100 text-yellow-800',
            'released': 'bg-green-100 text-green-800',
            'overdue': 'bg-red-100 text-red-800'
        };
        return classes[status] || 'bg-gray-100 text-gray-800';
    }

    getStatusText(status) {
        const texts = {
            'impounded': 'En fourrière',
            'pending_release': 'En attente',
            'released': 'Sorti',
            'overdue': 'En retard'
        };
        return texts[status] || status;
    }

    showError(message) {
        // Create and show error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message fixed top-4 right-4 z-50';
        errorDiv.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-exclamation-circle mr-2"></i>
                ${message}
                <button onclick="this.parentElement.parentElement.remove()" class="ml-4">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        document.body.appendChild(errorDiv);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (errorDiv.parentElement) {
                errorDiv.remove();
            }
        }, 5000);
    }

    showSuccess(message) {
        // Create and show success message
        const successDiv = document.createElement('div');
        successDiv.className = 'success-message fixed top-4 right-4 z-50';
        successDiv.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-check-circle mr-2"></i>
                ${message}
                <button onclick="this.parentElement.parentElement.remove()" class="ml-4">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        document.body.appendChild(successDiv);

        // Auto remove after 3 seconds
        setTimeout(() => {
            if (successDiv.parentElement) {
                successDiv.remove();
            }
        }, 3000);
    }

    openAddVehicleModal() {
        console.log('Opening add vehicle modal from class method...');
        const modal = document.getElementById('addVehicleModal');
        if (modal) {
            modal.classList.remove('hidden');
            // Set default date to today
            const dateInput = document.querySelector('input[name="entry_date"]');
            if (dateInput) {
                dateInput.value = new Date().toISOString().split('T')[0];
            }
            // Set default time to current time
            const timeInput = document.querySelector('input[name="entry_time"]');
            if (timeInput) {
                const now = new Date();
                const timeString = now.getHours().toString().padStart(2, '0') + ':' + now.getMinutes().toString().padStart(2, '0');
                timeInput.value = timeString;
            }
        } else {
            console.error('Modal not found!');
        }
    }

    closeModal(modalId) {
        console.log('Closing modal from class method:', modalId);
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('hidden');
            // Reset form if it exists
            const form = modal.querySelector('form');
            if (form) {
                form.reset();
            }
        }
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing FourriereApp...');
    window.app = new FourriereApp();
    console.log('FourriereApp initialized:', window.app);

    // Test if modal functions are available
    console.log('openAddVehicleModal function:', typeof window.openAddVehicleModal);
    console.log('closeModal function:', typeof window.closeModal);
});

// Modal functions (global scope for onclick handlers)
function openAddVehicleModal() {
    console.log('Global openAddVehicleModal called');
    if (window.app && typeof window.app.openAddVehicleModal === 'function') {
        window.app.openAddVehicleModal();
    } else {
        console.log('App not ready, using fallback modal logic');
        const modal = document.getElementById('addVehicleModal');
        if (modal) {
            modal.classList.remove('hidden');
            // Set default date to today
            const dateInput = document.querySelector('input[name="entry_date"]');
            if (dateInput) {
                dateInput.value = new Date().toISOString().split('T')[0];
            }
            // Set default time to current time
            const timeInput = document.querySelector('input[name="entry_time"]');
            if (timeInput) {
                const now = new Date();
                const timeString = now.getHours().toString().padStart(2, '0') + ':' + now.getMinutes().toString().padStart(2, '0');
                timeInput.value = timeString;
            }
        } else {
            console.error('Modal not found!');
        }
    }
}

function closeModal(modalId) {
    console.log('Global closeModal called:', modalId);
    if (window.app && typeof window.app.closeModal === 'function') {
        window.app.closeModal(modalId);
    } else {
        console.log('App not ready, using fallback modal logic');
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('hidden');
            // Reset form if it exists
            const form = modal.querySelector('form');
            if (form) {
                form.reset();
            }
        }
    }
}

// Global functions for onclick handlers
function generateReport(type) {
    if (window.app) {
        window.app.generateReport(type);
    }
}

// Make functions available globally for onclick handlers
window.openAddVehicleModal = openAddVehicleModal;
window.closeModal = closeModal;
window.generateReport = generateReport;

// Add more methods to FourriereApp class
FourriereApp.prototype.handleAddVehicle = async function(e) {
    e.preventDefault();

    try {
        const formData = new FormData(e.target);
        const vehicleData = Object.fromEntries(formData.entries());

        // Prepare owner info if provided
        if (vehicleData.owner_first_name || vehicleData.owner_last_name) {
            vehicleData.owner_info = {
                first_name: vehicleData.owner_first_name,
                last_name: vehicleData.owner_last_name,
                phone: vehicleData.owner_phone,
                email: vehicleData.owner_email,
                address: vehicleData.owner_address
            };
        }

        // Remove owner fields from main data
        delete vehicleData.owner_first_name;
        delete vehicleData.owner_last_name;
        delete vehicleData.owner_phone;
        delete vehicleData.owner_email;
        delete vehicleData.owner_address;

        await this.apiCall('/vehicles', {
            method: 'POST',
            body: JSON.stringify(vehicleData)
        });

        this.showSuccess('Véhicule enregistré avec succès');
        closeModal('addVehicleModal');

        // Refresh current view
        const activeSection = document.querySelector('.nav-item.active-nav-item').getAttribute('data-target');
        await this.loadSectionData(activeSection);
        await this.loadDashboardStats();

    } catch (error) {
        console.error('Error adding vehicle:', error);
        this.showError('Erreur lors de l\'enregistrement du véhicule');
    }
};

FourriereApp.prototype.handlePayment = async function(e) {
    e.preventDefault();

    try {
        const formData = new FormData(e.target);
        const paymentData = Object.fromEntries(formData.entries());

        await this.apiCall('/payments/process-release', {
            method: 'POST',
            body: JSON.stringify(paymentData)
        });

        this.showSuccess('Paiement traité et véhicule libéré avec succès');
        closeModal('paymentModal');

        // Refresh current view
        const activeSection = document.querySelector('.nav-item.active-nav-item').getAttribute('data-target');
        await this.loadSectionData(activeSection);
        await this.loadDashboardStats();

    } catch (error) {
        console.error('Error processing payment:', error);
        this.showError('Erreur lors du traitement du paiement');
    }
};

FourriereApp.prototype.loadVehicles = async function(filters = {}) {
    try {
        const params = new URLSearchParams({
            page: this.currentPage,
            limit: 10,
            ...filters,
            ...this.currentFilters
        });

        const data = await this.apiCall(`/vehicles?${params}`);

        // Determine which table to populate based on status
        let tableId = 'vehicles-table';
        if (filters.status === 'pending_release') {
            tableId = 'pending-vehicles-table';
        } else if (filters.status === 'released') {
            tableId = 'released-vehicles-table';
        }

        const tbody = document.getElementById(tableId);

        if (data.vehicles.length === 0) {
            tbody.innerHTML = '<tr><td colspan="9" class="text-center py-8 text-gray-500">Aucun véhicule trouvé</td></tr>';
            return;
        }

        tbody.innerHTML = data.vehicles.map(vehicle => {
            if (filters.status === 'pending_release') {
                return this.renderPendingVehicleRow(vehicle);
            } else if (filters.status === 'released') {
                return this.renderReleasedVehicleRow(vehicle);
            } else {
                return this.renderVehicleRow(vehicle);
            }
        }).join('');

        // Update pagination
        this.updatePagination(data.pagination, filters);

    } catch (error) {
        console.error('Error loading vehicles:', error);
        this.showError('Erreur lors du chargement des véhicules');
    }
};

FourriereApp.prototype.renderVehicleRow = function(vehicle) {
    return `
        <tr>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${vehicle.depot_number}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${vehicle.vehicle_type_name}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${vehicle.license_plate}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${vehicle.brand} ${vehicle.model}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${this.formatDate(vehicle.entry_date)}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${vehicle.days_in_storage}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${this.formatCurrency(vehicle.total_storage_cost)}</td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="status-badge ${this.getStatusClass(vehicle.status)}">${this.getStatusText(vehicle.status)}</span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <button class="text-blue-600 hover:text-blue-900 mr-2" onclick="app.viewVehicle(${vehicle.id})" title="Voir">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="text-green-600 hover:text-green-900 mr-2" onclick="app.editVehicle(${vehicle.id})" title="Modifier">
                    <i class="fas fa-edit"></i>
                </button>
                ${vehicle.status === 'impounded' ? `
                    <button class="text-yellow-600 hover:text-yellow-900 mr-2" onclick="app.openPaymentModal(${vehicle.id})" title="Paiement">
                        <i class="fas fa-credit-card"></i>
                    </button>
                ` : ''}
                <button class="text-red-600 hover:text-red-900" onclick="app.deleteVehicle(${vehicle.id})" title="Supprimer">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `;
};

FourriereApp.prototype.renderPendingVehicleRow = function(vehicle) {
    return `
        <tr>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${vehicle.depot_number}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${vehicle.license_plate} - ${vehicle.brand} ${vehicle.model}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${this.formatDate(vehicle.entry_date)}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${vehicle.days_in_storage}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${this.formatCurrency(vehicle.total_storage_cost)}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <button class="text-green-600 hover:text-green-900 mr-2" onclick="app.openPaymentModal(${vehicle.id})" title="Traiter le paiement">
                    <i class="fas fa-credit-card"></i> Payer
                </button>
                <button class="text-blue-600 hover:text-blue-900" onclick="app.viewVehicle(${vehicle.id})" title="Voir détails">
                    <i class="fas fa-eye"></i>
                </button>
            </td>
        </tr>
    `;
};

FourriereApp.prototype.renderReleasedVehicleRow = function(vehicle) {
    return `
        <tr>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${vehicle.depot_number}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${vehicle.license_plate} - ${vehicle.brand} ${vehicle.model}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${this.formatDate(vehicle.entry_date)}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${vehicle.release_date ? this.formatDate(vehicle.release_date) : '-'}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${vehicle.days_in_storage} jours</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${vehicle.payment_amount ? this.formatCurrency(vehicle.payment_amount) : '-'}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <button class="text-blue-600 hover:text-blue-900 mr-2" onclick="app.viewVehicle(${vehicle.id})" title="Voir détails">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="text-green-600 hover:text-green-900" onclick="app.printReceipt(${vehicle.id})" title="Imprimer reçu">
                    <i class="fas fa-print"></i>
                </button>
            </td>
        </tr>
    `;
};

// Additional methods for complete functionality
FourriereApp.prototype.loadOverdueVehicles = async function() {
    try {
        const data = await this.apiCall('/reports/overdue');
        const tbody = document.getElementById('overdue-vehicles-table');

        if (data.vehicles.length === 0) {
            tbody.innerHTML = '<tr><td colspan="6" class="text-center py-8 text-gray-500">Aucun véhicule en retard</td></tr>';
            return;
        }

        tbody.innerHTML = data.vehicles.map(vehicle => `
            <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${vehicle.depot_number}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${vehicle.license_plate} - ${vehicle.brand} ${vehicle.model}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${this.formatDate(vehicle.entry_date)}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600 font-medium">${vehicle.days_in_storage}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600 font-medium">${this.formatCurrency(vehicle.total_storage_cost)}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <button class="text-blue-600 hover:text-blue-900 mr-2" onclick="app.viewVehicle(${vehicle.id})" title="Voir détails">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="text-yellow-600 hover:text-yellow-900 mr-2" onclick="app.openPaymentModal(${vehicle.id})" title="Traiter le paiement">
                        <i class="fas fa-credit-card"></i>
                    </button>
                    <button class="text-red-600 hover:text-red-900" onclick="app.markAsDestroyed(${vehicle.id})" title="Marquer comme détruit">
                        <i class="fas fa-trash-alt"></i>
                    </button>
                </td>
            </tr>
        `).join('');

    } catch (error) {
        console.error('Error loading overdue vehicles:', error);
        this.showError('Erreur lors du chargement des véhicules en retard');
    }
};

FourriereApp.prototype.openPaymentModal = async function(vehicleId) {
    try {
        // Get vehicle details
        const vehicle = await this.apiCall(`/vehicles/${vehicleId}`);

        // Calculate payment amount
        const calculation = await this.apiCall(`/payments/calculate/${vehicleId}`);

        // Populate modal
        document.getElementById('payment-vehicle-id').value = vehicleId;
        document.getElementById('payment-vehicle-info').innerHTML = `
            <strong>${vehicle.license_plate}</strong> - ${vehicle.brand} ${vehicle.model}<br>
            <small>Dépôt: ${vehicle.depot_number} | Entrée: ${this.formatDate(vehicle.entry_date)}</small>
        `;
        document.getElementById('payment-amount').value = calculation.total_amount;

        // Set default dates
        document.querySelector('input[name="payment_date"]').value = new Date().toISOString().split('T')[0];
        document.querySelector('input[name="release_date"]').value = new Date().toISOString().split('T')[0];

        // Generate receipt and release order numbers
        const timestamp = Date.now();
        document.querySelector('input[name="receipt_number"]').value = `REC${timestamp}`;
        document.querySelector('input[name="release_order_number"]').value = `REL${timestamp}`;

        // Show modal
        document.getElementById('paymentModal').classList.remove('hidden');

    } catch (error) {
        console.error('Error opening payment modal:', error);
        this.showError('Erreur lors de l\'ouverture du modal de paiement');
    }
};

FourriereApp.prototype.applyFilters = function() {
    const search = document.getElementById('vehicle-search').value;
    const vehicleType = document.getElementById('vehicle-type-filter').value;
    const status = document.getElementById('vehicle-status-filter').value;

    this.currentFilters = {
        search: search || undefined,
        vehicle_type_id: vehicleType || undefined,
        status: status || undefined
    };

    this.currentPage = 1;

    // Load vehicles with filters
    const activeSection = document.querySelector('.nav-item.active-nav-item').getAttribute('data-target');
    this.loadSectionData(activeSection);
};

FourriereApp.prototype.updatePagination = function(pagination, filters = {}) {
    const container = document.getElementById('vehicles-pagination');
    if (!container) return;

    const { page, pages, total } = pagination;

    if (pages <= 1) {
        container.innerHTML = `<div class="text-sm text-gray-500">Total: ${total} véhicule(s)</div>`;
        return;
    }

    let paginationHTML = `
        <div class="flex items-center justify-between">
            <div class="text-sm text-gray-500">
                Page ${page} sur ${pages} (${total} véhicule(s) au total)
            </div>
            <div class="flex space-x-2">
    `;

    // Previous button
    if (page > 1) {
        paginationHTML += `<button onclick="app.goToPage(${page - 1})" class="px-3 py-1 border rounded hover:bg-gray-50">Précédent</button>`;
    }

    // Page numbers
    for (let i = Math.max(1, page - 2); i <= Math.min(pages, page + 2); i++) {
        const isActive = i === page;
        paginationHTML += `
            <button onclick="app.goToPage(${i})"
                    class="px-3 py-1 border rounded ${isActive ? 'bg-blue-500 text-white' : 'hover:bg-gray-50'}">
                ${i}
            </button>
        `;
    }

    // Next button
    if (page < pages) {
        paginationHTML += `<button onclick="app.goToPage(${page + 1})" class="px-3 py-1 border rounded hover:bg-gray-50">Suivant</button>`;
    }

    paginationHTML += '</div></div>';
    container.innerHTML = paginationHTML;
};

FourriereApp.prototype.goToPage = function(page) {
    this.currentPage = page;
    const activeSection = document.querySelector('.nav-item.active-nav-item').getAttribute('data-target');
    this.loadSectionData(activeSection);
};

FourriereApp.prototype.viewVehicle = function(vehicleId) {
    // This would open a detailed view modal
    alert(`Voir détails du véhicule ID: ${vehicleId}`);
};

FourriereApp.prototype.editVehicle = function(vehicleId) {
    // This would open the edit modal
    alert(`Modifier véhicule ID: ${vehicleId}`);
};

FourriereApp.prototype.deleteVehicle = async function(vehicleId) {
    if (!confirm('Êtes-vous sûr de vouloir supprimer ce véhicule ?')) {
        return;
    }

    try {
        await this.apiCall(`/vehicles/${vehicleId}`, { method: 'DELETE' });
        this.showSuccess('Véhicule supprimé avec succès');

        // Refresh current view
        const activeSection = document.querySelector('.nav-item.active-nav-item').getAttribute('data-target');
        await this.loadSectionData(activeSection);
        await this.loadDashboardStats();

    } catch (error) {
        console.error('Error deleting vehicle:', error);
        this.showError('Erreur lors de la suppression du véhicule');
    }
};

FourriereApp.prototype.markAsDestroyed = async function(vehicleId) {
    if (!confirm('Marquer ce véhicule comme détruit ? Cette action est irréversible.')) {
        return;
    }

    try {
        await this.apiCall(`/vehicles/${vehicleId}/status`, {
            method: 'PUT',
            body: JSON.stringify({ status: 'destroyed' })
        });

        this.showSuccess('Véhicule marqué comme détruit');

        // Refresh current view
        const activeSection = document.querySelector('.nav-item.active-nav-item').getAttribute('data-target');
        await this.loadSectionData(activeSection);
        await this.loadDashboardStats();

    } catch (error) {
        console.error('Error marking vehicle as destroyed:', error);
        this.showError('Erreur lors de la mise à jour du statut');
    }
};

FourriereApp.prototype.printReceipt = function(vehicleId) {
    // This would generate and print a receipt
    alert(`Imprimer reçu pour véhicule ID: ${vehicleId}`);
};

FourriereApp.prototype.performGlobalSearch = async function(query) {
    if (!query.trim()) return;

    try {
        const results = await this.apiCall(`/vehicles?search=${encodeURIComponent(query)}`);

        // Switch to vehicles view and show results
        document.querySelector('[data-target="impounded"]').click();

        // Update search field
        document.getElementById('vehicle-search').value = query;

        this.showSuccess(`${results.pagination.total} résultat(s) trouvé(s)`);

    } catch (error) {
        console.error('Error performing global search:', error);
        this.showError('Erreur lors de la recherche');
    }
};

FourriereApp.prototype.generateReport = async function(type) {
    try {
        const reportData = await this.apiCall(`/reports/${type}`);

        // Show report content
        document.getElementById('report-content').classList.remove('hidden');
        document.getElementById('report-title').textContent = this.getReportTitle(type);

        // Populate report data
        const reportContainer = document.getElementById('report-data');
        reportContainer.innerHTML = this.renderReportData(type, reportData);

    } catch (error) {
        console.error('Error generating report:', error);
        this.showError('Erreur lors de la génération du rapport');
    }
};

FourriereApp.prototype.getReportTitle = function(type) {
    const titles = {
        'summary': 'Rapport de synthèse',
        'financial': 'Rapport financier',
        'vehicles': 'Rapport des véhicules'
    };
    return titles[type] || 'Rapport';
};

FourriereApp.prototype.renderReportData = function(type, data) {
    // This would render different report formats based on type
    return `<pre>${JSON.stringify(data, null, 2)}</pre>`;
};

FourriereApp.prototype.saveSettings = async function() {
    try {
        const settings = {
            storage_cost: document.getElementById('storage-cost').value,
            overdue_threshold: document.getElementById('overdue-threshold').value,
            currency: document.getElementById('currency').value,
            company_name: document.getElementById('company-name').value,
            company_address: document.getElementById('company-address').value,
            company_phone: document.getElementById('company-phone').value,
            company_email: document.getElementById('company-email').value
        };

        // In a real implementation, this would save to the backend
        console.log('Saving settings:', settings);
        this.showSuccess('Paramètres sauvegardés avec succès');

    } catch (error) {
        console.error('Error saving settings:', error);
        this.showError('Erreur lors de la sauvegarde des paramètres');
    }
};