const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

async function createAuthTables() {
    let connection;

    try {
        console.log('🔐 Creating authentication tables...');

        // Create connection
        connection = await mysql.createConnection({
            host: process.env.DB_HOST,
            port: process.env.DB_PORT,
            user: process.env.DB_USER,
            password: process.env.DB_PASSWORD,
            database: process.env.DB_NAME,
            multipleStatements: true
        });

        console.log('✅ Connected to database');

        // Execute SQL commands one by one
        console.log('📄 Creating roles table...');
        await connection.execute(`
            CREATE TABLE IF NOT EXISTS roles (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(50) NOT NULL UNIQUE,
                description TEXT,
                permissions TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        `);

        console.log('📄 Creating users table...');
        await connection.execute(`
            CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) NOT NULL UNIQUE,
                email VARCHAR(100) NOT NULL UNIQUE,
                password_hash VARCHAR(255) NOT NULL,
                first_name VARCHAR(100),
                last_name VARCHAR(100),
                role_id INT NOT NULL,
                is_active BOOLEAN DEFAULT TRUE,
                last_login TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE RESTRICT
            )
        `);

        console.log('📄 Creating user_sessions table...');
        await connection.execute(`
            CREATE TABLE IF NOT EXISTS user_sessions (
                id VARCHAR(128) PRIMARY KEY,
                user_id INT NOT NULL,
                expires_at TIMESTAMP NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            )
        `);

        console.log('📄 Creating user_audit_log table...');
        await connection.execute(`
            CREATE TABLE IF NOT EXISTS user_audit_log (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                action VARCHAR(100) NOT NULL,
                resource_type VARCHAR(50),
                resource_id INT,
                details TEXT,
                ip_address VARCHAR(45),
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            )
        `);

        console.log('📄 Inserting default roles...');
        await connection.execute(`
            INSERT IGNORE INTO roles (name, description, permissions) VALUES
            ('admin', 'Administrateur système', 'vehicles:crud,payments:crud,reports:crud,settings:crud,users:crud,depositors:crud'),
            ('operator', 'Opérateur', 'vehicles:cru,payments:cru,reports:r,depositors:r'),
            ('viewer', 'Visualiseur', 'vehicles:r,payments:r,reports:r,depositors:r')
        `);

        console.log('📄 Inserting default admin user...');
        await connection.execute(`
            INSERT IGNORE INTO users (username, email, password_hash, first_name, last_name, role_id) VALUES
            ('admin', '<EMAIL>', '$2a$10$MGZhssflqrMHzwIYmLyY8.QcXx8wwennSBA4RieYC7pKh88ibNxVu', 'Administrateur', 'Système', 1)
        `);

        console.log('✅ Authentication tables created successfully');

        // Verify tables exist
        console.log('🔍 Verifying tables...');

        const [roles] = await connection.execute('SELECT COUNT(*) as count FROM roles');
        console.log(`   📋 Roles table: ${roles[0].count} roles found`);

        const [users] = await connection.execute('SELECT COUNT(*) as count FROM users');
        console.log(`   👤 Users table: ${users[0].count} users found`);

        const [sessions] = await connection.execute('SHOW TABLES LIKE "user_sessions"');
        console.log(`   🔑 Sessions table: ${sessions.length > 0 ? 'EXISTS' : 'NOT FOUND'}`);

        const [audit] = await connection.execute('SHOW TABLES LIKE "user_audit_log"');
        console.log(`   📊 Audit log table: ${audit.length > 0 ? 'EXISTS' : 'NOT FOUND'}`);

        // Test admin user
        const [adminUser] = await connection.execute('SELECT username, email, role_id FROM users WHERE username = "admin"');
        if (adminUser.length > 0) {
            console.log(`   🔐 Admin user: ${adminUser[0].username} (${adminUser[0].email}) - Role ID: ${adminUser[0].role_id}`);
        }

        console.log('\n🎉 Authentication system setup completed!');
        console.log('📋 Default credentials:');
        console.log('   Username: admin');
        console.log('   Password: admin123');

    } catch (error) {
        console.error('❌ Error creating authentication tables:', error.message);
        throw error;
    } finally {
        if (connection) {
            await connection.end();
        }
    }
}

// Run if this file is executed directly
if (require.main === module) {
    createAuthTables()
        .then(() => {
            console.log('✅ Script completed successfully');
            process.exit(0);
        })
        .catch((error) => {
            console.error('❌ Script failed:', error.message);
            process.exit(1);
        });
}

module.exports = { createAuthTables };
