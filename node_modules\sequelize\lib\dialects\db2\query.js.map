{"version": 3, "sources": ["../../../src/dialects/db2/query.js"], "sourcesContent": ["'use strict';\n\nconst util = require('util');\n\nconst AbstractQuery = require('../abstract/query');\nconst sequelizeErrors = require('../../errors');\nconst parserStore = require('../parserStore')('db2');\nconst _ = require('lodash');\nconst { logger } = require('../../utils/logger');\nconst moment = require('moment');\nconst debug = logger.debugContext('sql:db2');\n\nclass Query extends AbstractQuery {\n  getInsertIdField() {\n    return 'id';\n  }\n\n  getSQLTypeFromJsType(value) {\n    if (Buffer.isBuffer(value)) {\n      return { ParamType: 'INPUT', DataType: 'BLOB', Data: value };\n    }\n\n    if (typeof value === 'bigint') {\n      // The ibm_db module does not handle bigint, send as a string instead:\n      return value.toString();\n    }\n\n    return value;\n  }\n\n  async _run(connection, sql, parameters) {\n    this.sql = sql;\n    const benchmark = this.sequelize.options.benchmark || this.options.benchmark;\n    let queryBegin;\n    if (benchmark) {\n      queryBegin = Date.now();\n    } else {\n      this.sequelize.log(`Executing (${ this.connection.uuid || 'default' }): ${ this.sql}`, this.options);\n    }\n\n    const errStack = new Error().stack;\n\n    return new Promise((resolve, reject) => {\n      // TRANSACTION SUPPORT\n      if (_.startsWith(this.sql, 'BEGIN TRANSACTION')) {\n        connection.beginTransaction(err => {\n          if (err) {\n            reject(this.formatError(err, errStack));\n          } else {\n            resolve(this.formatResults());\n          }\n        });\n      } else if (_.startsWith(this.sql, 'COMMIT TRANSACTION')) {\n        connection.commitTransaction(err => {\n          if (err) {\n            reject(this.formatError(err, errStack));\n          } else {\n            resolve(this.formatResults());\n          }\n        });\n      } else if (_.startsWith(this.sql, 'ROLLBACK TRANSACTION')) {\n        connection.rollbackTransaction(err => {\n          if (err) {\n            reject(this.formatError(err, errStack));\n          } else {\n            resolve(this.formatResults());\n          }\n        });\n      } else if (_.startsWith(this.sql, 'SAVE TRANSACTION')) {\n        connection.commitTransaction(err => {\n          if (err) {\n            reject(this.formatError(err, errStack));\n          } else {\n            connection.beginTransaction(err => {\n              if (err) {\n                reject(this.formatError(err, errStack));\n              } else {\n                resolve(this.formatResults());\n              }\n            });\n          }\n        }, this.options.transaction.name);\n      } else {\n        const params = [];\n        if (parameters) {\n          _.forOwn(parameters, (value, key) => {\n            const param = this.getSQLTypeFromJsType(value, key);\n            params.push(param);\n          });\n        }\n        const SQL = this.sql.toUpperCase();\n        let newSql = this.sql;\n        if ((this.isSelectQuery() || _.startsWith(SQL, 'SELECT ')) &&\n            SQL.indexOf(' FROM ', 8) === -1 ) {\n          if (this.sql.charAt(this.sql.length - 1) === ';') {\n            newSql = this.sql.slice(0, this.sql.length - 1);\n          }\n          newSql += ' FROM SYSIBM.SYSDUMMY1;';\n        }\n\n        connection.prepare(newSql, (err, stmt) => {\n          if (err) {\n            reject(this.formatError(err, errStack));\n          }\n\n          stmt.execute(params, (err, result, outparams) => {\n            debug(`executed(${this.connection.uuid || 'default'}):${newSql} ${parameters ? util.inspect(parameters, { compact: true, breakLength: Infinity }) : ''}`);\n\n            if (benchmark) {\n              this.sequelize.log(`Executed (${this.connection.uuid || 'default'}): ${newSql} ${parameters ? util.inspect(parameters, { compact: true, breakLength: Infinity }) : ''}`, Date.now() - queryBegin, this.options);\n            }\n\n            if (err && err.message) {\n              err = this.filterSQLError(err, this.sql, connection);\n              if (err === null) {\n                stmt.closeSync();\n                resolve(this.formatResults([], 0));\n              }\n            }\n            if (err) {\n              err.sql = sql;\n              stmt.closeSync();\n              reject(this.formatError(err, errStack, connection, parameters));\n            } else {\n              let data = [];\n              let metadata = [];\n              let affectedRows = 0;\n              if (typeof result === 'object') {\n                if (_.startsWith(this.sql, 'DELETE FROM ')) {\n                  affectedRows = result.getAffectedRowsSync();\n                } else {\n                  data = result.fetchAllSync();\n                  metadata = result.getColumnMetadataSync();\n                }\n                result.closeSync();\n              }\n              stmt.closeSync();\n              const datalen = data.length;\n              if (datalen > 0) {\n                const coltypes = {};\n                for (let i = 0; i < metadata.length; i++) {\n                  coltypes[metadata[i].SQL_DESC_NAME] =\n                      metadata[i].SQL_DESC_TYPE_NAME;\n                }\n                for (let i = 0; i < datalen; i++) {\n                  for (const column in data[i]) {\n                    const parse = parserStore.get(coltypes[column]);\n                    const value = data[i][column];\n                    if (value !== null) {\n                      if (parse) {\n                        data[i][column] = parse(value);\n                      } else if (coltypes[column] === 'TIMESTAMP') {\n                        data[i][column] = new Date(moment.utc(value));\n                      } else if (coltypes[column] === 'BLOB') {\n                        data[i][column] = new Buffer.from(value);\n                      } else if (coltypes[column].indexOf('FOR BIT DATA') > 0) {\n                        data[i][column] = new Buffer.from(value, 'hex');\n                      }\n                    }\n                  }\n                }\n                if (outparams && outparams.length) {\n                  data.unshift(outparams);\n                }\n                resolve(this.formatResults(data, datalen, metadata, connection));\n              } else {\n                resolve(this.formatResults(data, affectedRows));\n              }\n            }\n          });\n        });\n      }\n    });\n  }\n\n  async run(sql, parameters) {\n    return await this._run(this.connection, sql, parameters);\n  }\n\n  static formatBindParameters(sql, values, dialect) {\n    let bindParam = {};\n    const replacementFunc = (match, key, values) => {\n      if (values[key] !== undefined) {\n        bindParam[key] = values[key];\n        return '?';\n      }\n      return undefined;\n    };\n    sql = AbstractQuery.formatBindParameters(sql, values, dialect, replacementFunc)[0];\n    if (Array.isArray(values) && typeof values[0] === 'object') {\n      bindParam = values;\n    }\n\n    return [sql, bindParam];\n  }\n\n  filterSQLError(err, sql, connection) {\n    if (err.message.search('SQL0204N') != -1 && _.startsWith(sql, 'DROP ')) {\n      err = null; // Ignore table not found error for drop table.\n    } else if (err.message.search('SQL0443N') != -1) {\n      if (this.isDropSchemaQuery()) {\n        // Delete ERRORSCHEMA.ERRORTABLE if it exist.\n        connection.querySync('DROP TABLE ERRORSCHEMA.ERRORTABLE;');\n        // Retry deleting the schema\n        connection.querySync(this.sql);\n      }\n      err = null; // Ignore drop schema error.\n    } else if (err.message.search('SQL0601N') != -1) {\n      const match = err.message.match(/SQL0601N {2}The name of the object to be created is identical to the existing name \"(.*)\" of type \"(.*)\"./);\n      if (match && match.length > 1 && match[2] === 'TABLE') {\n        let table;\n        const mtarray = match[1].split('.');\n        if (mtarray[1]) {\n          table = `\"${mtarray[0]}\".\"${mtarray[1]}\"`;\n        } else {\n          table = `\"${mtarray[0]}\"`;\n        }\n        if (connection.dropTable !== false) {\n          connection.querySync(`DROP TABLE ${table}`);\n          err = connection.querySync(sql);\n        }\n        else {\n          err = null;\n        }\n      } else {\n        err = null; // Ignore create schema error.\n      }\n    } else if (err.message.search('SQL0911N') != -1) {\n      if (err.message.search('Reason code \"2\"') != -1) {\n        err = null; // Ignore deadlock error due to program logic.\n      }\n    } else if (err.message.search('SQL0605W') != -1) {\n      err = null; // Ignore warning.\n    } else if (err.message.search('SQL0668N') != -1 &&\n      _.startsWith(sql, 'ALTER TABLE ')) {\n      connection.querySync(`CALL SYSPROC.ADMIN_CMD('REORG TABLE ${sql.substring(12).split(' ')[0]}')`);\n      err = connection.querySync(sql);\n    }\n    if (err && err.length === 0) { err = null; }\n    return err;\n  }\n\n  /**\n   * High level function that handles the results of a query execution.\n   *\n   *\n   * Example:\n   *  query.formatResults([\n   *    {\n   *      id: 1,              // this is from the main table\n   *      attr2: 'snafu',     // this is from the main table\n   *      Tasks.id: 1,        // this is from the associated table\n   *      Tasks.title: 'task' // this is from the associated table\n   *    }\n   *  ])\n   *\n   * @param {Array} data - The result of the query execution.\n   * @param {Integer} rowCount - The number of affected rows.\n   * @param {Array} metadata - Metadata of the returned result set.\n   * @param {object} conn - The connection object.\n   * @private\n   */\n  formatResults(data, rowCount, metadata, conn) {\n    let result = this.instance;\n    if (this.isInsertQuery(data, metadata)) {\n      this.handleInsertQuery(data, metadata);\n\n      if (!this.instance) {\n        if (this.options.plain) {\n          const record = data[0];\n          result = record[Object.keys(record)[0]];\n        } else {\n          result = data;\n        }\n      }\n    }\n\n    if (this.isShowTablesQuery()) {\n      result = data;\n    } else if (this.isDescribeQuery()) {\n      result = {};\n      for (const _result of data) {\n        if (_result.Default) {\n          _result.Default = _result.Default.replace(\"('\", '').replace(\"')\", '').replace(/'/g, '');\n        }\n\n        result[_result.Name] = {\n          type: _result.Type.toUpperCase(),\n          allowNull: _result.IsNull === 'Y' ? true : false,\n          defaultValue: _result.Default,\n          primaryKey: _result.KeySeq > 0,\n          autoIncrement: _result.IsIdentity === 'Y' ? true : false,\n          comment: _result.Comment\n        };\n      }\n    } else if (this.isShowIndexesQuery()) {\n      result = this.handleShowIndexesQuery(data);\n    } else if (this.isSelectQuery()) {\n      result = this.handleSelectQuery(data);\n    } else if (this.isUpsertQuery()) {\n      result = data;\n    } else if (this.isDropSchemaQuery()) {\n      result = data[0];\n      if (conn) {\n        const query = 'DROP TABLE ERRORSCHEMA.ERRORTABLE';\n        conn.querySync(query);\n      }\n    } else if (this.isCallQuery()) {\n      result = data;\n    } else if (this.isBulkUpdateQuery()) {\n      result = data.length;\n    } else if (this.isBulkDeleteQuery()) {\n      result = rowCount;\n    } else if (this.isVersionQuery()) {\n      result = data[0].VERSION;\n    } else if (this.isForeignKeysQuery()) {\n      result = data;\n    } else if (this.isInsertQuery() || this.isUpdateQuery()) {\n      result = [result, rowCount];\n    } else if (this.isShowConstraintsQuery()) {\n      result = this.handleShowConstraintsQuery(data);\n    } else if (this.isRawQuery()) {\n      // Db2 returns row data and metadata (affected rows etc) in a single object - let's standarize it, sorta\n      result = [data, metadata];\n    } else {\n      result = data;\n    }\n\n    return result;\n  }\n\n  handleShowTablesQuery(results) {\n    return results.map(resultSet => {\n      return {\n        tableName: resultSet.TABLE_NAME,\n        schema: resultSet.TABLE_SCHEMA\n      };\n    });\n  }\n\n  handleShowConstraintsQuery(data) {\n    // Remove SQL Contraints from constraints list.\n    return _.remove(data, constraint => {\n      return !_.startsWith(constraint.constraintName, 'SQL');\n    });\n  }\n\n  formatError(err, errStack, conn, parameters) {\n    let match;\n\n    if (!(err && err.message)) {\n      err['message'] = 'No error message found.';\n    }\n\n    match = err.message.match(/SQL0803N {2}One or more values in the INSERT statement, UPDATE statement, or foreign key update caused by a DELETE statement are not valid because the primary key, unique constraint or unique index identified by \"(\\d)+\" constrains table \"(.*)\\.(.*)\" from having duplicate values for the index key./);\n    if (match && match.length > 0) {\n      let uniqueIndexName = '';\n      let uniqueKey = '';\n      const fields = {};\n      let message = err.message;\n      const query = `SELECT INDNAME FROM SYSCAT.INDEXES  WHERE IID = ${match[1]} AND TABSCHEMA = '${match[2]}' AND TABNAME = '${match[3]}'`;\n\n      if (!!conn && match.length > 3) {\n        uniqueIndexName = conn.querySync(query);\n        uniqueIndexName = uniqueIndexName[0]['INDNAME'];\n      }\n\n      if (this.model && !!uniqueIndexName) {\n        uniqueKey = this.model.uniqueKeys[uniqueIndexName];\n      }\n\n      if (!uniqueKey && this.options.fields) {\n        uniqueKey = this.options.fields[match[1] - 1];\n      }\n\n      if (uniqueKey) {\n        if (this.options.where &&\n          this.options.where[uniqueKey.column] !== undefined) {\n          fields[uniqueKey.column] = this.options.where[uniqueKey.column];\n        } else if (this.options.instance && this.options.instance.dataValues &&\n          this.options.instance.dataValues[uniqueKey.column]) {\n          fields[uniqueKey.column] = this.options.instance.dataValues[uniqueKey.column];\n        } else if (parameters) {\n          fields[uniqueKey.column] = parameters['0'];\n        }\n      }\n\n      if (uniqueKey && !!uniqueKey.msg) {\n        message = uniqueKey.msg;\n      }\n\n      const errors = [];\n      _.forOwn(fields, (value, field) => {\n        errors.push(new sequelizeErrors.ValidationErrorItem(\n          this.getUniqueConstraintErrorMessage(field),\n          'unique violation', // sequelizeErrors.ValidationErrorItem.Origins.DB,\n          field,\n          value,\n          this.instance,\n          'not_unique'\n        ));\n      });\n\n      return new sequelizeErrors.UniqueConstraintError({ message, errors, parent: err, fields, stack: errStack });\n    }\n\n    match = err.message.match(/SQL0532N {2}A parent row cannot be deleted because the relationship \"(.*)\" restricts the deletion/) ||\n      err.message.match(/SQL0530N/) ||\n      err.message.match(/SQL0531N/);\n    if (match && match.length > 0) {\n      return new sequelizeErrors.ForeignKeyConstraintError({\n        fields: null,\n        index: match[1],\n        parent: err,\n        stack: errStack\n      });\n    }\n\n    match = err.message.match(/SQL0204N {2}\"(.*)\" is an undefined name./);\n    if (match && match.length > 1) {\n      const constraint = match[1];\n      let table = err.sql.match(/table \"(.+?)\"/i);\n      table = table ? table[1] : undefined;\n\n      return new sequelizeErrors.UnknownConstraintError({\n        message: match[0],\n        constraint,\n        table,\n        parent: err,\n        stack: errStack\n      });\n    }\n\n    return new sequelizeErrors.DatabaseError(err, { stack: errStack });\n  }\n\n\n  isDropSchemaQuery() {\n    let result = false;\n\n    if (_.startsWith(this.sql, 'CALL SYSPROC.ADMIN_DROP_SCHEMA')) {\n      result = true;\n    }\n    return result;\n  }\n\n  isShowOrDescribeQuery() {\n    let result = false;\n\n    result = result || this.sql.toLowerCase().startsWith(\"select c.column_name as 'name', c.data_type as 'type', c.is_nullable as 'isnull'\");\n    result = result || this.sql.toLowerCase().startsWith('select tablename = t.name, name = ind.name,');\n    result = result || this.sql.toLowerCase().startsWith('exec sys.sp_helpindex @objname');\n\n    return result;\n  }\n  isShowIndexesQuery() {\n    let result = false;\n\n    result = result || this.sql.toLowerCase().startsWith('exec sys.sp_helpindex @objname');\n    result = result || this.sql.startsWith('SELECT NAME AS \"name\", TBNAME AS \"tableName\", UNIQUERULE AS \"keyType\", COLNAMES, INDEXTYPE AS \"type\" FROM SYSIBM.SYSINDEXES');\n    return result;\n  }\n\n  handleShowIndexesQuery(data) {\n    let currItem;\n    const result = [];\n    data.forEach(item => {\n      if (!currItem || currItem.name !== item.Key_name) {\n        currItem = {\n          primary: item.keyType === 'P',\n          fields: [],\n          name: item.name,\n          tableName: item.tableName,\n          unique: item.keyType === 'U',\n          type: item.type\n        };\n\n        _.forEach(item.COLNAMES.replace(/\\+|-/g, x => { return ` ${ x}`; }).split(' '), column => {\n          let columnName = column.trim();\n          if ( columnName ) {\n            columnName = columnName.replace(/\\+|-/, '');\n            currItem.fields.push({\n              attribute: columnName,\n              length: undefined,\n              order: column.indexOf('-') === -1 ? 'ASC' : 'DESC',\n              collate: undefined\n            });\n          }\n        });\n        result.push(currItem);\n      }\n    });\n    return result;\n  }\n\n  handleInsertQuery(results, metaData) {\n    if (this.instance) {\n      // add the inserted row id to the instance\n      const autoIncrementAttribute = this.model.autoIncrementAttribute;\n      let id = null;\n      let autoIncrementAttributeAlias = null;\n\n      if (Object.prototype.hasOwnProperty.call(this.model.rawAttributes, autoIncrementAttribute) &&\n          this.model.rawAttributes[autoIncrementAttribute].field !== undefined)\n        autoIncrementAttributeAlias = this.model.rawAttributes[autoIncrementAttribute].field;\n      id = id || results && results[0][this.getInsertIdField()];\n      id = id || metaData && metaData[this.getInsertIdField()];\n      id = id || results && results[0][autoIncrementAttribute];\n      id = id || autoIncrementAttributeAlias && results && results[0][autoIncrementAttributeAlias];\n      this.instance[autoIncrementAttribute] = id;\n    }\n  }\n}\n\nmodule.exports = Query;\nmodule.exports.Query = Query;\nmodule.exports.default = Query;\n"], "mappings": ";AAEA,MAAM,OAAO,QAAQ;AAErB,MAAM,gBAAgB,QAAQ;AAC9B,MAAM,kBAAkB,QAAQ;AAChC,MAAM,cAAc,QAAQ,kBAAkB;AAC9C,MAAM,IAAI,QAAQ;AAClB,MAAM,EAAE,WAAW,QAAQ;AAC3B,MAAM,SAAS,QAAQ;AACvB,MAAM,QAAQ,OAAO,aAAa;AAElC,oBAAoB,cAAc;AAAA,EAChC,mBAAmB;AACjB,WAAO;AAAA;AAAA,EAGT,qBAAqB,OAAO;AAC1B,QAAI,OAAO,SAAS,QAAQ;AAC1B,aAAO,EAAE,WAAW,SAAS,UAAU,QAAQ,MAAM;AAAA;AAGvD,QAAI,OAAO,UAAU,UAAU;AAE7B,aAAO,MAAM;AAAA;AAGf,WAAO;AAAA;AAAA,QAGH,KAAK,YAAY,KAAK,YAAY;AACtC,SAAK,MAAM;AACX,UAAM,YAAY,KAAK,UAAU,QAAQ,aAAa,KAAK,QAAQ;AACnE,QAAI;AACJ,QAAI,WAAW;AACb,mBAAa,KAAK;AAAA,WACb;AACL,WAAK,UAAU,IAAI,cAAe,KAAK,WAAW,QAAQ,eAAiB,KAAK,OAAO,KAAK;AAAA;AAG9F,UAAM,WAAW,IAAI,QAAQ;AAE7B,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AAEtC,UAAI,EAAE,WAAW,KAAK,KAAK,sBAAsB;AAC/C,mBAAW,iBAAiB,SAAO;AACjC,cAAI,KAAK;AACP,mBAAO,KAAK,YAAY,KAAK;AAAA,iBACxB;AACL,oBAAQ,KAAK;AAAA;AAAA;AAAA,iBAGR,EAAE,WAAW,KAAK,KAAK,uBAAuB;AACvD,mBAAW,kBAAkB,SAAO;AAClC,cAAI,KAAK;AACP,mBAAO,KAAK,YAAY,KAAK;AAAA,iBACxB;AACL,oBAAQ,KAAK;AAAA;AAAA;AAAA,iBAGR,EAAE,WAAW,KAAK,KAAK,yBAAyB;AACzD,mBAAW,oBAAoB,SAAO;AACpC,cAAI,KAAK;AACP,mBAAO,KAAK,YAAY,KAAK;AAAA,iBACxB;AACL,oBAAQ,KAAK;AAAA;AAAA;AAAA,iBAGR,EAAE,WAAW,KAAK,KAAK,qBAAqB;AACrD,mBAAW,kBAAkB,SAAO;AAClC,cAAI,KAAK;AACP,mBAAO,KAAK,YAAY,KAAK;AAAA,iBACxB;AACL,uBAAW,iBAAiB,UAAO;AACjC,kBAAI,MAAK;AACP,uBAAO,KAAK,YAAY,MAAK;AAAA,qBACxB;AACL,wBAAQ,KAAK;AAAA;AAAA;AAAA;AAAA,WAIlB,KAAK,QAAQ,YAAY;AAAA,aACvB;AACL,cAAM,SAAS;AACf,YAAI,YAAY;AACd,YAAE,OAAO,YAAY,CAAC,OAAO,QAAQ;AACnC,kBAAM,QAAQ,KAAK,qBAAqB,OAAO;AAC/C,mBAAO,KAAK;AAAA;AAAA;AAGhB,cAAM,MAAM,KAAK,IAAI;AACrB,YAAI,SAAS,KAAK;AAClB,YAAK,MAAK,mBAAmB,EAAE,WAAW,KAAK,eAC3C,IAAI,QAAQ,UAAU,OAAO,IAAK;AACpC,cAAI,KAAK,IAAI,OAAO,KAAK,IAAI,SAAS,OAAO,KAAK;AAChD,qBAAS,KAAK,IAAI,MAAM,GAAG,KAAK,IAAI,SAAS;AAAA;AAE/C,oBAAU;AAAA;AAGZ,mBAAW,QAAQ,QAAQ,CAAC,KAAK,SAAS;AACxC,cAAI,KAAK;AACP,mBAAO,KAAK,YAAY,KAAK;AAAA;AAG/B,eAAK,QAAQ,QAAQ,CAAC,MAAK,QAAQ,cAAc;AAC/C,kBAAM,YAAY,KAAK,WAAW,QAAQ,cAAc,UAAU,aAAa,KAAK,QAAQ,YAAY,EAAE,SAAS,MAAM,aAAa,cAAc;AAEpJ,gBAAI,WAAW;AACb,mBAAK,UAAU,IAAI,aAAa,KAAK,WAAW,QAAQ,eAAe,UAAU,aAAa,KAAK,QAAQ,YAAY,EAAE,SAAS,MAAM,aAAa,cAAc,MAAM,KAAK,QAAQ,YAAY,KAAK;AAAA;AAGzM,gBAAI,QAAO,KAAI,SAAS;AACtB,qBAAM,KAAK,eAAe,MAAK,KAAK,KAAK;AACzC,kBAAI,SAAQ,MAAM;AAChB,qBAAK;AACL,wBAAQ,KAAK,cAAc,IAAI;AAAA;AAAA;AAGnC,gBAAI,MAAK;AACP,mBAAI,MAAM;AACV,mBAAK;AACL,qBAAO,KAAK,YAAY,MAAK,UAAU,YAAY;AAAA,mBAC9C;AACL,kBAAI,OAAO;AACX,kBAAI,WAAW;AACf,kBAAI,eAAe;AACnB,kBAAI,OAAO,WAAW,UAAU;AAC9B,oBAAI,EAAE,WAAW,KAAK,KAAK,iBAAiB;AAC1C,iCAAe,OAAO;AAAA,uBACjB;AACL,yBAAO,OAAO;AACd,6BAAW,OAAO;AAAA;AAEpB,uBAAO;AAAA;AAET,mBAAK;AACL,oBAAM,UAAU,KAAK;AACrB,kBAAI,UAAU,GAAG;AACf,sBAAM,WAAW;AACjB,yBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,2BAAS,SAAS,GAAG,iBACjB,SAAS,GAAG;AAAA;AAElB,yBAAS,IAAI,GAAG,IAAI,SAAS,KAAK;AAChC,6BAAW,UAAU,KAAK,IAAI;AAC5B,0BAAM,QAAQ,YAAY,IAAI,SAAS;AACvC,0BAAM,QAAQ,KAAK,GAAG;AACtB,wBAAI,UAAU,MAAM;AAClB,0BAAI,OAAO;AACT,6BAAK,GAAG,UAAU,MAAM;AAAA,iCACf,SAAS,YAAY,aAAa;AAC3C,6BAAK,GAAG,UAAU,IAAI,KAAK,OAAO,IAAI;AAAA,iCAC7B,SAAS,YAAY,QAAQ;AACtC,6BAAK,GAAG,UAAU,IAAI,OAAO,KAAK;AAAA,iCACzB,SAAS,QAAQ,QAAQ,kBAAkB,GAAG;AACvD,6BAAK,GAAG,UAAU,IAAI,OAAO,KAAK,OAAO;AAAA;AAAA;AAAA;AAAA;AAKjD,oBAAI,aAAa,UAAU,QAAQ;AACjC,uBAAK,QAAQ;AAAA;AAEf,wBAAQ,KAAK,cAAc,MAAM,SAAS,UAAU;AAAA,qBAC/C;AACL,wBAAQ,KAAK,cAAc,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QASzC,IAAI,KAAK,YAAY;AACzB,WAAO,MAAM,KAAK,KAAK,KAAK,YAAY,KAAK;AAAA;AAAA,SAGxC,qBAAqB,KAAK,QAAQ,SAAS;AAChD,QAAI,YAAY;AAChB,UAAM,kBAAkB,CAAC,OAAO,KAAK,YAAW;AAC9C,UAAI,QAAO,SAAS,QAAW;AAC7B,kBAAU,OAAO,QAAO;AACxB,eAAO;AAAA;AAET,aAAO;AAAA;AAET,UAAM,cAAc,qBAAqB,KAAK,QAAQ,SAAS,iBAAiB;AAChF,QAAI,MAAM,QAAQ,WAAW,OAAO,OAAO,OAAO,UAAU;AAC1D,kBAAY;AAAA;AAGd,WAAO,CAAC,KAAK;AAAA;AAAA,EAGf,eAAe,KAAK,KAAK,YAAY;AACnC,QAAI,IAAI,QAAQ,OAAO,eAAe,MAAM,EAAE,WAAW,KAAK,UAAU;AACtE,YAAM;AAAA,eACG,IAAI,QAAQ,OAAO,eAAe,IAAI;AAC/C,UAAI,KAAK,qBAAqB;AAE5B,mBAAW,UAAU;AAErB,mBAAW,UAAU,KAAK;AAAA;AAE5B,YAAM;AAAA,eACG,IAAI,QAAQ,OAAO,eAAe,IAAI;AAC/C,YAAM,QAAQ,IAAI,QAAQ,MAAM;AAChC,UAAI,SAAS,MAAM,SAAS,KAAK,MAAM,OAAO,SAAS;AACrD,YAAI;AACJ,cAAM,UAAU,MAAM,GAAG,MAAM;AAC/B,YAAI,QAAQ,IAAI;AACd,kBAAQ,IAAI,QAAQ,QAAQ,QAAQ;AAAA,eAC/B;AACL,kBAAQ,IAAI,QAAQ;AAAA;AAEtB,YAAI,WAAW,cAAc,OAAO;AAClC,qBAAW,UAAU,cAAc;AACnC,gBAAM,WAAW,UAAU;AAAA,eAExB;AACH,gBAAM;AAAA;AAAA,aAEH;AACL,cAAM;AAAA;AAAA,eAEC,IAAI,QAAQ,OAAO,eAAe,IAAI;AAC/C,UAAI,IAAI,QAAQ,OAAO,sBAAsB,IAAI;AAC/C,cAAM;AAAA;AAAA,eAEC,IAAI,QAAQ,OAAO,eAAe,IAAI;AAC/C,YAAM;AAAA,eACG,IAAI,QAAQ,OAAO,eAAe,MAC3C,EAAE,WAAW,KAAK,iBAAiB;AACnC,iBAAW,UAAU,uCAAuC,IAAI,UAAU,IAAI,MAAM,KAAK;AACzF,YAAM,WAAW,UAAU;AAAA;AAE7B,QAAI,OAAO,IAAI,WAAW,GAAG;AAAE,YAAM;AAAA;AACrC,WAAO;AAAA;AAAA,EAuBT,cAAc,MAAM,UAAU,UAAU,MAAM;AAC5C,QAAI,SAAS,KAAK;AAClB,QAAI,KAAK,cAAc,MAAM,WAAW;AACtC,WAAK,kBAAkB,MAAM;AAE7B,UAAI,CAAC,KAAK,UAAU;AAClB,YAAI,KAAK,QAAQ,OAAO;AACtB,gBAAM,SAAS,KAAK;AACpB,mBAAS,OAAO,OAAO,KAAK,QAAQ;AAAA,eAC/B;AACL,mBAAS;AAAA;AAAA;AAAA;AAKf,QAAI,KAAK,qBAAqB;AAC5B,eAAS;AAAA,eACA,KAAK,mBAAmB;AACjC,eAAS;AACT,iBAAW,WAAW,MAAM;AAC1B,YAAI,QAAQ,SAAS;AACnB,kBAAQ,UAAU,QAAQ,QAAQ,QAAQ,MAAM,IAAI,QAAQ,MAAM,IAAI,QAAQ,MAAM;AAAA;AAGtF,eAAO,QAAQ,QAAQ;AAAA,UACrB,MAAM,QAAQ,KAAK;AAAA,UACnB,WAAW,QAAQ,WAAW,MAAM,OAAO;AAAA,UAC3C,cAAc,QAAQ;AAAA,UACtB,YAAY,QAAQ,SAAS;AAAA,UAC7B,eAAe,QAAQ,eAAe,MAAM,OAAO;AAAA,UACnD,SAAS,QAAQ;AAAA;AAAA;AAAA,eAGZ,KAAK,sBAAsB;AACpC,eAAS,KAAK,uBAAuB;AAAA,eAC5B,KAAK,iBAAiB;AAC/B,eAAS,KAAK,kBAAkB;AAAA,eACvB,KAAK,iBAAiB;AAC/B,eAAS;AAAA,eACA,KAAK,qBAAqB;AACnC,eAAS,KAAK;AACd,UAAI,MAAM;AACR,cAAM,QAAQ;AACd,aAAK,UAAU;AAAA;AAAA,eAER,KAAK,eAAe;AAC7B,eAAS;AAAA,eACA,KAAK,qBAAqB;AACnC,eAAS,KAAK;AAAA,eACL,KAAK,qBAAqB;AACnC,eAAS;AAAA,eACA,KAAK,kBAAkB;AAChC,eAAS,KAAK,GAAG;AAAA,eACR,KAAK,sBAAsB;AACpC,eAAS;AAAA,eACA,KAAK,mBAAmB,KAAK,iBAAiB;AACvD,eAAS,CAAC,QAAQ;AAAA,eACT,KAAK,0BAA0B;AACxC,eAAS,KAAK,2BAA2B;AAAA,eAChC,KAAK,cAAc;AAE5B,eAAS,CAAC,MAAM;AAAA,WACX;AACL,eAAS;AAAA;AAGX,WAAO;AAAA;AAAA,EAGT,sBAAsB,SAAS;AAC7B,WAAO,QAAQ,IAAI,eAAa;AAC9B,aAAO;AAAA,QACL,WAAW,UAAU;AAAA,QACrB,QAAQ,UAAU;AAAA;AAAA;AAAA;AAAA,EAKxB,2BAA2B,MAAM;AAE/B,WAAO,EAAE,OAAO,MAAM,gBAAc;AAClC,aAAO,CAAC,EAAE,WAAW,WAAW,gBAAgB;AAAA;AAAA;AAAA,EAIpD,YAAY,KAAK,UAAU,MAAM,YAAY;AAC3C,QAAI;AAEJ,QAAI,CAAE,QAAO,IAAI,UAAU;AACzB,UAAI,aAAa;AAAA;AAGnB,YAAQ,IAAI,QAAQ,MAAM;AAC1B,QAAI,SAAS,MAAM,SAAS,GAAG;AAC7B,UAAI,kBAAkB;AACtB,UAAI,YAAY;AAChB,YAAM,SAAS;AACf,UAAI,UAAU,IAAI;AAClB,YAAM,QAAQ,mDAAmD,MAAM,uBAAuB,MAAM,sBAAsB,MAAM;AAEhI,UAAI,CAAC,CAAC,QAAQ,MAAM,SAAS,GAAG;AAC9B,0BAAkB,KAAK,UAAU;AACjC,0BAAkB,gBAAgB,GAAG;AAAA;AAGvC,UAAI,KAAK,SAAS,CAAC,CAAC,iBAAiB;AACnC,oBAAY,KAAK,MAAM,WAAW;AAAA;AAGpC,UAAI,CAAC,aAAa,KAAK,QAAQ,QAAQ;AACrC,oBAAY,KAAK,QAAQ,OAAO,MAAM,KAAK;AAAA;AAG7C,UAAI,WAAW;AACb,YAAI,KAAK,QAAQ,SACf,KAAK,QAAQ,MAAM,UAAU,YAAY,QAAW;AACpD,iBAAO,UAAU,UAAU,KAAK,QAAQ,MAAM,UAAU;AAAA,mBAC/C,KAAK,QAAQ,YAAY,KAAK,QAAQ,SAAS,cACxD,KAAK,QAAQ,SAAS,WAAW,UAAU,SAAS;AACpD,iBAAO,UAAU,UAAU,KAAK,QAAQ,SAAS,WAAW,UAAU;AAAA,mBAC7D,YAAY;AACrB,iBAAO,UAAU,UAAU,WAAW;AAAA;AAAA;AAI1C,UAAI,aAAa,CAAC,CAAC,UAAU,KAAK;AAChC,kBAAU,UAAU;AAAA;AAGtB,YAAM,SAAS;AACf,QAAE,OAAO,QAAQ,CAAC,OAAO,UAAU;AACjC,eAAO,KAAK,IAAI,gBAAgB,oBAC9B,KAAK,gCAAgC,QACrC,oBACA,OACA,OACA,KAAK,UACL;AAAA;AAIJ,aAAO,IAAI,gBAAgB,sBAAsB,EAAE,SAAS,QAAQ,QAAQ,KAAK,QAAQ,OAAO;AAAA;AAGlG,YAAQ,IAAI,QAAQ,MAAM,wGACxB,IAAI,QAAQ,MAAM,eAClB,IAAI,QAAQ,MAAM;AACpB,QAAI,SAAS,MAAM,SAAS,GAAG;AAC7B,aAAO,IAAI,gBAAgB,0BAA0B;AAAA,QACnD,QAAQ;AAAA,QACR,OAAO,MAAM;AAAA,QACb,QAAQ;AAAA,QACR,OAAO;AAAA;AAAA;AAIX,YAAQ,IAAI,QAAQ,MAAM;AAC1B,QAAI,SAAS,MAAM,SAAS,GAAG;AAC7B,YAAM,aAAa,MAAM;AACzB,UAAI,QAAQ,IAAI,IAAI,MAAM;AAC1B,cAAQ,QAAQ,MAAM,KAAK;AAE3B,aAAO,IAAI,gBAAgB,uBAAuB;AAAA,QAChD,SAAS,MAAM;AAAA,QACf;AAAA,QACA;AAAA,QACA,QAAQ;AAAA,QACR,OAAO;AAAA;AAAA;AAIX,WAAO,IAAI,gBAAgB,cAAc,KAAK,EAAE,OAAO;AAAA;AAAA,EAIzD,oBAAoB;AAClB,QAAI,SAAS;AAEb,QAAI,EAAE,WAAW,KAAK,KAAK,mCAAmC;AAC5D,eAAS;AAAA;AAEX,WAAO;AAAA;AAAA,EAGT,wBAAwB;AACtB,QAAI,SAAS;AAEb,aAAS,UAAU,KAAK,IAAI,cAAc,WAAW;AACrD,aAAS,UAAU,KAAK,IAAI,cAAc,WAAW;AACrD,aAAS,UAAU,KAAK,IAAI,cAAc,WAAW;AAErD,WAAO;AAAA;AAAA,EAET,qBAAqB;AACnB,QAAI,SAAS;AAEb,aAAS,UAAU,KAAK,IAAI,cAAc,WAAW;AACrD,aAAS,UAAU,KAAK,IAAI,WAAW;AACvC,WAAO;AAAA;AAAA,EAGT,uBAAuB,MAAM;AAC3B,QAAI;AACJ,UAAM,SAAS;AACf,SAAK,QAAQ,UAAQ;AACnB,UAAI,CAAC,YAAY,SAAS,SAAS,KAAK,UAAU;AAChD,mBAAW;AAAA,UACT,SAAS,KAAK,YAAY;AAAA,UAC1B,QAAQ;AAAA,UACR,MAAM,KAAK;AAAA,UACX,WAAW,KAAK;AAAA,UAChB,QAAQ,KAAK,YAAY;AAAA,UACzB,MAAM,KAAK;AAAA;AAGb,UAAE,QAAQ,KAAK,SAAS,QAAQ,SAAS,OAAK;AAAE,iBAAO,IAAK;AAAA,WAAQ,MAAM,MAAM,YAAU;AACxF,cAAI,aAAa,OAAO;AACxB,cAAK,YAAa;AAChB,yBAAa,WAAW,QAAQ,QAAQ;AACxC,qBAAS,OAAO,KAAK;AAAA,cACnB,WAAW;AAAA,cACX,QAAQ;AAAA,cACR,OAAO,OAAO,QAAQ,SAAS,KAAK,QAAQ;AAAA,cAC5C,SAAS;AAAA;AAAA;AAAA;AAIf,eAAO,KAAK;AAAA;AAAA;AAGhB,WAAO;AAAA;AAAA,EAGT,kBAAkB,SAAS,UAAU;AACnC,QAAI,KAAK,UAAU;AAEjB,YAAM,yBAAyB,KAAK,MAAM;AAC1C,UAAI,KAAK;AACT,UAAI,8BAA8B;AAElC,UAAI,OAAO,UAAU,eAAe,KAAK,KAAK,MAAM,eAAe,2BAC/D,KAAK,MAAM,cAAc,wBAAwB,UAAU;AAC7D,sCAA8B,KAAK,MAAM,cAAc,wBAAwB;AACjF,WAAK,MAAM,WAAW,QAAQ,GAAG,KAAK;AACtC,WAAK,MAAM,YAAY,SAAS,KAAK;AACrC,WAAK,MAAM,WAAW,QAAQ,GAAG;AACjC,WAAK,MAAM,+BAA+B,WAAW,QAAQ,GAAG;AAChE,WAAK,SAAS,0BAA0B;AAAA;AAAA;AAAA;AAK9C,OAAO,UAAU;AACjB,OAAO,QAAQ,QAAQ;AACvB,OAAO,QAAQ,UAAU;", "names": []}