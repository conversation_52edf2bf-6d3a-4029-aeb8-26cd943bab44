{"version": 3, "sources": ["../src/data-types.js"], "sourcesContent": ["'use strict';\n\nconst util = require('util');\nconst _ = require('lodash');\nconst wkx = require('wkx');\nconst sequelizeErrors = require('./errors');\nconst Validator = require('./utils/validator-extras').validator;\nconst momentTz = require('moment-timezone');\nconst moment = require('moment');\nconst { logger } = require('./utils/logger');\nconst warnings = {};\nconst { classToInvokable } = require('./utils/class-to-invokable');\nconst { joinSQLFragments } = require('./utils/join-sql-fragments');\n\nclass ABSTRACT {\n  toString(options) {\n    return this.toSql(options);\n  }\n  toSql() {\n    return this.key;\n  }\n  stringify(value, options) {\n    if (this._stringify) {\n      return this._stringify(value, options);\n    }\n    return value;\n  }\n  bindParam(value, options) {\n    if (this._bindParam) {\n      return this._bindParam(value, options);\n    }\n    return options.bindParam(this.stringify(value, options));\n  }\n  static toString() {\n    return this.name;\n  }\n  static warn(link, text) {\n    if (!warnings[text]) {\n      warnings[text] = true;\n      logger.warn(`${text} \\n>> Check: ${link}`);\n    }\n  }\n  static extend(oldType) {\n    return new this(oldType.options);\n  }\n}\n\nABSTRACT.prototype.dialectTypes = '';\n\n/**\n * STRING A variable length string\n */\nclass STRING extends ABSTRACT {\n  /**\n   * @param {number} [length=255] length of string\n   * @param {boolean} [binary=false] Is this binary?\n   */\n  constructor(length, binary) {\n    super();\n    const options = typeof length === 'object' && length || { length, binary };\n    this.options = options;\n    this._binary = options.binary;\n    this._length = options.length || 255;\n  }\n  toSql() {\n    return joinSQLFragments([\n      `VARCHAR(${this._length})`,\n      this._binary && 'BINARY'\n    ]);\n  }\n  validate(value) {\n    if (Object.prototype.toString.call(value) !== '[object String]') {\n      if (this.options.binary && Buffer.isBuffer(value) || typeof value === 'number') {\n        return true;\n      }\n      throw new sequelizeErrors.ValidationError(util.format('%j is not a valid string', value));\n    }\n    return true;\n  }\n\n  get BINARY() {\n    this._binary = true;\n    this.options.binary = true;\n    return this;\n  }\n\n  static get BINARY() {\n    return new this().BINARY;\n  }\n}\n\n/**\n * CHAR A fixed length string\n */\nclass CHAR extends STRING {\n  /**\n   * @param {number} [length=255] length of string\n   * @param {boolean} [binary=false] Is this binary?\n   */\n  constructor(length, binary) {\n    super(typeof length === 'object' && length || { length, binary });\n  }\n  toSql() {\n    return joinSQLFragments([\n      `CHAR(${this._length})`,\n      this._binary && 'BINARY'\n    ]);\n  }\n}\n\n/**\n * Unlimited length TEXT column\n */\nclass TEXT extends ABSTRACT {\n  /**\n   * @param {string} [length=''] could be tiny, medium, long.\n   */\n  constructor(length) {\n    super();\n    const options = typeof length === 'object' && length || { length };\n    this.options = options;\n    this._length = options.length || '';\n  }\n  toSql() {\n    switch (this._length.toLowerCase()) {\n      case 'tiny':\n        return 'TINYTEXT';\n      case 'medium':\n        return 'MEDIUMTEXT';\n      case 'long':\n        return 'LONGTEXT';\n      default:\n        return this.key;\n    }\n  }\n  validate(value) {\n    if (typeof value !== 'string') {\n      throw new sequelizeErrors.ValidationError(util.format('%j is not a valid string', value));\n    }\n    return true;\n  }\n}\n\n/**\n * An unlimited length case-insensitive text column.\n * Original case is preserved but acts case-insensitive when comparing values (such as when finding or unique constraints).\n * Only available in Postgres and SQLite.\n *\n */\nclass CITEXT extends ABSTRACT {\n  toSql() {\n    return 'CITEXT';\n  }\n  validate(value) {\n    if (typeof value !== 'string') {\n      throw new sequelizeErrors.ValidationError(util.format('%j is not a valid string', value));\n    }\n    return true;\n  }\n}\n\n/**\n * Base number type which is used to build other types\n */\nclass NUMBER extends ABSTRACT {\n  /**\n   * @param {object} options type options\n   * @param {string|number} [options.length] length of type, like `INT(4)`\n   * @param {boolean} [options.zerofill] Is zero filled?\n   * @param {boolean} [options.unsigned] Is unsigned?\n   * @param {string|number} [options.decimals] number of decimal points, used with length `FLOAT(5, 4)`\n   * @param {string|number} [options.precision] defines precision for decimal type\n   * @param {string|number} [options.scale] defines scale for decimal type\n   */\n  constructor(options = {}) {\n    super();\n    if (typeof options === 'number') {\n      options = {\n        length: options\n      };\n    }\n    this.options = options;\n    this._length = options.length;\n    this._zerofill = options.zerofill;\n    this._decimals = options.decimals;\n    this._precision = options.precision;\n    this._scale = options.scale;\n    this._unsigned = options.unsigned;\n  }\n  toSql() {\n    let result = this.key;\n    if (this._length) {\n      result += `(${this._length}`;\n      if (typeof this._decimals === 'number') {\n        result += `,${this._decimals}`;\n      }\n      result += ')';\n    }\n    if (this._unsigned) {\n      result += ' UNSIGNED';\n    }\n    if (this._zerofill) {\n      result += ' ZEROFILL';\n    }\n    return result;\n  }\n  validate(value) {\n    if (!Validator.isFloat(String(value))) {\n      throw new sequelizeErrors.ValidationError(util.format(`%j is not a valid ${this.key.toLowerCase()}`, value));\n    }\n    return true;\n  }\n  _stringify(number) {\n    if (typeof number === 'number' || typeof number === 'bigint' || typeof number === 'boolean' || number === null || number === undefined) {\n      return number;\n    }\n    if (typeof number.toString === 'function') {\n      return number.toString();\n    }\n    return number;\n  }\n\n  get UNSIGNED() {\n    this._unsigned = true;\n    this.options.unsigned = true;\n    return this;\n  }\n\n  get ZEROFILL() {\n    this._zerofill = true;\n    this.options.zerofill = true;\n    return this;\n  }\n\n  static get UNSIGNED() {\n    return new this().UNSIGNED;\n  }\n\n  static get ZEROFILL() {\n    return new this().ZEROFILL;\n  }\n}\n\n/**\n * A 32 bit integer\n */\nclass INTEGER extends NUMBER {\n  validate(value) {\n    if (!Validator.isInt(String(value))) {\n      throw new sequelizeErrors.ValidationError(util.format(`%j is not a valid ${this.key.toLowerCase()}`, value));\n    }\n    return true;\n  }\n}\n\n/**\n * A 8 bit integer\n */\nclass TINYINT extends INTEGER {\n}\n\n/**\n * A 16 bit integer\n */\nclass SMALLINT extends INTEGER {\n}\n\n/**\n * A 24 bit integer\n */\nclass MEDIUMINT extends INTEGER {\n}\n\n/**\n * A 64 bit integer\n */\nclass BIGINT extends INTEGER {\n}\n\n/**\n * Floating point number (4-byte precision).\n */\nclass FLOAT extends NUMBER {\n  /**\n   * @param {string|number} [length] length of type, like `FLOAT(4)`\n   * @param {string|number} [decimals] number of decimal points, used with length `FLOAT(5, 4)`\n   */\n  constructor(length, decimals) {\n    super(typeof length === 'object' && length || { length, decimals });\n  }\n  validate(value) {\n    if (!Validator.isFloat(String(value))) {\n      throw new sequelizeErrors.ValidationError(util.format('%j is not a valid float', value));\n    }\n    return true;\n  }\n}\n\n/**\n * Floating point number (4-byte precision).\n */\nclass REAL extends NUMBER {\n  /**\n   * @param {string|number} [length] length of type, like `REAL(4)`\n   * @param {string|number} [decimals] number of decimal points, used with length `REAL(5, 4)`\n   */\n  constructor(length, decimals) {\n    super(typeof length === 'object' && length || { length, decimals });\n  }\n}\n\n/**\n * Floating point number (8-byte precision).\n */\nclass DOUBLE extends NUMBER {\n  /**\n   * @param {string|number} [length] length of type, like `DOUBLE PRECISION(25)`\n   * @param {string|number} [decimals] number of decimal points, used with length `DOUBLE PRECISION(25, 10)`\n   */\n  constructor(length, decimals) {\n    super(typeof length === 'object' && length || { length, decimals });\n  }\n}\n\n/**\n * Decimal type, variable precision, take length as specified by user\n */\nclass DECIMAL extends NUMBER {\n  /**\n   * @param {string|number} [precision] defines precision\n   * @param {string|number} [scale] defines scale\n   */\n  constructor(precision, scale) {\n    super(typeof precision === 'object' && precision || { precision, scale });\n  }\n  toSql() {\n    if (this._precision || this._scale) {\n      return `DECIMAL(${[this._precision, this._scale].filter(_.identity).join(',')})`;\n    }\n    return 'DECIMAL';\n  }\n  validate(value) {\n    if (!Validator.isDecimal(String(value))) {\n      throw new sequelizeErrors.ValidationError(util.format('%j is not a valid decimal', value));\n    }\n    return true;\n  }\n}\n\n// TODO: Create intermediate class\nconst protoExtensions = {\n  escape: false,\n  _value(value) {\n    if (isNaN(value)) {\n      return 'NaN';\n    }\n    if (!isFinite(value)) {\n      const sign = value < 0 ? '-' : '';\n      return `${sign}Infinity`;\n    }\n\n    return value;\n  },\n  _stringify(value) {\n    return `'${this._value(value)}'`;\n  },\n  _bindParam(value, options) {\n    return options.bindParam(this._value(value));\n  }\n};\n\nfor (const floating of [FLOAT, DOUBLE, REAL]) {\n  Object.assign(floating.prototype, protoExtensions);\n}\n\n/**\n * A boolean / tinyint column, depending on dialect\n */\nclass BOOLEAN extends ABSTRACT {\n  toSql() {\n    return 'TINYINT(1)';\n  }\n  validate(value) {\n    if (!Validator.isBoolean(String(value))) {\n      throw new sequelizeErrors.ValidationError(util.format('%j is not a valid boolean', value));\n    }\n    return true;\n  }\n  _sanitize(value) {\n    if (value !== null && value !== undefined) {\n      if (Buffer.isBuffer(value) && value.length === 1) {\n        // Bit fields are returned as buffers\n        value = value[0];\n      }\n      const type = typeof value;\n      if (type === 'string') {\n        // Only take action on valid boolean strings.\n        return value === 'true' ? true : value === 'false' ? false : value;\n      }\n      if (type === 'number') {\n        // Only take action on valid boolean integers.\n        return value === 1 ? true : value === 0 ? false : value;\n      }\n    }\n    return value;\n  }\n}\n\n\nBOOLEAN.parse = BOOLEAN.prototype._sanitize;\n\n/**\n * A time column\n *\n */\nclass TIME extends ABSTRACT {\n  toSql() {\n    return 'TIME';\n  }\n}\n\n/**\n * Date column with timezone, default is UTC\n */\nclass DATE extends ABSTRACT {\n  /**\n   * @param {string|number} [length] precision to allow storing milliseconds\n   */\n  constructor(length) {\n    super();\n    const options = typeof length === 'object' && length || { length };\n    this.options = options;\n    this._length = options.length || '';\n  }\n  toSql() {\n    return 'DATETIME';\n  }\n  validate(value) {\n    if (!Validator.isDate(String(value))) {\n      throw new sequelizeErrors.ValidationError(util.format('%j is not a valid date', value));\n    }\n    return true;\n  }\n  _sanitize(value, options) {\n    if ((!options || options && !options.raw) && !(value instanceof Date) && !!value) {\n      return new Date(value);\n    }\n    return value;\n  }\n  _isChanged(value, originalValue) {\n    if (originalValue && !!value &&\n      (value === originalValue ||\n        value instanceof Date && originalValue instanceof Date && value.getTime() === originalValue.getTime())) {\n      return false;\n    }\n    // not changed when set to same empty value\n    if (!originalValue && !value && originalValue === value) {\n      return false;\n    }\n    return true;\n  }\n  _applyTimezone(date, options) {\n    if (options.timezone) {\n      if (momentTz.tz.zone(options.timezone)) {\n        return momentTz(date).tz(options.timezone);\n      }\n      return date = moment(date).utcOffset(options.timezone);\n    }\n    return momentTz(date);\n  }\n  _stringify(date, options) {\n    if (!moment.isMoment(date)) {\n      date = this._applyTimezone(date, options);\n    }\n    // Z here means current timezone, _not_ UTC\n    return date.format('YYYY-MM-DD HH:mm:ss.SSS Z');\n  }\n}\n\n/**\n * A date only column (no timestamp)\n */\nclass DATEONLY extends ABSTRACT {\n  toSql() {\n    return 'DATE';\n  }\n  _stringify(date) {\n    return moment(date).format('YYYY-MM-DD');\n  }\n  _sanitize(value, options) {\n    if ((!options || options && !options.raw) && !!value) {\n      return moment(value).format('YYYY-MM-DD');\n    }\n    return value;\n  }\n  _isChanged(value, originalValue) {\n    if (originalValue && !!value && originalValue === value) {\n      return false;\n    }\n    // not changed when set to same empty value\n    if (!originalValue && !value && originalValue === value) {\n      return false;\n    }\n    return true;\n  }\n}\n\n/**\n * A key / value store column. Only available in Postgres.\n */\nclass HSTORE extends ABSTRACT {\n  validate(value) {\n    if (!_.isPlainObject(value)) {\n      throw new sequelizeErrors.ValidationError(util.format('%j is not a valid hstore', value));\n    }\n    return true;\n  }\n}\n\n/**\n * A JSON string column. Available in MySQL, Postgres and SQLite\n */\nclass JSONTYPE extends ABSTRACT {\n  validate() {\n    return true;\n  }\n  _stringify(value) {\n    return JSON.stringify(value);\n  }\n}\n\n/**\n * A binary storage JSON column. Only available in Postgres.\n */\nclass JSONB extends JSONTYPE {\n}\n\n/**\n * A default value of the current timestamp\n */\nclass NOW extends ABSTRACT {\n}\n\n/**\n * Binary storage\n */\nclass BLOB extends ABSTRACT {\n  /**\n   * @param {string} [length=''] could be tiny, medium, long.\n   */\n  constructor(length) {\n    super();\n    const options = typeof length === 'object' && length || { length };\n    this.options = options;\n    this._length = options.length || '';\n  }\n  toSql() {\n    switch (this._length.toLowerCase()) {\n      case 'tiny':\n        return 'TINYBLOB';\n      case 'medium':\n        return 'MEDIUMBLOB';\n      case 'long':\n        return 'LONGBLOB';\n      default:\n        return this.key;\n    }\n  }\n  validate(value) {\n    if (typeof value !== 'string' && !Buffer.isBuffer(value)) {\n      throw new sequelizeErrors.ValidationError(util.format('%j is not a valid blob', value));\n    }\n    return true;\n  }\n  _stringify(value) {\n    if (!Buffer.isBuffer(value)) {\n      if (Array.isArray(value)) {\n        value = Buffer.from(value);\n      }\n      else {\n        value = Buffer.from(value.toString());\n      }\n    }\n    const hex = value.toString('hex');\n    return this._hexify(hex);\n  }\n  _hexify(hex) {\n    return `X'${hex}'`;\n  }\n  _bindParam(value, options) {\n    if (!Buffer.isBuffer(value)) {\n      if (Array.isArray(value)) {\n        value = Buffer.from(value);\n      }\n      else {\n        value = Buffer.from(value.toString());\n      }\n    }\n    return options.bindParam(value);\n  }\n}\n\n\nBLOB.prototype.escape = false;\n\n/**\n * Range types are data types representing a range of values of some element type (called the range's subtype).\n * Only available in Postgres. See [the Postgres documentation](http://www.postgresql.org/docs/9.4/static/rangetypes.html) for more details\n */\nclass RANGE extends ABSTRACT {\n  /**\n   * @param {ABSTRACT} subtype A subtype for range, like RANGE(DATE)\n   */\n  constructor(subtype) {\n    super();\n    const options = _.isPlainObject(subtype) ? subtype : { subtype };\n    if (!options.subtype)\n      options.subtype = new INTEGER();\n    if (typeof options.subtype === 'function') {\n      options.subtype = new options.subtype();\n    }\n    this._subtype = options.subtype.key;\n    this.options = options;\n  }\n  validate(value) {\n    if (!Array.isArray(value)) {\n      throw new sequelizeErrors.ValidationError(util.format('%j is not a valid range', value));\n    }\n    if (value.length !== 2) {\n      throw new sequelizeErrors.ValidationError('A range must be an array with two elements');\n    }\n    return true;\n  }\n}\n\n/**\n * A column storing a unique universal identifier.\n * Use with `UUIDV1` or `UUIDV4` for default values.\n */\nclass UUID extends ABSTRACT {\n  validate(value, options) {\n    if (typeof value !== 'string' || !Validator.isUUID(value) && (!options || !options.acceptStrings)) {\n      throw new sequelizeErrors.ValidationError(util.format('%j is not a valid uuid', value));\n    }\n    return true;\n  }\n}\n\n/**\n * A default unique universal identifier generated following the UUID v1 standard\n */\nclass UUIDV1 extends ABSTRACT {\n  validate(value, options) {\n    if (typeof value !== 'string' || !Validator.isUUID(value) && (!options || !options.acceptStrings)) {\n      throw new sequelizeErrors.ValidationError(util.format('%j is not a valid uuid', value));\n    }\n    return true;\n  }\n}\n\n/**\n * A default unique universal identifier generated following the UUID v4 standard\n */\nclass UUIDV4 extends ABSTRACT {\n  validate(value, options) {\n    if (typeof value !== 'string' || !Validator.isUUID(value, 4) && (!options || !options.acceptStrings)) {\n      throw new sequelizeErrors.ValidationError(util.format('%j is not a valid uuidv4', value));\n    }\n    return true;\n  }\n}\n\n/**\n * A virtual value that is not stored in the DB. This could for example be useful if you want to provide a default value in your model that is returned to the user but not stored in the DB.\n *\n * You could also use it to validate a value before permuting and storing it. VIRTUAL also takes a return type and dependency fields as arguments\n * If a virtual attribute is present in `attributes` it will automatically pull in the extra fields as well.\n * Return type is mostly useful for setups that rely on types like GraphQL.\n *\n * @example <caption>Checking password length before hashing it</caption>\n * sequelize.define('user', {\n *   password_hash: DataTypes.STRING,\n *   password: {\n *     type: DataTypes.VIRTUAL,\n *     set: function (val) {\n *        // Remember to set the data value, otherwise it won't be validated\n *        this.setDataValue('password', val);\n *        this.setDataValue('password_hash', this.salt + val);\n *      },\n *      validate: {\n *         isLongEnough: function (val) {\n *           if (val.length < 7) {\n *             throw new Error(\"Please choose a longer password\")\n *          }\n *       }\n *     }\n *   }\n * })\n *\n * # In the above code the password is stored plainly in the password field so it can be validated, but is never stored in the DB.\n *\n * @example <caption>Virtual with dependency fields</caption>\n * {\n *   active: {\n *     type: new DataTypes.VIRTUAL(DataTypes.BOOLEAN, ['createdAt']),\n *     get: function() {\n *       return this.get('createdAt') > Date.now() - (7 * 24 * 60 * 60 * 1000)\n *     }\n *   }\n * }\n *\n */\nclass VIRTUAL extends ABSTRACT {\n  /**\n   * @param {ABSTRACT} [ReturnType] return type for virtual type\n   * @param {Array} [fields] array of fields this virtual type is dependent on\n   */\n  constructor(ReturnType, fields) {\n    super();\n    if (typeof ReturnType === 'function')\n      ReturnType = new ReturnType();\n    this.returnType = ReturnType;\n    this.fields = fields;\n  }\n}\n\n/**\n * An enumeration, Postgres Only\n *\n * @example\n * DataTypes.ENUM('value', 'another value')\n * DataTypes.ENUM(['value', 'another value'])\n * DataTypes.ENUM({\n *   values: ['value', 'another value']\n * })\n */\nclass ENUM extends ABSTRACT {\n  /**\n   * @param {...any|{ values: any[] }|any[]} args either array of values or options object with values array. It also supports variadic values\n   */\n  constructor(...args) {\n    super();\n    const value = args[0];\n    const options = typeof value === 'object' && !Array.isArray(value) && value || {\n      values: args.reduce((result, element) => {\n        return result.concat(Array.isArray(element) ? element : [element]);\n      }, [])\n    };\n    this.values = options.values;\n    this.options = options;\n  }\n  validate(value) {\n    if (!this.values.includes(value)) {\n      throw new sequelizeErrors.ValidationError(util.format('%j is not a valid choice in %j', value, this.values));\n    }\n    return true;\n  }\n}\n\n/**\n * An array of `type`. Only available in Postgres.\n *\n * @example\n * DataTypes.ARRAY(DataTypes.DECIMAL)\n */\nclass ARRAY extends ABSTRACT {\n  /**\n   * @param {ABSTRACT} type type of array values\n   */\n  constructor(type) {\n    super();\n    const options = _.isPlainObject(type) ? type : { type };\n    this.options = options;\n    this.type = typeof options.type === 'function' ? new options.type() : options.type;\n  }\n  toSql() {\n    return `${this.type.toSql()}[]`;\n  }\n  validate(value) {\n    if (!Array.isArray(value)) {\n      throw new sequelizeErrors.ValidationError(util.format('%j is not a valid array', value));\n    }\n    return true;\n  }\n  static is(obj, type) {\n    return obj instanceof ARRAY && obj.type instanceof type;\n  }\n}\n\n/**\n * A column storing Geometry information.\n * It is only available in PostgreSQL (with PostGIS), MariaDB or MySQL.\n *\n * GeoJSON is accepted as input and returned as output.\n *\n * In PostGIS, the GeoJSON is parsed using the PostGIS function `ST_GeomFromGeoJSON`.\n * In MySQL it is parsed using the function `ST_GeomFromText`.\n *\n * Therefore, one can just follow the [GeoJSON spec](https://tools.ietf.org/html/rfc7946) for handling geometry objects.  See the following examples:\n *\n * @example <caption>Defining a Geometry type attribute</caption>\n * DataTypes.GEOMETRY\n * DataTypes.GEOMETRY('POINT')\n * DataTypes.GEOMETRY('POINT', 4326)\n *\n * @example <caption>Create a new point</caption>\n * const point = { type: 'Point', coordinates: [-76.984722, 39.807222]}; // GeoJson format: [lng, lat]\n *\n * User.create({username: 'username', geometry: point });\n *\n * @example <caption>Create a new linestring</caption>\n * const line = { type: 'LineString', 'coordinates': [ [100.0, 0.0], [101.0, 1.0] ] };\n *\n * User.create({username: 'username', geometry: line });\n *\n * @example <caption>Create a new polygon</caption>\n * const polygon = { type: 'Polygon', coordinates: [\n *                 [ [100.0, 0.0], [101.0, 0.0], [101.0, 1.0],\n *                   [100.0, 1.0], [100.0, 0.0] ]\n *                 ]};\n *\n * User.create({username: 'username', geometry: polygon });\n *\n * @example <caption>Create a new point with a custom SRID</caption>\n * const point = {\n *   type: 'Point',\n *   coordinates: [-76.984722, 39.807222], // GeoJson format: [lng, lat]\n *   crs: { type: 'name', properties: { name: 'EPSG:4326'} }\n * };\n *\n * User.create({username: 'username', geometry: point })\n *\n *\n * @see {@link DataTypes.GEOGRAPHY}\n */\nclass GEOMETRY extends ABSTRACT {\n  /**\n   * @param {string} [type] Type of geometry data\n   * @param {string} [srid] SRID of type\n   */\n  constructor(type, srid) {\n    super();\n    const options = _.isPlainObject(type) ? type : { type, srid };\n    this.options = options;\n    this.type = options.type;\n    this.srid = options.srid;\n  }\n  _stringify(value, options) {\n    return `ST_GeomFromText(${options.escape(wkx.Geometry.parseGeoJSON(value).toWkt())})`;\n  }\n  _bindParam(value, options) {\n    return `ST_GeomFromText(${options.bindParam(wkx.Geometry.parseGeoJSON(value).toWkt())})`;\n  }\n}\n\nGEOMETRY.prototype.escape = false;\n\n/**\n * A geography datatype represents two dimensional spacial objects in an elliptic coord system.\n *\n * __The difference from geometry and geography type:__\n *\n * PostGIS 1.5 introduced a new spatial type called geography, which uses geodetic measurement instead of Cartesian measurement.\n * Coordinate points in the geography type are always represented in WGS 84 lon lat degrees (SRID 4326),\n * but measurement functions and relationships ST_Distance, ST_DWithin, ST_Length, and ST_Area always return answers in meters or assume inputs in meters.\n *\n * __What is best to use? It depends:__\n *\n * When choosing between the geometry and geography type for data storage, you should consider what you’ll be using it for.\n * If all you do are simple measurements and relationship checks on your data, and your data covers a fairly large area, then most likely you’ll be better off storing your data using the new geography type.\n * Although the new geography data type can cover the globe, the geometry type is far from obsolete.\n * The geometry type has a much richer set of functions than geography, relationship checks are generally faster, and it has wider support currently across desktop and web-mapping tools\n *\n * @example <caption>Defining a Geography type attribute</caption>\n * DataTypes.GEOGRAPHY\n * DataTypes.GEOGRAPHY('POINT')\n * DataTypes.GEOGRAPHY('POINT', 4326)\n */\nclass GEOGRAPHY extends ABSTRACT {\n  /**\n   * @param {string} [type] Type of geography data\n   * @param {string} [srid] SRID of type\n   */\n  constructor(type, srid) {\n    super();\n    const options = _.isPlainObject(type) ? type : { type, srid };\n    this.options = options;\n    this.type = options.type;\n    this.srid = options.srid;\n  }\n  _stringify(value, options) {\n    return `ST_GeomFromText(${options.escape(wkx.Geometry.parseGeoJSON(value).toWkt())})`;\n  }\n  _bindParam(value, options) {\n    return `ST_GeomFromText(${options.bindParam(wkx.Geometry.parseGeoJSON(value).toWkt())})`;\n  }\n}\n\n\nGEOGRAPHY.prototype.escape = false;\n\n/**\n * The cidr type holds an IPv4 or IPv6 network specification. Takes 7 or 19 bytes.\n *\n * Only available for Postgres\n */\nclass CIDR extends ABSTRACT {\n  validate(value) {\n    if (typeof value !== 'string' || !Validator.isIPRange(value)) {\n      throw new sequelizeErrors.ValidationError(util.format('%j is not a valid CIDR', value));\n    }\n    return true;\n  }\n}\n\n/**\n * The INET type holds an IPv4 or IPv6 host address, and optionally its subnet. Takes 7 or 19 bytes\n *\n * Only available for Postgres\n */\nclass INET extends ABSTRACT {\n  validate(value) {\n    if (typeof value !== 'string' || !Validator.isIP(value)) {\n      throw new sequelizeErrors.ValidationError(util.format('%j is not a valid INET', value));\n    }\n    return true;\n  }\n}\n\n/**\n * The MACADDR type stores MAC addresses. Takes 6 bytes\n *\n * Only available for Postgres\n *\n */\nclass MACADDR extends ABSTRACT {\n  validate(value) {\n    if (typeof value !== 'string' || !Validator.isMACAddress(value)) {\n      throw new sequelizeErrors.ValidationError(util.format('%j is not a valid MACADDR', value));\n    }\n    return true;\n  }\n}\n\n/**\n * The TSVECTOR type stores text search vectors.\n *\n * Only available for Postgres\n *\n */\nclass TSVECTOR extends ABSTRACT {\n  validate(value) {\n    if (typeof value !== 'string') {\n      throw new sequelizeErrors.ValidationError(util.format('%j is not a valid string', value));\n    }\n    return true;\n  }\n}\n\n/**\n * A convenience class holding commonly used data types. The data types are used when defining a new model using `Sequelize.define`, like this:\n * ```js\n * sequelize.define('model', {\n *   column: DataTypes.INTEGER\n * })\n * ```\n * When defining a model you can just as easily pass a string as type, but often using the types defined here is beneficial. For example, using `DataTypes.BLOB`, mean\n * that that column will be returned as an instance of `Buffer` when being fetched by sequelize.\n *\n * To provide a length for the data type, you can invoke it like a function: `INTEGER(2)`\n *\n * Some data types have special properties that can be accessed in order to change the data type.\n * For example, to get an unsigned integer with zerofill you can do `DataTypes.INTEGER.UNSIGNED.ZEROFILL`.\n * The order you access the properties in do not matter, so `DataTypes.INTEGER.ZEROFILL.UNSIGNED` is fine as well.\n *\n * * All number types (`INTEGER`, `BIGINT`, `FLOAT`, `DOUBLE`, `REAL`, `DECIMAL`) expose the properties `UNSIGNED` and `ZEROFILL`\n * * The `CHAR` and `STRING` types expose the `BINARY` property\n *\n * Three of the values provided here (`NOW`, `UUIDV1` and `UUIDV4`) are special default values, that should not be used to define types. Instead they are used as shorthands for\n * defining default values. For example, to get a uuid field with a default value generated following v1 of the UUID standard:\n * ```js\n * sequelize.define('model', {\n *   uuid: {\n *     type: DataTypes.UUID,\n *     defaultValue: DataTypes.UUIDV1,\n *     primaryKey: true\n *   }\n * })\n * ```\n * There may be times when you want to generate your own UUID conforming to some other algorithm. This is accomplished\n * using the defaultValue property as well, but instead of specifying one of the supplied UUID types, you return a value\n * from a function.\n * ```js\n * sequelize.define('model', {\n *   uuid: {\n *     type: DataTypes.UUID,\n *     defaultValue: function() {\n *       return generateMyId()\n *     },\n *     primaryKey: true\n *   }\n * })\n * ```\n */\nconst DataTypes = module.exports = {\n  ABSTRACT,\n  STRING,\n  CHAR,\n  TEXT,\n  NUMBER,\n  TINYINT,\n  SMALLINT,\n  MEDIUMINT,\n  INTEGER,\n  BIGINT,\n  FLOAT,\n  TIME,\n  DATE,\n  DATEONLY,\n  BOOLEAN,\n  NOW,\n  BLOB,\n  DECIMAL,\n  NUMERIC: DECIMAL,\n  UUID,\n  UUIDV1,\n  UUIDV4,\n  HSTORE,\n  JSON: JSONTYPE,\n  JSONB,\n  VIRTUAL,\n  ARRAY,\n  ENUM,\n  RANGE,\n  REAL,\n  'DOUBLE PRECISION': DOUBLE,\n  DOUBLE,\n  GEOMETRY,\n  GEOGRAPHY,\n  CIDR,\n  INET,\n  MACADDR,\n  CITEXT,\n  TSVECTOR\n};\n\n_.each(DataTypes, (dataType, name) => {\n  // guard for aliases\n  if (!Object.prototype.hasOwnProperty.call(dataType, 'key')) {\n    dataType.types = {};\n    dataType.key = dataType.prototype.key = name;\n  }\n});\n\nconst dialectMap = {};\ndialectMap.postgres = require('./dialects/postgres/data-types')(DataTypes);\ndialectMap.mysql = require('./dialects/mysql/data-types')(DataTypes);\ndialectMap.mariadb = require('./dialects/mariadb/data-types')(DataTypes);\ndialectMap.sqlite = require('./dialects/sqlite/data-types')(DataTypes);\ndialectMap.mssql = require('./dialects/mssql/data-types')(DataTypes);\ndialectMap.db2 = require('./dialects/db2/data-types')(DataTypes);\ndialectMap.snowflake = require('./dialects/snowflake/data-types')(DataTypes);\ndialectMap.oracle = require('./dialects/oracle/data-types')(DataTypes);\n\nconst dialectList = Object.values(dialectMap);\n\nfor (const dataTypes of dialectList) {\n  _.each(dataTypes, (DataType, key) => {\n    if (!DataType.key) {\n      DataType.key = DataType.prototype.key = key;\n    }\n  });\n}\n\n// Wrap all data types to not require `new`\nfor (const dataTypes of [DataTypes, ...dialectList]) {\n  _.each(dataTypes, (DataType, key) => {\n    dataTypes[key] = classToInvokable(DataType);\n  });\n}\n\nObject.assign(DataTypes, dialectMap);\n"], "mappings": ";AAEA,MAAM,OAAO,QAAQ;AACrB,MAAM,IAAI,QAAQ;AAClB,MAAM,MAAM,QAAQ;AACpB,MAAM,kBAAkB,QAAQ;AAChC,MAAM,YAAY,QAAQ,4BAA4B;AACtD,MAAM,WAAW,QAAQ;AACzB,MAAM,SAAS,QAAQ;AACvB,MAAM,EAAE,WAAW,QAAQ;AAC3B,MAAM,WAAW;AACjB,MAAM,EAAE,qBAAqB,QAAQ;AACrC,MAAM,EAAE,qBAAqB,QAAQ;AAErC,eAAe;AAAA,EACb,SAAS,SAAS;AAChB,WAAO,KAAK,MAAM;AAAA;AAAA,EAEpB,QAAQ;AACN,WAAO,KAAK;AAAA;AAAA,EAEd,UAAU,OAAO,SAAS;AACxB,QAAI,KAAK,YAAY;AACnB,aAAO,KAAK,WAAW,OAAO;AAAA;AAEhC,WAAO;AAAA;AAAA,EAET,UAAU,OAAO,SAAS;AACxB,QAAI,KAAK,YAAY;AACnB,aAAO,KAAK,WAAW,OAAO;AAAA;AAEhC,WAAO,QAAQ,UAAU,KAAK,UAAU,OAAO;AAAA;AAAA,SAE1C,WAAW;AAChB,WAAO,KAAK;AAAA;AAAA,SAEP,KAAK,MAAM,MAAM;AACtB,QAAI,CAAC,SAAS,OAAO;AACnB,eAAS,QAAQ;AACjB,aAAO,KAAK,GAAG;AAAA,YAAoB;AAAA;AAAA;AAAA,SAGhC,OAAO,SAAS;AACrB,WAAO,IAAI,KAAK,QAAQ;AAAA;AAAA;AAI5B,SAAS,UAAU,eAAe;AAKlC,qBAAqB,SAAS;AAAA,EAK5B,YAAY,QAAQ,QAAQ;AAC1B;AACA,UAAM,UAAU,OAAO,WAAW,YAAY,UAAU,EAAE,QAAQ;AAClE,SAAK,UAAU;AACf,SAAK,UAAU,QAAQ;AACvB,SAAK,UAAU,QAAQ,UAAU;AAAA;AAAA,EAEnC,QAAQ;AACN,WAAO,iBAAiB;AAAA,MACtB,WAAW,KAAK;AAAA,MAChB,KAAK,WAAW;AAAA;AAAA;AAAA,EAGpB,SAAS,OAAO;AACd,QAAI,OAAO,UAAU,SAAS,KAAK,WAAW,mBAAmB;AAC/D,UAAI,KAAK,QAAQ,UAAU,OAAO,SAAS,UAAU,OAAO,UAAU,UAAU;AAC9E,eAAO;AAAA;AAET,YAAM,IAAI,gBAAgB,gBAAgB,KAAK,OAAO,4BAA4B;AAAA;AAEpF,WAAO;AAAA;AAAA,MAGL,SAAS;AACX,SAAK,UAAU;AACf,SAAK,QAAQ,SAAS;AACtB,WAAO;AAAA;AAAA,aAGE,SAAS;AAClB,WAAO,IAAI,OAAO;AAAA;AAAA;AAOtB,mBAAmB,OAAO;AAAA,EAKxB,YAAY,QAAQ,QAAQ;AAC1B,UAAM,OAAO,WAAW,YAAY,UAAU,EAAE,QAAQ;AAAA;AAAA,EAE1D,QAAQ;AACN,WAAO,iBAAiB;AAAA,MACtB,QAAQ,KAAK;AAAA,MACb,KAAK,WAAW;AAAA;AAAA;AAAA;AAQtB,mBAAmB,SAAS;AAAA,EAI1B,YAAY,QAAQ;AAClB;AACA,UAAM,UAAU,OAAO,WAAW,YAAY,UAAU,EAAE;AAC1D,SAAK,UAAU;AACf,SAAK,UAAU,QAAQ,UAAU;AAAA;AAAA,EAEnC,QAAQ;AACN,YAAQ,KAAK,QAAQ;AAAA,WACd;AACH,eAAO;AAAA,WACJ;AACH,eAAO;AAAA,WACJ;AACH,eAAO;AAAA;AAEP,eAAO,KAAK;AAAA;AAAA;AAAA,EAGlB,SAAS,OAAO;AACd,QAAI,OAAO,UAAU,UAAU;AAC7B,YAAM,IAAI,gBAAgB,gBAAgB,KAAK,OAAO,4BAA4B;AAAA;AAEpF,WAAO;AAAA;AAAA;AAUX,qBAAqB,SAAS;AAAA,EAC5B,QAAQ;AACN,WAAO;AAAA;AAAA,EAET,SAAS,OAAO;AACd,QAAI,OAAO,UAAU,UAAU;AAC7B,YAAM,IAAI,gBAAgB,gBAAgB,KAAK,OAAO,4BAA4B;AAAA;AAEpF,WAAO;AAAA;AAAA;AAOX,qBAAqB,SAAS;AAAA,EAU5B,YAAY,UAAU,IAAI;AACxB;AACA,QAAI,OAAO,YAAY,UAAU;AAC/B,gBAAU;AAAA,QACR,QAAQ;AAAA;AAAA;AAGZ,SAAK,UAAU;AACf,SAAK,UAAU,QAAQ;AACvB,SAAK,YAAY,QAAQ;AACzB,SAAK,YAAY,QAAQ;AACzB,SAAK,aAAa,QAAQ;AAC1B,SAAK,SAAS,QAAQ;AACtB,SAAK,YAAY,QAAQ;AAAA;AAAA,EAE3B,QAAQ;AACN,QAAI,SAAS,KAAK;AAClB,QAAI,KAAK,SAAS;AAChB,gBAAU,IAAI,KAAK;AACnB,UAAI,OAAO,KAAK,cAAc,UAAU;AACtC,kBAAU,IAAI,KAAK;AAAA;AAErB,gBAAU;AAAA;AAEZ,QAAI,KAAK,WAAW;AAClB,gBAAU;AAAA;AAEZ,QAAI,KAAK,WAAW;AAClB,gBAAU;AAAA;AAEZ,WAAO;AAAA;AAAA,EAET,SAAS,OAAO;AACd,QAAI,CAAC,UAAU,QAAQ,OAAO,SAAS;AACrC,YAAM,IAAI,gBAAgB,gBAAgB,KAAK,OAAO,qBAAqB,KAAK,IAAI,iBAAiB;AAAA;AAEvG,WAAO;AAAA;AAAA,EAET,WAAW,QAAQ;AACjB,QAAI,OAAO,WAAW,YAAY,OAAO,WAAW,YAAY,OAAO,WAAW,aAAa,WAAW,QAAQ,WAAW,QAAW;AACtI,aAAO;AAAA;AAET,QAAI,OAAO,OAAO,aAAa,YAAY;AACzC,aAAO,OAAO;AAAA;AAEhB,WAAO;AAAA;AAAA,MAGL,WAAW;AACb,SAAK,YAAY;AACjB,SAAK,QAAQ,WAAW;AACxB,WAAO;AAAA;AAAA,MAGL,WAAW;AACb,SAAK,YAAY;AACjB,SAAK,QAAQ,WAAW;AACxB,WAAO;AAAA;AAAA,aAGE,WAAW;AACpB,WAAO,IAAI,OAAO;AAAA;AAAA,aAGT,WAAW;AACpB,WAAO,IAAI,OAAO;AAAA;AAAA;AAOtB,sBAAsB,OAAO;AAAA,EAC3B,SAAS,OAAO;AACd,QAAI,CAAC,UAAU,MAAM,OAAO,SAAS;AACnC,YAAM,IAAI,gBAAgB,gBAAgB,KAAK,OAAO,qBAAqB,KAAK,IAAI,iBAAiB;AAAA;AAEvG,WAAO;AAAA;AAAA;AAOX,sBAAsB,QAAQ;AAAA;AAM9B,uBAAuB,QAAQ;AAAA;AAM/B,wBAAwB,QAAQ;AAAA;AAMhC,qBAAqB,QAAQ;AAAA;AAM7B,oBAAoB,OAAO;AAAA,EAKzB,YAAY,QAAQ,UAAU;AAC5B,UAAM,OAAO,WAAW,YAAY,UAAU,EAAE,QAAQ;AAAA;AAAA,EAE1D,SAAS,OAAO;AACd,QAAI,CAAC,UAAU,QAAQ,OAAO,SAAS;AACrC,YAAM,IAAI,gBAAgB,gBAAgB,KAAK,OAAO,2BAA2B;AAAA;AAEnF,WAAO;AAAA;AAAA;AAOX,mBAAmB,OAAO;AAAA,EAKxB,YAAY,QAAQ,UAAU;AAC5B,UAAM,OAAO,WAAW,YAAY,UAAU,EAAE,QAAQ;AAAA;AAAA;AAO5D,qBAAqB,OAAO;AAAA,EAK1B,YAAY,QAAQ,UAAU;AAC5B,UAAM,OAAO,WAAW,YAAY,UAAU,EAAE,QAAQ;AAAA;AAAA;AAO5D,sBAAsB,OAAO;AAAA,EAK3B,YAAY,WAAW,OAAO;AAC5B,UAAM,OAAO,cAAc,YAAY,aAAa,EAAE,WAAW;AAAA;AAAA,EAEnE,QAAQ;AACN,QAAI,KAAK,cAAc,KAAK,QAAQ;AAClC,aAAO,WAAW,CAAC,KAAK,YAAY,KAAK,QAAQ,OAAO,EAAE,UAAU,KAAK;AAAA;AAE3E,WAAO;AAAA;AAAA,EAET,SAAS,OAAO;AACd,QAAI,CAAC,UAAU,UAAU,OAAO,SAAS;AACvC,YAAM,IAAI,gBAAgB,gBAAgB,KAAK,OAAO,6BAA6B;AAAA;AAErF,WAAO;AAAA;AAAA;AAKX,MAAM,kBAAkB;AAAA,EACtB,QAAQ;AAAA,EACR,OAAO,OAAO;AACZ,QAAI,MAAM,QAAQ;AAChB,aAAO;AAAA;AAET,QAAI,CAAC,SAAS,QAAQ;AACpB,YAAM,OAAO,QAAQ,IAAI,MAAM;AAC/B,aAAO,GAAG;AAAA;AAGZ,WAAO;AAAA;AAAA,EAET,WAAW,OAAO;AAChB,WAAO,IAAI,KAAK,OAAO;AAAA;AAAA,EAEzB,WAAW,OAAO,SAAS;AACzB,WAAO,QAAQ,UAAU,KAAK,OAAO;AAAA;AAAA;AAIzC,WAAW,YAAY,CAAC,OAAO,QAAQ,OAAO;AAC5C,SAAO,OAAO,SAAS,WAAW;AAAA;AAMpC,sBAAsB,SAAS;AAAA,EAC7B,QAAQ;AACN,WAAO;AAAA;AAAA,EAET,SAAS,OAAO;AACd,QAAI,CAAC,UAAU,UAAU,OAAO,SAAS;AACvC,YAAM,IAAI,gBAAgB,gBAAgB,KAAK,OAAO,6BAA6B;AAAA;AAErF,WAAO;AAAA;AAAA,EAET,UAAU,OAAO;AACf,QAAI,UAAU,QAAQ,UAAU,QAAW;AACzC,UAAI,OAAO,SAAS,UAAU,MAAM,WAAW,GAAG;AAEhD,gBAAQ,MAAM;AAAA;AAEhB,YAAM,OAAO,OAAO;AACpB,UAAI,SAAS,UAAU;AAErB,eAAO,UAAU,SAAS,OAAO,UAAU,UAAU,QAAQ;AAAA;AAE/D,UAAI,SAAS,UAAU;AAErB,eAAO,UAAU,IAAI,OAAO,UAAU,IAAI,QAAQ;AAAA;AAAA;AAGtD,WAAO;AAAA;AAAA;AAKX,QAAQ,QAAQ,QAAQ,UAAU;AAMlC,mBAAmB,SAAS;AAAA,EAC1B,QAAQ;AACN,WAAO;AAAA;AAAA;AAOX,mBAAmB,SAAS;AAAA,EAI1B,YAAY,QAAQ;AAClB;AACA,UAAM,UAAU,OAAO,WAAW,YAAY,UAAU,EAAE;AAC1D,SAAK,UAAU;AACf,SAAK,UAAU,QAAQ,UAAU;AAAA;AAAA,EAEnC,QAAQ;AACN,WAAO;AAAA;AAAA,EAET,SAAS,OAAO;AACd,QAAI,CAAC,UAAU,OAAO,OAAO,SAAS;AACpC,YAAM,IAAI,gBAAgB,gBAAgB,KAAK,OAAO,0BAA0B;AAAA;AAElF,WAAO;AAAA;AAAA,EAET,UAAU,OAAO,SAAS;AACxB,QAAK,EAAC,WAAW,WAAW,CAAC,QAAQ,QAAQ,CAAE,kBAAiB,SAAS,CAAC,CAAC,OAAO;AAChF,aAAO,IAAI,KAAK;AAAA;AAElB,WAAO;AAAA;AAAA,EAET,WAAW,OAAO,eAAe;AAC/B,QAAI,iBAAiB,CAAC,CAAC,SACpB,WAAU,iBACT,iBAAiB,QAAQ,yBAAyB,QAAQ,MAAM,cAAc,cAAc,YAAY;AAC1G,aAAO;AAAA;AAGT,QAAI,CAAC,iBAAiB,CAAC,SAAS,kBAAkB,OAAO;AACvD,aAAO;AAAA;AAET,WAAO;AAAA;AAAA,EAET,eAAe,MAAM,SAAS;AAC5B,QAAI,QAAQ,UAAU;AACpB,UAAI,SAAS,GAAG,KAAK,QAAQ,WAAW;AACtC,eAAO,SAAS,MAAM,GAAG,QAAQ;AAAA;AAEnC,aAAO,OAAO,OAAO,MAAM,UAAU,QAAQ;AAAA;AAE/C,WAAO,SAAS;AAAA;AAAA,EAElB,WAAW,MAAM,SAAS;AACxB,QAAI,CAAC,OAAO,SAAS,OAAO;AAC1B,aAAO,KAAK,eAAe,MAAM;AAAA;AAGnC,WAAO,KAAK,OAAO;AAAA;AAAA;AAOvB,uBAAuB,SAAS;AAAA,EAC9B,QAAQ;AACN,WAAO;AAAA;AAAA,EAET,WAAW,MAAM;AACf,WAAO,OAAO,MAAM,OAAO;AAAA;AAAA,EAE7B,UAAU,OAAO,SAAS;AACxB,QAAK,EAAC,WAAW,WAAW,CAAC,QAAQ,QAAQ,CAAC,CAAC,OAAO;AACpD,aAAO,OAAO,OAAO,OAAO;AAAA;AAE9B,WAAO;AAAA;AAAA,EAET,WAAW,OAAO,eAAe;AAC/B,QAAI,iBAAiB,CAAC,CAAC,SAAS,kBAAkB,OAAO;AACvD,aAAO;AAAA;AAGT,QAAI,CAAC,iBAAiB,CAAC,SAAS,kBAAkB,OAAO;AACvD,aAAO;AAAA;AAET,WAAO;AAAA;AAAA;AAOX,qBAAqB,SAAS;AAAA,EAC5B,SAAS,OAAO;AACd,QAAI,CAAC,EAAE,cAAc,QAAQ;AAC3B,YAAM,IAAI,gBAAgB,gBAAgB,KAAK,OAAO,4BAA4B;AAAA;AAEpF,WAAO;AAAA;AAAA;AAOX,uBAAuB,SAAS;AAAA,EAC9B,WAAW;AACT,WAAO;AAAA;AAAA,EAET,WAAW,OAAO;AAChB,WAAO,KAAK,UAAU;AAAA;AAAA;AAO1B,oBAAoB,SAAS;AAAA;AAM7B,kBAAkB,SAAS;AAAA;AAM3B,mBAAmB,SAAS;AAAA,EAI1B,YAAY,QAAQ;AAClB;AACA,UAAM,UAAU,OAAO,WAAW,YAAY,UAAU,EAAE;AAC1D,SAAK,UAAU;AACf,SAAK,UAAU,QAAQ,UAAU;AAAA;AAAA,EAEnC,QAAQ;AACN,YAAQ,KAAK,QAAQ;AAAA,WACd;AACH,eAAO;AAAA,WACJ;AACH,eAAO;AAAA,WACJ;AACH,eAAO;AAAA;AAEP,eAAO,KAAK;AAAA;AAAA;AAAA,EAGlB,SAAS,OAAO;AACd,QAAI,OAAO,UAAU,YAAY,CAAC,OAAO,SAAS,QAAQ;AACxD,YAAM,IAAI,gBAAgB,gBAAgB,KAAK,OAAO,0BAA0B;AAAA;AAElF,WAAO;AAAA;AAAA,EAET,WAAW,OAAO;AAChB,QAAI,CAAC,OAAO,SAAS,QAAQ;AAC3B,UAAI,MAAM,QAAQ,QAAQ;AACxB,gBAAQ,OAAO,KAAK;AAAA,aAEjB;AACH,gBAAQ,OAAO,KAAK,MAAM;AAAA;AAAA;AAG9B,UAAM,MAAM,MAAM,SAAS;AAC3B,WAAO,KAAK,QAAQ;AAAA;AAAA,EAEtB,QAAQ,KAAK;AACX,WAAO,KAAK;AAAA;AAAA,EAEd,WAAW,OAAO,SAAS;AACzB,QAAI,CAAC,OAAO,SAAS,QAAQ;AAC3B,UAAI,MAAM,QAAQ,QAAQ;AACxB,gBAAQ,OAAO,KAAK;AAAA,aAEjB;AACH,gBAAQ,OAAO,KAAK,MAAM;AAAA;AAAA;AAG9B,WAAO,QAAQ,UAAU;AAAA;AAAA;AAK7B,KAAK,UAAU,SAAS;AAMxB,oBAAoB,SAAS;AAAA,EAI3B,YAAY,SAAS;AACnB;AACA,UAAM,UAAU,EAAE,cAAc,WAAW,UAAU,EAAE;AACvD,QAAI,CAAC,QAAQ;AACX,cAAQ,UAAU,IAAI;AACxB,QAAI,OAAO,QAAQ,YAAY,YAAY;AACzC,cAAQ,UAAU,IAAI,QAAQ;AAAA;AAEhC,SAAK,WAAW,QAAQ,QAAQ;AAChC,SAAK,UAAU;AAAA;AAAA,EAEjB,SAAS,OAAO;AACd,QAAI,CAAC,MAAM,QAAQ,QAAQ;AACzB,YAAM,IAAI,gBAAgB,gBAAgB,KAAK,OAAO,2BAA2B;AAAA;AAEnF,QAAI,MAAM,WAAW,GAAG;AACtB,YAAM,IAAI,gBAAgB,gBAAgB;AAAA;AAE5C,WAAO;AAAA;AAAA;AAQX,mBAAmB,SAAS;AAAA,EAC1B,SAAS,OAAO,SAAS;AACvB,QAAI,OAAO,UAAU,YAAY,CAAC,UAAU,OAAO,UAAW,EAAC,WAAW,CAAC,QAAQ,gBAAgB;AACjG,YAAM,IAAI,gBAAgB,gBAAgB,KAAK,OAAO,0BAA0B;AAAA;AAElF,WAAO;AAAA;AAAA;AAOX,qBAAqB,SAAS;AAAA,EAC5B,SAAS,OAAO,SAAS;AACvB,QAAI,OAAO,UAAU,YAAY,CAAC,UAAU,OAAO,UAAW,EAAC,WAAW,CAAC,QAAQ,gBAAgB;AACjG,YAAM,IAAI,gBAAgB,gBAAgB,KAAK,OAAO,0BAA0B;AAAA;AAElF,WAAO;AAAA;AAAA;AAOX,qBAAqB,SAAS;AAAA,EAC5B,SAAS,OAAO,SAAS;AACvB,QAAI,OAAO,UAAU,YAAY,CAAC,UAAU,OAAO,OAAO,MAAO,EAAC,WAAW,CAAC,QAAQ,gBAAgB;AACpG,YAAM,IAAI,gBAAgB,gBAAgB,KAAK,OAAO,4BAA4B;AAAA;AAEpF,WAAO;AAAA;AAAA;AA4CX,sBAAsB,SAAS;AAAA,EAK7B,YAAY,YAAY,QAAQ;AAC9B;AACA,QAAI,OAAO,eAAe;AACxB,mBAAa,IAAI;AACnB,SAAK,aAAa;AAClB,SAAK,SAAS;AAAA;AAAA;AAclB,mBAAmB,SAAS;AAAA,EAI1B,eAAe,MAAM;AACnB;AACA,UAAM,QAAQ,KAAK;AACnB,UAAM,UAAU,OAAO,UAAU,YAAY,CAAC,MAAM,QAAQ,UAAU,SAAS;AAAA,MAC7E,QAAQ,KAAK,OAAO,CAAC,QAAQ,YAAY;AACvC,eAAO,OAAO,OAAO,MAAM,QAAQ,WAAW,UAAU,CAAC;AAAA,SACxD;AAAA;AAEL,SAAK,SAAS,QAAQ;AACtB,SAAK,UAAU;AAAA;AAAA,EAEjB,SAAS,OAAO;AACd,QAAI,CAAC,KAAK,OAAO,SAAS,QAAQ;AAChC,YAAM,IAAI,gBAAgB,gBAAgB,KAAK,OAAO,kCAAkC,OAAO,KAAK;AAAA;AAEtG,WAAO;AAAA;AAAA;AAUX,oBAAoB,SAAS;AAAA,EAI3B,YAAY,MAAM;AAChB;AACA,UAAM,UAAU,EAAE,cAAc,QAAQ,OAAO,EAAE;AACjD,SAAK,UAAU;AACf,SAAK,OAAO,OAAO,QAAQ,SAAS,aAAa,IAAI,QAAQ,SAAS,QAAQ;AAAA;AAAA,EAEhF,QAAQ;AACN,WAAO,GAAG,KAAK,KAAK;AAAA;AAAA,EAEtB,SAAS,OAAO;AACd,QAAI,CAAC,MAAM,QAAQ,QAAQ;AACzB,YAAM,IAAI,gBAAgB,gBAAgB,KAAK,OAAO,2BAA2B;AAAA;AAEnF,WAAO;AAAA;AAAA,SAEF,GAAG,KAAK,MAAM;AACnB,WAAO,eAAe,SAAS,IAAI,gBAAgB;AAAA;AAAA;AAkDvD,uBAAuB,SAAS;AAAA,EAK9B,YAAY,MAAM,MAAM;AACtB;AACA,UAAM,UAAU,EAAE,cAAc,QAAQ,OAAO,EAAE,MAAM;AACvD,SAAK,UAAU;AACf,SAAK,OAAO,QAAQ;AACpB,SAAK,OAAO,QAAQ;AAAA;AAAA,EAEtB,WAAW,OAAO,SAAS;AACzB,WAAO,mBAAmB,QAAQ,OAAO,IAAI,SAAS,aAAa,OAAO;AAAA;AAAA,EAE5E,WAAW,OAAO,SAAS;AACzB,WAAO,mBAAmB,QAAQ,UAAU,IAAI,SAAS,aAAa,OAAO;AAAA;AAAA;AAIjF,SAAS,UAAU,SAAS;AAuB5B,wBAAwB,SAAS;AAAA,EAK/B,YAAY,MAAM,MAAM;AACtB;AACA,UAAM,UAAU,EAAE,cAAc,QAAQ,OAAO,EAAE,MAAM;AACvD,SAAK,UAAU;AACf,SAAK,OAAO,QAAQ;AACpB,SAAK,OAAO,QAAQ;AAAA;AAAA,EAEtB,WAAW,OAAO,SAAS;AACzB,WAAO,mBAAmB,QAAQ,OAAO,IAAI,SAAS,aAAa,OAAO;AAAA;AAAA,EAE5E,WAAW,OAAO,SAAS;AACzB,WAAO,mBAAmB,QAAQ,UAAU,IAAI,SAAS,aAAa,OAAO;AAAA;AAAA;AAKjF,UAAU,UAAU,SAAS;AAO7B,mBAAmB,SAAS;AAAA,EAC1B,SAAS,OAAO;AACd,QAAI,OAAO,UAAU,YAAY,CAAC,UAAU,UAAU,QAAQ;AAC5D,YAAM,IAAI,gBAAgB,gBAAgB,KAAK,OAAO,0BAA0B;AAAA;AAElF,WAAO;AAAA;AAAA;AASX,mBAAmB,SAAS;AAAA,EAC1B,SAAS,OAAO;AACd,QAAI,OAAO,UAAU,YAAY,CAAC,UAAU,KAAK,QAAQ;AACvD,YAAM,IAAI,gBAAgB,gBAAgB,KAAK,OAAO,0BAA0B;AAAA;AAElF,WAAO;AAAA;AAAA;AAUX,sBAAsB,SAAS;AAAA,EAC7B,SAAS,OAAO;AACd,QAAI,OAAO,UAAU,YAAY,CAAC,UAAU,aAAa,QAAQ;AAC/D,YAAM,IAAI,gBAAgB,gBAAgB,KAAK,OAAO,6BAA6B;AAAA;AAErF,WAAO;AAAA;AAAA;AAUX,uBAAuB,SAAS;AAAA,EAC9B,SAAS,OAAO;AACd,QAAI,OAAO,UAAU,UAAU;AAC7B,YAAM,IAAI,gBAAgB,gBAAgB,KAAK,OAAO,4BAA4B;AAAA;AAEpF,WAAO;AAAA;AAAA;AAiDX,MAAM,YAAY,OAAO,UAAU;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,SAAS;AAAA,EACT;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,MAAM;AAAA,EACN;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,oBAAoB;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAGF,EAAE,KAAK,WAAW,CAAC,UAAU,SAAS;AAEpC,MAAI,CAAC,OAAO,UAAU,eAAe,KAAK,UAAU,QAAQ;AAC1D,aAAS,QAAQ;AACjB,aAAS,MAAM,SAAS,UAAU,MAAM;AAAA;AAAA;AAI5C,MAAM,aAAa;AACnB,WAAW,WAAW,QAAQ,kCAAkC;AAChE,WAAW,QAAQ,QAAQ,+BAA+B;AAC1D,WAAW,UAAU,QAAQ,iCAAiC;AAC9D,WAAW,SAAS,QAAQ,gCAAgC;AAC5D,WAAW,QAAQ,QAAQ,+BAA+B;AAC1D,WAAW,MAAM,QAAQ,6BAA6B;AACtD,WAAW,YAAY,QAAQ,mCAAmC;AAClE,WAAW,SAAS,QAAQ,gCAAgC;AAE5D,MAAM,cAAc,OAAO,OAAO;AAElC,WAAW,aAAa,aAAa;AACnC,IAAE,KAAK,WAAW,CAAC,UAAU,QAAQ;AACnC,QAAI,CAAC,SAAS,KAAK;AACjB,eAAS,MAAM,SAAS,UAAU,MAAM;AAAA;AAAA;AAAA;AAM9C,WAAW,aAAa,CAAC,WAAW,GAAG,cAAc;AACnD,IAAE,KAAK,WAAW,CAAC,UAAU,QAAQ;AACnC,cAAU,OAAO,iBAAiB;AAAA;AAAA;AAItC,OAAO,OAAO,WAAW;", "names": []}