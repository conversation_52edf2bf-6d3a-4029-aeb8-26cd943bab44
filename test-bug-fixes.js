// Test script to verify bug fixes
const http = require('http');

function makeRequest(path) {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: 'localhost',
            port: 3000,
            path: path,
            method: 'GET'
        };

        const req = http.request(options, (res) => {
            let body = '';
            res.on('data', (chunk) => {
                body += chunk;
            });
            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(body);
                    resolve({ status: res.statusCode, data: jsonData });
                } catch (e) {
                    resolve({ status: res.statusCode, data: body });
                }
            });
        });

        req.on('error', (err) => {
            reject(err);
        });

        req.end();
    });
}

async function testBugFixes() {
    console.log('🔧 TESTING BUG FIXES\n');

    try {
        // Test 1: Data Display Issues
        console.log('1. 📊 TESTING DATA DISPLAY ISSUES');
        
        // Test vehicles API
        const vehiclesResponse = await makeRequest('/api/vehicles');
        console.log(`   ✅ Vehicles API: ${vehiclesResponse.status === 200 ? 'OK' : 'FAIL'}`);
        
        if (vehiclesResponse.status === 200) {
            const vehicles = vehiclesResponse.data.vehicles || vehiclesResponse.data;
            console.log(`   📋 Found ${vehicles.length} vehicles in database`);
            
            if (vehicles.length > 0) {
                console.log(`   📝 Sample vehicle: ${vehicles[0].license_plate} (${vehicles[0].status})`);
            }
        }

        // Test vehicles with filters
        const impoundedResponse = await makeRequest('/api/vehicles?status=impounded');
        console.log(`   ✅ Impounded vehicles API: ${impoundedResponse.status === 200 ? 'OK' : 'FAIL'}`);
        
        if (impoundedResponse.status === 200) {
            const impounded = impoundedResponse.data.vehicles || impoundedResponse.data;
            console.log(`   🚗 Found ${impounded.length} impounded vehicles`);
        }

        // Test recent vehicles
        const recentResponse = await makeRequest('/api/vehicles?limit=5&sort=entry_date&order=desc');
        console.log(`   ✅ Recent vehicles API: ${recentResponse.status === 200 ? 'OK' : 'FAIL'}`);
        
        if (recentResponse.status === 200) {
            const recent = recentResponse.data.vehicles || recentResponse.data;
            console.log(`   📅 Found ${recent.length} recent vehicles`);
        }

        // Test 2: JavaScript File
        console.log('\n2. 📄 TESTING JAVASCRIPT FILE');
        const jsResponse = await makeRequest('/js/app.js');
        console.log(`   ✅ JavaScript file: ${jsResponse.status === 200 ? 'OK' : 'FAIL'}`);
        
        if (jsResponse.status === 200) {
            const jsContent = jsResponse.data;
            const hasLoadVehicles = jsContent.includes('loadVehicles');
            const hasCloseModal = jsContent.includes('closeModal');
            const hasSetupModalEventListeners = jsContent.includes('setupModalEventListeners');
            
            console.log(`   📋 Has loadVehicles method: ${hasLoadVehicles ? 'YES' : 'NO'}`);
            console.log(`   📋 Has closeModal method: ${hasCloseModal ? 'YES' : 'NO'}`);
            console.log(`   📋 Has setupModalEventListeners: ${hasSetupModalEventListeners ? 'YES' : 'NO'}`);
        }

        // Test 3: Depositors API
        console.log('\n3. 👮 TESTING DEPOSITORS API');
        const depositorsResponse = await makeRequest('/api/depositors?active_only=true');
        console.log(`   ✅ Depositors API: ${depositorsResponse.status === 200 ? 'OK' : 'FAIL'}`);
        
        if (depositorsResponse.status === 200) {
            console.log(`   📋 Found ${depositorsResponse.data.length} active depositors`);
            depositorsResponse.data.forEach(depositor => {
                console.log(`      - ${depositor.name} (ID: ${depositor.id})`);
            });
        }

        // Test 4: Vehicle Types API
        console.log('\n4. 🚗 TESTING VEHICLE TYPES API');
        const typesResponse = await makeRequest('/api/vehicles/types/list');
        console.log(`   ✅ Vehicle types API: ${typesResponse.status === 200 ? 'OK' : 'FAIL'}`);
        
        if (typesResponse.status === 200) {
            console.log(`   📋 Found ${typesResponse.data.length} vehicle types`);
            typesResponse.data.forEach(type => {
                console.log(`      - ${type.name} (ID: ${type.id})`);
            });
        }

        // Test 5: Dashboard Stats
        console.log('\n5. 📊 TESTING DASHBOARD STATS');
        const statsResponse = await makeRequest('/api/dashboard/stats');
        console.log(`   ✅ Dashboard stats API: ${statsResponse.status === 200 ? 'OK' : 'FAIL'}`);
        
        if (statsResponse.status === 200) {
            const stats = statsResponse.data;
            console.log(`   📊 Statistics:`);
            console.log(`      - Impounded: ${stats.impounded}`);
            console.log(`      - Pending release: ${stats.pending_release}`);
            console.log(`      - Released this month: ${stats.released_this_month}`);
            console.log(`      - Overdue: ${stats.overdue}`);
        }

        console.log('\n🎉 BUG FIXES TEST COMPLETED!');
        console.log('\n📋 SUMMARY:');
        console.log('   ✅ Data APIs are working correctly');
        console.log('   ✅ JavaScript file loads with all necessary methods');
        console.log('   ✅ Vehicle data should now display in tables');
        console.log('   ✅ Modal close buttons should work');
        console.log('   ✅ Depositor management should be functional');

        console.log('\n🔍 MANUAL TESTING CHECKLIST:');
        console.log('   1. Open http://localhost:3000');
        console.log('   2. Check if dashboard shows vehicle statistics');
        console.log('   3. Click "Véhicules en fourrière" - should show vehicle list');
        console.log('   4. Click "Nouvelle entrée" - modal should open');
        console.log('   5. Try closing modal with X button or Cancel');
        console.log('   6. Go to "Paramètres" - check depositor management');
        console.log('   7. Try editing/deleting depositors');

        console.log('\n✨ If all tests pass, the critical bugs are fixed!');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

testBugFixes();
