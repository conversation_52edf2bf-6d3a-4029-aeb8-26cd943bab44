// Test script to verify navigation and modal fixes
console.log('🔧 Testing Navigation and Modal Fixes...\n');

async function testFixes() {
    try {
        // Test 1: Check if navigation links work
        console.log('1. Testing navigation links...');
        const navItems = document.querySelectorAll('.nav-item');
        console.log(`   📋 Found ${navItems.length} navigation items`);
        
        navItems.forEach((item, index) => {
            const target = item.getAttribute('data-target');
            const hasSection = document.getElementById(target);
            console.log(`   ${index + 1}. ${target}: ${hasSection ? '✅ OK' : '❌ MISSING'}`);
        });

        // Test 2: Check modal buttons
        console.log('\n2. Testing modal buttons...');
        
        const modalButtons = [
            { id: 'add-vehicle-btn', name: 'Add Vehicle Button' },
            { id: 'close-add-vehicle-modal', name: 'Close Add Vehicle Modal' },
            { id: 'cancel-add-vehicle', name: 'Cancel Add Vehicle' },
            { id: 'close-edit-vehicle-modal', name: 'Close Edit Vehicle Modal' },
            { id: 'cancel-edit-vehicle', name: 'Cancel Edit Vehicle' },
            { id: 'close-view-vehicle-modal', name: 'Close View Vehicle Modal' },
            { id: 'close-view-vehicle', name: 'Close View Vehicle' },
            { id: 'close-payment-modal', name: 'Close Payment Modal' },
            { id: 'cancel-payment', name: 'Cancel Payment' },
            { id: 'close-user-modal', name: 'Close User Modal' },
            { id: 'cancel-user', name: 'Cancel User' }
        ];

        modalButtons.forEach(({ id, name }) => {
            const button = document.getElementById(id);
            console.log(`   📋 ${name}: ${button ? '✅ OK' : '❌ MISSING'}`);
        });

        // Test 3: Check if app instance exists
        console.log('\n3. Testing app instance...');
        console.log(`   📋 Window.app exists: ${window.app ? '✅ OK' : '❌ MISSING'}`);
        
        if (window.app) {
            const methods = [
                'openAddVehicleModal',
                'closeModal',
                'viewVehicle',
                'editVehicle',
                'processPayment',
                'handleNavigation'
            ];
            
            methods.forEach(method => {
                console.log(`   📋 app.${method}: ${typeof window.app[method] === 'function' ? '✅ OK' : '❌ MISSING'}`);
            });
        }

        // Test 4: Check CSS classes
        console.log('\n4. Testing CSS classes...');
        const activeNavItem = document.querySelector('.active-nav-item');
        console.log(`   📋 Active nav item: ${activeNavItem ? '✅ OK' : '❌ MISSING'}`);
        
        const modalElements = document.querySelectorAll('[id*="Modal"]');
        console.log(`   📋 Found ${modalElements.length} modal elements`);

        // Test 5: Simulate navigation click
        console.log('\n5. Testing navigation functionality...');
        const firstNavItem = document.querySelector('.nav-item[data-target="impounded"]');
        if (firstNavItem) {
            console.log('   📋 Simulating click on "Véhicules en fourrière"...');
            firstNavItem.click();
            
            setTimeout(() => {
                const targetSection = document.getElementById('impounded');
                const isVisible = targetSection && !targetSection.classList.contains('hidden');
                console.log(`   📋 Section visibility: ${isVisible ? '✅ OK' : '❌ FAIL'}`);
                
                const pageTitle = document.getElementById('page-title');
                const titleUpdated = pageTitle && pageTitle.textContent.includes('Véhicules');
                console.log(`   📋 Title updated: ${titleUpdated ? '✅ OK' : '❌ FAIL'}`);
            }, 100);
        }

        // Test 6: Test modal opening
        console.log('\n6. Testing modal functionality...');
        const addVehicleBtn = document.getElementById('add-vehicle-btn');
        if (addVehicleBtn) {
            console.log('   📋 Simulating click on "Add Vehicle" button...');
            addVehicleBtn.click();
            
            setTimeout(() => {
                const modal = document.getElementById('addVehicleModal');
                const isVisible = modal && !modal.classList.contains('hidden');
                console.log(`   📋 Modal visibility: ${isVisible ? '✅ OK' : '❌ FAIL'}`);
                
                // Close the modal
                if (isVisible) {
                    const closeBtn = document.getElementById('close-add-vehicle-modal');
                    if (closeBtn) {
                        closeBtn.click();
                        setTimeout(() => {
                            const isClosed = modal.classList.contains('hidden');
                            console.log(`   📋 Modal close: ${isClosed ? '✅ OK' : '❌ FAIL'}`);
                        }, 100);
                    }
                }
            }, 100);
        }

        console.log('\n🎉 Fix testing completed!');
        console.log('\n📋 Manual Testing Instructions:');
        console.log('1. Click on different sidebar menu items');
        console.log('2. Click on "Nouvelle entrée" button');
        console.log('3. Click on X buttons to close modals');
        console.log('4. Check that no # appears in URL when navigating');
        console.log('5. Verify that page titles update correctly');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

// Run tests when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', testFixes);
} else {
    testFixes();
}

// Also run tests after a short delay to ensure app is initialized
setTimeout(testFixes, 1000);
