<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion Fourrière Véhicules</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#10B981',
                        accent: '#8B5CF6',
                        dark: '#1F2937',
                        light: '#F9FAFB'
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
        
        * {
            font-family: 'Poppins', sans-serif;
        }
        
        .active-nav-item {
            background: rgba(59, 130, 246, 0.1);
            border-right: 4px solid #3B82F6;
        }
        
        .sidebar-minimized {
            width: 60px !important;
        }
        
        .sidebar-minimized span {
            display: none !important;
        }
        
        .sidebar-minimized .brand {
            justify-content: center;
        }
        
        .sidebar-minimized .brand-text {
            display: none;
        }
        
        .card-hover {
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        .animal-tag {
            display: inline-block;
            padding: 0.2rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        .photo-placeholder {
            background: #E5E7EB;
            border-radius: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6B7280;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        <div id="sidebar" class="w-64 bg-white shadow-md flex flex-col transition-all duration-300">
            <div class="p-4 border-b">
                <div class="brand flex items-center">
                    <div class="bg-primary w-10 h-10 rounded-lg flex items-center justify-center">
                        <i class="fas fa-paw text-white text-xl"></i>
                    </div>
                    <span class="brand-text ml-3 text-xl font-bold text-gray-800">Fourrière Véhicules</span>
                </div>
            </div>
            
            <div class="flex-1 overflow-y-auto py-4">
                <nav class="space-y-1">
                    <a href="#" data-target="dashboard" class="nav-item flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 active-nav-item">
                        <i class="fas fa-chart-line text-gray-500"></i>
                        <span class="ml-3">Tableau de bord</span>
                    </a>
                    <a href="#" data-target="impounded" class="nav-item flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50">
                        <i class="fas fa-car text-gray-500"></i>
                        <span class="ml-3">Véhicules en fourrière</span>
                    </a>
                    <a href="#" data-target="pending" class="nav-item flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50">
                        <i class="fas fa-hourglass-half text-gray-500"></i>
                        <span class="ml-3">En attente de sortie</span>
                    </a>
                    <a href="#" data-target="released" class="nav-item flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50">
                        <i class="fas fa-sign-out-alt text-gray-500"></i>
                        <span class="ml-3">Véhicules sortis</span>
                    </a>
                    <a href="#" data-target="overdue" class="nav-item flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50">
                        <i class="fas fa-exclamation-triangle text-gray-500"></i>
                        <span class="ml-3">Délais dépassés (1+ an)</span>
                    </a>
                    <a href="#" data-target="reports" class="nav-item flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50">
                        <i class="fas fa-chart-pie text-gray-500"></i>
                        <span class="ml-3">Rapports</span>
                    </a>
                    <a href="#" data-target="settings" class="nav-item flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50">
                        <i class="fas fa-cog text-gray-500"></i>
                        <span class="ml-3">Paramètres</span>
                    </a>
                </nav>
                
                <div class="p-4 mt-6">
                    <div class="bg-blue-50 rounded-lg p-4">
                        <h3 class="text-sm font-medium text-blue-800">Besoins d'aide?</h3>
                        <p class="text-xs text-blue-600 mt-1">Notre équipe est disponible pour vous aider</p>
                        <button class="mt-3 w-full bg-primary hover:bg-blue-700 text-white text-xs py-1.5 rounded-md transition">
                            Contacter le support
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="p-4 border-t">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-blue-600"></i>
                        </div>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-900">Adeline Dubois</p>
                        <p class="text-xs text-gray-500">Responsable fourrière</p>
                    </div>
                    <button id="sidebar-toggle" class="ml-auto p-1 rounded-md hover:bg-gray-100">
                        <i class="fas fa-chevron-left text-gray-500"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <header class="bg-white shadow">
                <div class="px-6 py-4 flex items-center">
                    <h1 id="page-title" class="text-2xl font-bold text-gray-800">Tableau de bord</h1>
                    <div class="ml-auto flex items-center">
                        <div class="relative">
                            <input type="text" class="border rounded-full px-4 py-1.5 text-sm w-64 focus:outline-none focus:ring-1 focus:ring-primary" placeholder="Rechercher...">
                            <i class="fas fa-search absolute right-3 top-2 text-gray-400"></i>
                        </div>
                        <div class="ml-6 flex items-center space-x-3">
                            <button class="p-2 rounded-full hover:bg-gray-100 relative">
                                <i class="fas fa-bell text-gray-500"></i>
                                <span class="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
                            </button>
                            <button class="p-2 rounded-full hover:bg-gray-100">
                                <i class="fas fa-question-circle text-gray-500"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </header>
            
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Dashboard Section -->
                <section id="dashboard" class="page-section">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                        <div class="bg-white rounded-xl shadow-sm p-5 card-hover">
                            <div class="flex items-center">
                                <div class="bg-blue-100 p-3 rounded-lg">
                                    <i class="fas fa-car text-blue-600 text-xl"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-gray-500 text-sm">Véhicules immobilisés</p>
                                    <h3 class="text-2xl font-bold text-gray-800">82</h3>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white rounded-xl shadow-sm p-5 card-hover">
                            <div class="flex items-center">
                                <div class="bg-yellow-100 p-3 rounded-lg">
                                    <i class="fas fa-hourglass-half text-yellow-600 text-xl"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-gray-500 text-sm">En attente de sortie</p>
                                    <h3 class="text-2xl font-bold text-gray-800">14 <span class="text-base text-red-500">(3 impayés)</span></h3>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white rounded-xl shadow-sm p-5 card-hover">
                            <div class="flex items-center">
                                <div class="bg-green-100 p-3 rounded-lg">
                                    <i class="fas fa-sign-out-alt text-green-600 text-xl"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-gray-500 text-sm">Sortis ce mois</p>
                                    <h3 class="text-2xl font-bold text-gray-800">26</h3>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white rounded-xl shadow-sm p-5 card-hover">
                            <div class="flex items-center">
                                <div class="bg-red-100 p-3 rounded-lg">
                                    <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-gray-500 text-sm">En fourrière +1 an</p>
                                    <h3 class="text-2xl font-bold text-gray-800">7</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                        <div class="bg-white rounded-xl shadow-sm p-5">
                            <div class="flex justify-between items-center mb-4">
                                <h2 class="text-lg font-semibold text-gray-800">Statistiques mensuelles</h2>
                                <select class="text-sm border rounded px-2 py-1">
                                    <option>30 derniers jours</option>
                                    <option>60 derniers jours</option>
                                    <option>90 derniers jours</option>
                                </select>
                            </div>
                            <div class="h-64">
                                <canvas id="statsChart"></canvas>
                            </div>
                        </div>
                        <div class="bg-white rounded-xl shadow-sm p-5">
                            <div class="flex justify-between items-center mb-4">
                                <h2 class="text-lg font-semibold text-gray-800">Types d'animaux</h2>
                                <button class="text-primary text-sm">
                                    <i class="fas fa-download mr-1"></i> Exporter
                                </button>
                            </div>
                            <div class="flex">
                                <div class="w-32 h-32">
                                    <canvas id="animalTypesChart"></canvas>
                                </div>
                                <div class="ml-6 flex-1">
                                    <div class="space-y-2">
                                        <div class="flex items-center">
                                            <div class="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                                            <span class="text-sm text-gray-600">Chiens (72%)</span>
                                        </div>
                                        <div class="flex items-center">
                                            <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                                            <span class="text-sm text-gray-600">Chats (18%)</span>
                                        </div>
                                        <div class="flex items-center">
                                            <div class="w-3 h-3 bg-purple-500 rounded-full mr-2"></div>
                                            <span class="text-sm text-gray-600">Oiseaux (6%)</span>
                                        </div>
                                        <div class="flex items-center">
                                            <div class="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
                                            <span class="text-sm text-gray-600">Autres (4%)</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-xl shadow-sm p-5 mb-6">
                        <div class="flex justify-between items-center mb-4">
                            <h2 class="text-lg font-semibold text-gray-800">Animaux récemment enregistrés</h2>
                            <button class="text-primary text-sm">
                                Voir tous
                            </button>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Animal</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date d'entrée</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">#ID123</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="w-10 h-10 photo-placeholder">
                                                    <i class="fas fa-dog"></i>
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900">Rex</div>
                                                    <div class="text-xs text-gray-500">Berger Allemand</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Chien</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">15/06/2023</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="bg-yellow-100 text-yellow-800 status-badge">En attente</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <button class="text-blue-600 hover:text-blue-900 mr-3"><i class="fas fa-eye"></i></button>
                                            <button class="text-green-600 hover:text-green-900"><i class="fas fa-edit"></i></button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">#ID124</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="w-10 h-10 photo-placeholder">
                                                    <i class="fas fa-cat"></i>
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900">Misty</div>
                                                    <div class="text-xs text-gray-500">Siamois</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Chat</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">14/06/2023</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="bg-green-100 text-green-800 status-badge">Adopté</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <button class="text-blue-600 hover:text-blue-900 mr-3"><i class="fas fa-eye"></i></button>
                                            <button class="text-green-600 hover:text-green-900"><i class="fas fa-edit"></i></button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">#ID125</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="w-10 h-10 photo-placeholder">
                                                    <i class="fas fa-dog"></i>
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900">Buddy</div>
                                                    <div class="text-xs text-gray-500">Labrador</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Chien</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">13/06/2023</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="bg-blue-100 text-blue-800 status-badge">En soin</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <button class="text-blue-600 hover:text-blue-900 mr-3"><i class="fas fa-eye"></i></button>
                                            <button class="text-green-600 hover:text-green-900"><i class="fas fa-edit"></i></button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">#ID126</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="w-10 h-10 photo-placeholder">
                                                    <i class="fas fa-dove"></i>
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900">Coco</div>
                                                    <div class="text-xs text-gray-500">Perroquet</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Oiseau</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">11/06/2023</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="bg-red-100 text-red-800 status-badge">Récupéré</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <button class="text-blue-600 hover:text-blue-900 mr-3"><i class="fas fa-eye"></i></button>
                                            <button class="text-green-600 hover:text-green-900"><i class="fas fa-edit"></i></button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </section>
                
                <!-- Impounded Vehicles Section -->
                <section id="impounded" class="page-section hidden">
                    <div class="mb-4 flex justify-between items-center">
                        <h2 class="text-xl font-bold text-gray-800">Véhicules en fourrière</h2>
                        <button class="btn-primary" onclick="openAddVehicleModal()">
                            <i class="fas fa-plus mr-2"></i> Nouvelle entrée
                        </button>
                    </div>
                    <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">N° Dépôt</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Immatriculation</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Marque/Modèle</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date d'entrée</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <!-- Sample rows would go here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </section>
                
                <!-- Pending Release Section -->
                <section id="pending" class="page-section hidden">
                    <div class="mb-4">
                        <h2 class="text-xl font-bold text-gray-800">Véhicules en attente de sortie</h2>
                        <div class="mt-2 text-sm text-gray-600">3 véhicules avec paiement en attente</div>
                    </div>
                    <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                        <!-- Similar table structure as above -->
                    </div>
                </section>
                
                <!-- Released Vehicles Section -->
                <section id="released" class="page-section hidden">
                    <div class="mb-4">
                        <h2 class="text-xl font-bold text-gray-800">Véhicules sortis</h2>
                    </div>
                    <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                        <!-- Similar table structure as above -->
                    </div>
                </section>
                
                <!-- Overdue Vehicles Section -->
                <section id="overdue" class="page-section hidden">
                    <div class="mb-4">
                        <h2 class="text-xl font-bold text-gray-800">Véhicules en fourrière depuis plus d'un an</h2>
                    </div>
                    <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                        <!-- Similar table structure as above -->
                    </div>
                </section>
                
                <!-- Reports Section (Hidden by default) -->
                <section id="reports" class="page-section hidden">
                    <!-- Content for Reports -->
                </section>
                
                <!-- Settings Section (Hidden by default) -->
                <section id="settings" class="page-section hidden">
                    <!-- Content for Settings -->
                </section>
            </main>
            
            <!-- Footer -->
            <footer class="bg-white border-t py-4 px-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500">© 2023 Fourrière Communale. Tous droits réservés.</p>
                    </div>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-gray-500">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-gray-500">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-gray-500">
                            <i class="fab fa-instagram"></i>
                        </a>
                    </div>
                </div>
            </footer>
        </div>
    </div>
    
    <!-- Modals -->
    <div id="addVehicleModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg w-full max-w-md">
            <div class="border-b px-6 py-4 flex justify-between items-center">
                <h3 class="text-lg font-medium text-gray-900">Nouvelle entrée de véhicule</h3>
                <button onclick="closeModal('addVehicleModal')" class="text-gray-400 hover:text-gray-500">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="px-6 py-4 space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Type de véhicule</label>
                    <select class="w-full border rounded-md px-3 py-2">
                        <option>Voiture</option>
                        <option>Moto</option>
                        <option>Camion</option>
                        <option>Autre</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">N° de dépôt</label>
                    <input type="text" class="w-full border rounded-md px-3 py-2">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Déposant</label>
                    <input type="text" class="w-full border rounded-md px-3 py-2">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Immatriculation</label>
                    <input type="text" class="w-full border rounded-md px-3 py-2">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Marque</label>
                    <input type="text" class="w-full border rounded-md px-3 py-2">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Date d'entrée</label>
                    <input type="date" class="w-full border rounded-md px-3 py-2">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Observations</label>
                    <textarea rows="3" class="w-full border rounded-md px-3 py-2"></textarea>
                </div>
            </div>
            <div class="border-t px-6 py-4 flex justify-end space-x-3">
                <button onclick="closeModal('addVehicleModal')" class="px-4 py-2 border rounded-md text-gray-700 hover:bg-gray-50">
                    Annuler
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                    Enregistrer
                </button>
            </div>
        </div>
    </div>

    <div id="paymentModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg w-full max-w-md">
            <div class="border-b px-6 py-4 flex justify-between items-center">
                <h3 class="text-lg font-medium text-gray-900">Paiement et sortie de véhicule</h3>
                <button onclick="closeModal('paymentModal')" class="text-gray-400 hover:text-gray-500">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="px-6 py-4 space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Montant à payer</label>
                    <input type="text" class="w-full border rounded-md px-3 py-2 bg-gray-100" value="750,00 DH" readonly>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Date de paiement</label>
                    <input type="date" class="w-full border rounded-md px-3 py-2">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">N° de quittance</label>
                    <input type="text" class="w-full border rounded-md px-3 py-2">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">N° d'ordre de sortie</label>
                    <input type="text" class="w-full border rounded-md px-3 py-2">
                </div>
            </div>
            <div class="border-t px-6 py-4 flex justify-end space-x-3">
                <button onclick="closeModal('paymentModal')" class="px-4 py-2 border rounded-md text-gray-700 hover:bg-gray-50">
                    Annuler
                </button>
                <button class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                    Valider le paiement
                </button>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // Navigation functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Handle sidebar navigation
            const navItems = document.querySelectorAll('.nav-item');
            const pageSections = document.querySelectorAll('.page-section');
            const pageTitle = document.getElementById('page-title');
            
            navItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // Remove active class from all items
                    navItems.forEach(item => {
                        item.classList.remove('active-nav-item');
                    });
                    
                    // Add active class to clicked item
                    this.classList.add('active-nav-item');
                    
                    // Hide all sections
                    pageSections.forEach(section => {
                        section.classList.add('hidden');
                    });
                    
                    // Show the target section
                    const targetSection = this.getAttribute('data-target');
                    document.getElementById(`${targetSection}`).classList.remove('hidden');
                    
                    // Update page title
                    const itemName = this.querySelector('span').textContent;
                    pageTitle.textContent = itemName;
                });
            });
            
            // Toggle sidebar
            const sidebar = document.getElementById('sidebar');
            const sidebarToggle = document.getElementById('sidebar-toggle');
            const chevronIcon = sidebarToggle.querySelector('i');
            
            sidebarToggle.addEventListener('click', function() {
                sidebar.classList.toggle('sidebar-minimized');
                if (sidebar.classList.contains('sidebar-minimized')) {
                    chevronIcon.classList.remove('fa-chevron-left');
                    chevronIcon.classList.add('fa-chevron-right');
                } else {
                    chevronIcon.classList.remove('fa-chevron-right');
                    chevronIcon.classList.add('fa-chevron-left');
                }
            });
            
            // Initialize charts
            const statsCtx = document.getElementById('statsChart').getContext('2d');
            const statsChart = new Chart(statsCtx, {
                type: 'line',
                data: {
                    labels: ['1', '5', '10', '15', '20', '25', '30'],
                    datasets: [
                        {
                            label: 'Animaux entrants',
                            data: [3, 8, 6, 9, 4, 7, 5],
                            borderColor: '#3B82F6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            tension: 0.4,
                            fill: true
                        },
                        {
                            label: 'Animants sortants',
                            data: [2, 4, 3, 5, 4, 2, 6],
                            borderColor: '#10B981',
                            backgroundColor: 'rgba(16, 185, 129, 0.1)',
                            tension: 0.4,
                            fill: true
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
            
            const animalTypesCtx = document.getElementById('animalTypesChart').getContext('2d');
            const animalTypesChart = new Chart(animalTypesCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Chiens', 'Chats', 'Oiseaux', 'Autres'],
                    datasets: [{
                        data: [72, 18, 6, 4],
                        backgroundColor: [
                            '#3B82F6',
                            '#10B981',
                            '#8B5CF6',
                            '#F59E0B'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    cutout: '65%'
                }
            });
        });
    </script>
</body>
</html>