# Système de Gestion de Fourrière Véhicules

Un système complet de gestion de fourrière pour véhicules développé avec Node.js, Express, MariaDB et une interface web moderne.

## 🚗 Fonctionnalités

### Gestion des Véhicules
- ✅ Enregistrement de nouveaux véhicules en fourrière
- ✅ Suivi des véhicules par statut (en fourrière, en attente de sortie, sortis, en retard)
- ✅ Recherche et filtrage avancés
- ✅ Gestion des informations propriétaires
- ✅ Calcul automatique des frais de stockage
- ✅ Détection automatique des véhicules en retard (+1 an)

### Gestion des Paiements
- ✅ Traitement des paiements avec différentes méthodes
- ✅ Génération automatique des numéros de quittance
- ✅ Processus de libération des véhicules
- ✅ Calcul automatique des montants dus
- ✅ Historique des paiements

### Tableau de Bord et Rapports
- ✅ Statistiques en temps réel
- ✅ Graphiques interactifs (Chart.js)
- ✅ Rapports de synthèse, financiers et véhicules
- ✅ Alertes pour véhicules en retard
- ✅ Suivi des revenus et paiements

### Interface Utilisateur
- ✅ Interface moderne et responsive (Tailwind CSS)
- ✅ Navigation intuitive avec sidebar
- ✅ Modals pour les formulaires
- ✅ Messages de succès/erreur
- ✅ Pagination des résultats

## 🛠️ Technologies Utilisées

### Backend
- **Node.js** - Runtime JavaScript
- **Express.js** - Framework web
- **MariaDB/MySQL** - Base de données
- **mysql2** - Driver MySQL pour Node.js
- **express-validator** - Validation des données
- **helmet** - Sécurité HTTP
- **cors** - Gestion CORS
- **dotenv** - Variables d'environnement

### Frontend
- **HTML5** - Structure
- **Tailwind CSS** - Styles et responsive design
- **JavaScript ES6+** - Logique frontend
- **Chart.js** - Graphiques et visualisations
- **Font Awesome** - Icônes

## 📋 Prérequis

- Node.js (version 16+)
- MariaDB ou MySQL
- npm ou yarn

## 🚀 Installation et Configuration

### 1. Cloner le projet
```bash
git clone <repository-url>
cd fourriere-management
```

### 2. Installer les dépendances
```bash
npm install
```

### 3. Configuration de la base de données
Créer une base de données MariaDB/MySQL et configurer les paramètres dans le fichier `.env`:

```env
DB_HOST=*************
DB_PORT=3306
DB_NAME=fourriere_db
DB_USER=root
DB_PASSWORD=Yomix1990**
PORT=3000
NODE_ENV=development
```

### 4. Exécuter les migrations
```bash
npm run migrate
```

### 5. Démarrer le serveur
```bash
npm start
```

L'application sera accessible à l'adresse: http://localhost:3000

## 📊 Structure de la Base de Données

### Tables Principales
- **vehicles** - Informations des véhicules
- **vehicle_types** - Types de véhicules (voiture, moto, etc.)
- **vehicle_owners** - Propriétaires des véhicules
- **payments** - Paiements effectués
- **vehicle_releases** - Sorties de véhicules
- **vehicle_photos** - Photos des véhicules
- **system_settings** - Paramètres du système

## 🔧 API Endpoints

### Véhicules
- `GET /api/vehicles` - Liste des véhicules avec filtres
- `POST /api/vehicles` - Créer un nouveau véhicule
- `GET /api/vehicles/:id` - Détails d'un véhicule
- `PUT /api/vehicles/:id` - Modifier un véhicule
- `DELETE /api/vehicles/:id` - Supprimer un véhicule
- `PUT /api/vehicles/:id/status` - Changer le statut

### Paiements
- `GET /api/payments` - Liste des paiements
- `POST /api/payments` - Créer un paiement
- `POST /api/payments/process-release` - Traiter paiement et libération
- `GET /api/payments/calculate/:vehicle_id` - Calculer montant dû

### Tableau de Bord
- `GET /api/dashboard/stats` - Statistiques générales
- `GET /api/dashboard/monthly-stats` - Statistiques mensuelles
- `GET /api/dashboard/vehicle-types` - Répartition par types
- `GET /api/dashboard/recent-vehicles` - Véhicules récents

### Rapports
- `GET /api/reports/summary` - Rapport de synthèse
- `GET /api/reports/financial` - Rapport financier
- `GET /api/reports/vehicles` - Rapport véhicules
- `GET /api/reports/overdue` - Véhicules en retard

## 🎯 Utilisation

### 1. Tableau de Bord
- Vue d'ensemble des statistiques
- Graphiques des entrées/sorties
- Répartition par types de véhicules
- Véhicules récemment enregistrés

### 2. Gestion des Véhicules
- Ajouter un nouveau véhicule via le bouton "Nouvelle entrée"
- Filtrer par type, statut ou recherche textuelle
- Actions disponibles: voir, modifier, payer, supprimer

### 3. Traitement des Paiements
- Cliquer sur l'icône de paiement pour un véhicule
- Le montant est calculé automatiquement
- Remplir les informations de paiement et de sortie
- Validation automatique de la libération

### 4. Rapports
- Accéder à la section "Rapports"
- Générer différents types de rapports
- Exporter les données (fonctionnalité à implémenter)

## 🔒 Sécurité

- Headers de sécurité avec Helmet
- Validation des données d'entrée
- Protection CORS configurée
- Gestion d'erreurs centralisée
- Échappement des requêtes SQL

## 📈 Fonctionnalités Avancées

### Calcul Automatique des Frais
- Frais de stockage par jour configurables
- Calcul automatique basé sur la durée
- Gestion des différentes devises

### Détection des Véhicules en Retard
- Seuil configurable (par défaut 365 jours)
- Alertes automatiques
- Actions spéciales pour véhicules abandonnés

### Système de Notifications
- Alertes visuelles dans l'interface
- Compteurs de notifications
- Messages de succès/erreur

## 🚀 Déploiement en Production

### Variables d'Environnement
```env
NODE_ENV=production
DB_HOST=your-production-db-host
DB_PASSWORD=your-secure-password
JWT_SECRET=your-jwt-secret
```

### Recommandations
- Utiliser un reverse proxy (nginx)
- Configurer HTTPS
- Sauvegardes automatiques de la base de données
- Monitoring et logs
- Mise en place d'un CDN pour les assets statiques

## 📝 Licence

Ce projet est sous licence MIT.

## 👥 Support

Pour toute question ou support technique, contactez l'équipe de développement.

---

**Système de Gestion de Fourrière Véhicules** - Version 1.0.0
