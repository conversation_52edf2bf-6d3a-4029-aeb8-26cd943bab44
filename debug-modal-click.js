// Debug modal click issues
console.log('🔍 Debugging Modal Click Issues...\n');

function debugModalClick() {
    console.log('=== MODAL CLICK DIAGNOSTIC ===\n');
    
    // Test 1: Check if app exists
    console.log('1. Checking app instance...');
    if (!window.app) {
        console.error('❌ CRITICAL: window.app does not exist!');
        console.log('   This means the main application did not load properly.');
        console.log('   Check for JavaScript errors in the console.');
        return;
    }
    console.log('✅ window.app exists');
    
    // Test 2: Check processPayment method
    console.log('\n2. Checking processPayment method...');
    if (typeof window.app.processPayment !== 'function') {
        console.error('❌ CRITICAL: app.processPayment is not a function!');
        console.log('   The processPayment method is missing or not properly defined.');
        return;
    }
    console.log('✅ app.processPayment is a function');
    
    // Test 3: Check modal element
    console.log('\n3. Checking payment modal...');
    const modal = document.getElementById('paymentModal');
    if (!modal) {
        console.error('❌ CRITICAL: Payment modal element not found!');
        console.log('   The modal with ID "paymentModal" does not exist in the DOM.');
        return;
    }
    console.log('✅ Payment modal element exists');
    console.log(`   Modal classes: ${modal.className}`);
    console.log(`   Modal is hidden: ${modal.classList.contains('hidden')}`);
    
    // Test 4: Look for payment buttons
    console.log('\n4. Looking for payment buttons...');
    const paymentButtons = document.querySelectorAll('button[onclick*="processPayment"]');
    console.log(`   Found ${paymentButtons.length} payment buttons`);
    
    if (paymentButtons.length === 0) {
        console.warn('⚠️ WARNING: No payment buttons found!');
        console.log('   This could mean:');
        console.log('   - No vehicles are loaded');
        console.log('   - No vehicles have "impounded" status');
        console.log('   - User is not in the correct section');
        
        // Check current section
        const activeNav = document.querySelector('.nav-item.active-nav-item');
        const currentSection = activeNav ? activeNav.dataset.target : 'unknown';
        console.log(`   Current section: ${currentSection}`);
        
        // Check for any vehicles
        const vehicleRows = document.querySelectorAll('table tbody tr');
        console.log(`   Total vehicle rows: ${vehicleRows.length}`);
        
        if (vehicleRows.length === 0) {
            console.log('   ❌ No vehicles loaded. Try adding some vehicles first.');
        }
        
        return;
    }
    
    // Test 5: Analyze first payment button
    console.log('\n5. Analyzing first payment button...');
    const firstButton = paymentButtons[0];
    const onclick = firstButton.getAttribute('onclick');
    console.log(`   Button onclick: ${onclick}`);
    console.log(`   Button text: "${firstButton.textContent.trim()}"`);
    console.log(`   Button classes: ${firstButton.className}`);
    
    // Extract vehicle ID
    const match = onclick.match(/processPayment\((\d+)\)/);
    if (!match) {
        console.error('❌ ERROR: Cannot extract vehicle ID from onclick');
        return;
    }
    
    const vehicleId = parseInt(match[1]);
    console.log(`   Vehicle ID: ${vehicleId}`);
    
    // Test 6: Test API call manually
    console.log('\n6. Testing API call manually...');
    console.log(`   Attempting to fetch vehicle ${vehicleId}...`);
    
    fetch(`/api/vehicles/${vehicleId}`)
        .then(response => {
            console.log(`   API Response status: ${response.status} ${response.statusText}`);
            if (response.ok) {
                return response.json();
            } else {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
        })
        .then(vehicle => {
            console.log('✅ Vehicle data retrieved successfully:');
            console.log(`   - Depot: ${vehicle.depot_number}`);
            console.log(`   - License: ${vehicle.license_plate}`);
            console.log(`   - Status: ${vehicle.status}`);
            console.log(`   - Entry date: ${vehicle.entry_date}`);
            
            // Test 7: Test processPayment directly
            console.log('\n7. Testing processPayment directly...');
            try {
                window.app.processPayment(vehicleId);
                console.log('✅ processPayment called successfully');
                
                // Check if modal opened
                setTimeout(() => {
                    const isModalVisible = !modal.classList.contains('hidden');
                    console.log(`   Modal opened: ${isModalVisible ? '✅ YES' : '❌ NO'}`);
                    
                    if (!isModalVisible) {
                        console.error('❌ PROBLEM: Modal did not open!');
                        console.log('   Check for JavaScript errors in the processPayment method.');
                    } else {
                        console.log('🎉 SUCCESS: Modal opened correctly!');
                        
                        // Check modal content
                        const vehicleInfo = document.getElementById('payment-vehicle-info');
                        if (vehicleInfo && vehicleInfo.innerHTML.trim()) {
                            console.log('✅ Modal content populated');
                        } else {
                            console.warn('⚠️ Modal opened but content not populated');
                        }
                        
                        // Close modal after test
                        setTimeout(() => {
                            modal.classList.add('hidden');
                            console.log('Test modal closed');
                        }, 3000);
                    }
                }, 1000);
                
            } catch (error) {
                console.error('❌ ERROR in processPayment:', error);
                console.error('Stack trace:', error.stack);
            }
        })
        .catch(error => {
            console.error('❌ API ERROR:', error.message);
            console.log('   This could mean:');
            console.log('   - User is not authenticated');
            console.log('   - Vehicle does not exist');
            console.log('   - Server is not responding');
        });
}

// Test 8: Create manual test button
function createManualTestButton() {
    const testBtn = document.createElement('button');
    testBtn.innerHTML = '🔧 Debug Modal Click';
    testBtn.className = 'bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded m-2';
    testBtn.onclick = debugModalClick;
    
    // Add to page
    const header = document.querySelector('h1, h2, .text-2xl');
    if (header && header.parentNode) {
        header.parentNode.insertBefore(testBtn, header.nextSibling);
        console.log('🔧 Debug button added to page');
    }
}

// Auto-run diagnostic
setTimeout(() => {
    console.log('🚀 Auto-running modal click diagnostic...');
    createManualTestButton();
    debugModalClick();
}, 6000); // Wait 6 seconds for everything to load
