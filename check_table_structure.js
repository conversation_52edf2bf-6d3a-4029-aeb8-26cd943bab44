// Script to check the actual structure of the vehicles table
const mysql = require('mysql2/promise');

async function checkTableStructure() {
    try {
        console.log('🔍 Checking vehicles table structure...\n');
        
        const connection = await mysql.createConnection({
            host: '*************',
            port: 3306,
            user: 'root',
            password: 'Azerty123456789',
            database: 'fourriere_db'
        });

        // Get table structure
        const [columns] = await connection.execute('DESCRIBE vehicles');
        
        console.log('📊 VEHICLES TABLE STRUCTURE:');
        console.log('=====================================');
        console.log('Field\t\t\tType\t\t\tNull\tKey\tDefault\tExtra');
        console.log('-----\t\t\t----\t\t\t----\t---\t-------\t-----');
        
        columns.forEach(col => {
            const field = col.Field.padEnd(20);
            const type = col.Type.padEnd(20);
            const nullable = col.Null.padEnd(8);
            const key = col.Key.padEnd(8);
            const defaultVal = (col.Default || 'NULL').toString().padEnd(12);
            const extra = col.Extra || '';
            
            console.log(`${field}${type}${nullable}${key}${defaultVal}${extra}`);
        });
        
        console.log('\n🔍 COLUMNS THAT EXIST:');
        const existingColumns = columns.map(col => col.Field);
        existingColumns.forEach((col, index) => {
            console.log(`${index + 1}. ${col}`);
        });
        
        console.log('\n❌ COLUMNS THAT DON\'T EXIST (based on current code):');
        const expectedColumns = [
            'depot_number', 'license_plate', 'vehicle_type_id', 'brand', 
            'color', 'year', 'vin', 'depositor', 'entry_date', 'entry_time', 
            'location_found', 'reason_impounded', 'observations', 
            'storage_cost_per_day', 'owner_id'
        ];
        
        const missingColumns = expectedColumns.filter(col => !existingColumns.includes(col));
        missingColumns.forEach((col, index) => {
            console.log(`${index + 1}. ${col} ❌`);
        });
        
        console.log('\n✅ COLUMNS THAT EXIST (from expected):');
        const presentColumns = expectedColumns.filter(col => existingColumns.includes(col));
        presentColumns.forEach((col, index) => {
            console.log(`${index + 1}. ${col} ✅`);
        });
        
        await connection.end();
        
    } catch (error) {
        console.error('❌ Error checking table structure:', error.message);
    }
}

checkTableStructure();
