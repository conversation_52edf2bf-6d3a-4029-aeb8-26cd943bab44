/**
 * check if a string is a boolean.
 */
export default function isBoolean(str: string, options?: Options): boolean;

export interface Options {
    /**
     * If loose is is set to false, the validator will strictly match ['true', 'false', '0', '1'].
     * If loose is set to true, the validator will also match 'yes', 'no', and will match a valid boolean string of any case. (eg: ['true', 'True', 'TRUE']).
     * @default false
     */
    loose?: boolean | undefined;
}
