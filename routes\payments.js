const express = require('express');
const router = express.Router();
const { body, validationResult } = require('express-validator');
const db = require('../config/database');
const moment = require('moment');

// Validation middleware
const validatePayment = [
    body('vehicle_id').isInt().withMessage('ID véhicule invalide'),
    body('amount').isFloat({ min: 0 }).withMessage('Montant invalide'),
    body('payment_date').isDate().withMessage('Date de paiement invalide'),
    body('receipt_number').notEmpty().withMessage('Numéro de quittance requis'),
    body('payment_method').isIn(['cash', 'check', 'bank_transfer', 'card']).withMessage('Méthode de paiement invalide')
];

// GET /api/payments - Get all payments
router.get('/', async (req, res) => {
    try {
        const { vehicle_id, page = 1, limit = 10 } = req.query;
        const offset = (page - 1) * limit;

        let query = `
            SELECT p.*, v.depot_number, v.license_plate, v.brand, v.model
            FROM payments p
            JOIN vehicles v ON p.vehicle_id = v.id
            WHERE 1=1
        `;

        const params = [];

        if (vehicle_id) {
            query += ' AND p.vehicle_id = ?';
            params.push(vehicle_id);
        }

        query += ' ORDER BY p.payment_date DESC LIMIT ? OFFSET ?';
        params.push(parseInt(limit), parseInt(offset));

        const payments = await db.executeQuery(query, params);

        // Get total count
        let countQuery = 'SELECT COUNT(*) as total FROM payments p WHERE 1=1';
        const countParams = [];

        if (vehicle_id) {
            countQuery += ' AND p.vehicle_id = ?';
            countParams.push(vehicle_id);
        }

        const [countResult] = await db.executeQuery(countQuery, countParams);
        const total = countResult.total;

        res.json({
            payments,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                pages: Math.ceil(total / limit)
            }
        });

    } catch (error) {
        console.error('Error fetching payments:', error);
        res.status(500).json({ error: 'Erreur lors de la récupération des paiements' });
    }
});

// GET /api/payments/:id - Get single payment
router.get('/:id', async (req, res) => {
    try {
        const { id } = req.params;

        const query = `
            SELECT p.*, v.depot_number, v.license_plate, v.brand, v.model,
                   v.entry_date, v.storage_cost_per_day,
                   DATEDIFF(p.payment_date, v.entry_date) as storage_days
            FROM payments p
            JOIN vehicles v ON p.vehicle_id = v.id
            WHERE p.id = ?
        `;

        const [payment] = await db.executeQuery(query, [id]);

        if (!payment) {
            return res.status(404).json({ error: 'Paiement non trouvé' });
        }

        res.json(payment);

    } catch (error) {
        console.error('Error fetching payment:', error);
        res.status(500).json({ error: 'Erreur lors de la récupération du paiement' });
    }
});

// POST /api/payments - Create new payment
router.post('/', validatePayment, async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }

        const { vehicle_id, amount, payment_date, receipt_number, payment_method, notes } = req.body;

        // Check if vehicle exists
        const [vehicle] = await db.executeQuery('SELECT * FROM vehicles WHERE id = ?', [vehicle_id]);
        if (!vehicle) {
            return res.status(404).json({ error: 'Véhicule non trouvé' });
        }

        const result = await db.executeQuery(
            'INSERT INTO payments (vehicle_id, amount, payment_date, receipt_number, payment_method, notes) VALUES (?, ?, ?, ?, ?, ?)',
            [vehicle_id, amount, payment_date, receipt_number, payment_method, notes]
        );

        res.status(201).json({
            message: 'Paiement enregistré avec succès',
            payment_id: result.insertId
        });

    } catch (error) {
        console.error('Error creating payment:', error);
        if (error.code === 'ER_DUP_ENTRY') {
            res.status(400).json({ error: 'Numéro de quittance déjà existant' });
        } else {
            res.status(500).json({ error: 'Erreur lors de l\'enregistrement du paiement' });
        }
    }
});

// PUT /api/payments/:id - Update payment
router.put('/:id', validatePayment, async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }

        const { id } = req.params;
        const { vehicle_id, amount, payment_date, receipt_number, payment_method, notes } = req.body;

        await db.executeQuery(
            'UPDATE payments SET vehicle_id = ?, amount = ?, payment_date = ?, receipt_number = ?, payment_method = ?, notes = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [vehicle_id, amount, payment_date, receipt_number, payment_method, notes, id]
        );

        res.json({ message: 'Paiement mis à jour avec succès' });

    } catch (error) {
        console.error('Error updating payment:', error);
        res.status(500).json({ error: 'Erreur lors de la mise à jour du paiement' });
    }
});

// DELETE /api/payments/:id - Delete payment
router.delete('/:id', async (req, res) => {
    try {
        const { id } = req.params;

        await db.executeQuery('DELETE FROM payments WHERE id = ?', [id]);

        res.json({ message: 'Paiement supprimé avec succès' });

    } catch (error) {
        console.error('Error deleting payment:', error);
        res.status(500).json({ error: 'Erreur lors de la suppression du paiement' });
    }
});

// POST /api/payments/process-release - Process payment and release vehicle
router.post('/process-release', async (req, res) => {
    try {
        const {
            vehicle_id, amount, payment_date, receipt_number, payment_method, notes,
            release_order_number, release_date, released_to_name, released_to_id, released_to_phone
        } = req.body;

        // Validate required fields
        if (!vehicle_id || !amount || !payment_date || !receipt_number || !release_order_number) {
            return res.status(400).json({ error: 'Champs requis manquants' });
        }

        // Use payment_date as release_date if not provided
        const finalReleaseDate = release_date || payment_date;

        // Get vehicle information
        const [vehicle] = await db.executeQuery(
            'SELECT *, DATEDIFF(?, entry_date) as storage_days FROM vehicles WHERE id = ?',
            [payment_date, vehicle_id]
        );

        if (!vehicle) {
            return res.status(404).json({ error: 'Véhicule non trouvé' });
        }

        // Start transaction
        const queries = [
            // Insert payment (payment_method is now optional, default to 'cash')
            {
                query: 'INSERT INTO payments (vehicle_id, amount, payment_date, receipt_number, payment_method, notes) VALUES (?, ?, ?, ?, ?, ?)',
                params: [vehicle_id, amount, payment_date, receipt_number, payment_method || 'cash', notes]
            },
            // Insert release record (released_to_name and phone are now optional)
            {
                query: 'INSERT INTO vehicle_releases (vehicle_id, release_order_number, release_date, released_to_name, released_to_id, released_to_phone, total_storage_days, total_amount, payment_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, LAST_INSERT_ID())',
                params: [vehicle_id, release_order_number, finalReleaseDate, released_to_name || 'Non spécifié', released_to_id, released_to_phone, vehicle.storage_days, amount]
            },
            // Update vehicle status to pending_release (en attente de sortie)
            {
                query: 'UPDATE vehicles SET status = "pending_release", updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                params: [vehicle_id]
            }
        ];

        await db.executeTransaction(queries);

        res.json({
            message: 'Paiement traité avec succès. Le véhicule est maintenant en attente de sortie.',
            storage_days: vehicle.storage_days,
            total_amount: amount,
            new_status: 'pending_release'
        });

    } catch (error) {
        console.error('Error processing release:', error);
        if (error.code === 'ER_DUP_ENTRY') {
            res.status(400).json({ error: 'Numéro de quittance ou d\'ordre de sortie déjà existant' });
        } else {
            res.status(500).json({ error: 'Erreur lors du traitement de la sortie' });
        }
    }
});

// GET /api/payments/calculate/:vehicle_id - Calculate payment amount for vehicle
router.get('/calculate/:vehicle_id', async (req, res) => {
    try {
        const { vehicle_id } = req.params;
        const { until_date } = req.query;

        const [vehicle] = await db.executeQuery(
            'SELECT entry_date, storage_cost_per_day FROM vehicles WHERE id = ?',
            [vehicle_id]
        );

        if (!vehicle) {
            return res.status(404).json({ error: 'Véhicule non trouvé' });
        }

        const endDate = until_date ? moment(until_date) : moment();
        const entryDate = moment(vehicle.entry_date);
        const storageDays = endDate.diff(entryDate, 'days') + 1; // Include entry day
        const totalAmount = storageDays * vehicle.storage_cost_per_day;

        res.json({
            entry_date: vehicle.entry_date,
            until_date: endDate.format('YYYY-MM-DD'),
            storage_days: storageDays,
            cost_per_day: vehicle.storage_cost_per_day,
            total_amount: totalAmount
        });

    } catch (error) {
        console.error('Error calculating payment:', error);
        res.status(500).json({ error: 'Erreur lors du calcul du paiement' });
    }
});

module.exports = router;
