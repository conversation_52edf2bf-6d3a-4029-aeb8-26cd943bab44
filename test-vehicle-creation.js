// Simple test for vehicle creation
const http = require('http');

function createVehicle(vehicleData) {
    return new Promise((resolve, reject) => {
        const postData = JSON.stringify(vehicleData);
        
        const options = {
            hostname: 'localhost',
            port: 3000,
            path: '/api/vehicles',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(postData)
            }
        };

        const req = http.request(options, (res) => {
            let body = '';
            res.on('data', (chunk) => {
                body += chunk;
            });
            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(body);
                    resolve({ status: res.statusCode, data: jsonData });
                } catch (e) {
                    resolve({ status: res.statusCode, data: body });
                }
            });
        });

        req.on('error', (err) => {
            reject(err);
        });

        req.write(postData);
        req.end();
    });
}

async function testVehicleCreation() {
    console.log('🧪 Testing Vehicle Creation...\n');

    const testVehicle = {
        depot_number: `TEST${Date.now()}`,
        license_plate: 'TEST-123',
        vehicle_type_id: 1,
        depositor: 'Police Municipale',
        brand: 'Toyota',
        entry_date: '2023-12-08',
        observations: 'Test du formulaire simplifié'
    };

    console.log('📝 Test vehicle data:');
    console.log(JSON.stringify(testVehicle, null, 2));

    try {
        const result = await createVehicle(testVehicle);
        console.log(`\n📊 Response status: ${result.status}`);
        console.log('📄 Response data:');
        console.log(JSON.stringify(result.data, null, 2));

        if (result.status === 201) {
            console.log('\n✅ Vehicle creation successful!');
        } else {
            console.log('\n❌ Vehicle creation failed!');
            if (result.data.errors) {
                console.log('Validation errors:');
                result.data.errors.forEach(error => {
                    console.log(`  - ${error.msg} (${error.param})`);
                });
            }
        }
    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

testVehicleCreation();
