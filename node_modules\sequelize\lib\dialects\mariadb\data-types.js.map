{"version": 3, "sources": ["../../../src/dialects/mariadb/data-types.js"], "sourcesContent": ["'use strict';\n\nconst wkx = require('wkx');\nconst _ = require('lodash');\nconst momentTz = require('moment-timezone');\nconst moment = require('moment');\n\nmodule.exports = BaseTypes => {\n  BaseTypes.ABSTRACT.prototype.dialectTypes = 'https://mariadb.com/kb/en/library/resultset/#field-types';\n\n  /**\n   * types: [buffer_type, ...]\n   *\n   * @see documentation : https://mariadb.com/kb/en/library/resultset/#field-types\n   * @see connector implementation : https://github.com/MariaDB/mariadb-connector-nodejs/blob/master/lib/const/field-type.js\n   */\n\n  BaseTypes.DATE.types.mariadb = ['DATETIME'];\n  BaseTypes.STRING.types.mariadb = ['VAR_STRING'];\n  BaseTypes.CHAR.types.mariadb = ['STRING'];\n  BaseTypes.TEXT.types.mariadb = ['BLOB'];\n  BaseTypes.TINYINT.types.mariadb = ['TINY'];\n  BaseTypes.SMALLINT.types.mariadb = ['SHORT'];\n  BaseTypes.MEDIUMINT.types.mariadb = ['INT24'];\n  BaseTypes.INTEGER.types.mariadb = ['LONG'];\n  BaseTypes.BIGINT.types.mariadb = ['LONGLONG'];\n  BaseTypes.FLOAT.types.mariadb = ['FLOAT'];\n  BaseTypes.TIME.types.mariadb = ['TIME'];\n  BaseTypes.DATEONLY.types.mariadb = ['DATE'];\n  BaseTypes.BOOLEAN.types.mariadb = ['TINY'];\n  BaseTypes.BLOB.types.mariadb = ['TINYBLOB', 'BLOB', 'LONGBLOB'];\n  BaseTypes.DECIMAL.types.mariadb = ['NEWDECIMAL'];\n  BaseTypes.UUID.types.mariadb = false;\n  BaseTypes.ENUM.types.mariadb = false;\n  BaseTypes.REAL.types.mariadb = ['DOUBLE'];\n  BaseTypes.DOUBLE.types.mariadb = ['DOUBLE'];\n  BaseTypes.GEOMETRY.types.mariadb = ['GEOMETRY'];\n  BaseTypes.JSON.types.mariadb = ['JSON'];\n\n  class DECIMAL extends BaseTypes.DECIMAL {\n    toSql() {\n      let definition = super.toSql();\n      if (this._unsigned) {\n        definition += ' UNSIGNED';\n      }\n      if (this._zerofill) {\n        definition += ' ZEROFILL';\n      }\n      return definition;\n    }\n  }\n\n  class DATE extends BaseTypes.DATE {\n    toSql() {\n      return this._length ? `DATETIME(${this._length})` : 'DATETIME';\n    }\n    _stringify(date, options) {\n      if (!moment.isMoment(date)) {\n        date = this._applyTimezone(date, options);\n      }\n\n      return date.format('YYYY-MM-DD HH:mm:ss.SSS');\n    }\n    static parse(value, options) {\n      value = value.string();\n      if (value === null) {\n        return value;\n      }\n      if (momentTz.tz.zone(options.timezone)) {\n        value = momentTz.tz(value, options.timezone).toDate();\n      }\n      else {\n        value = new Date(`${value} ${options.timezone}`);\n      }\n      return value;\n    }\n  }\n\n  class DATEONLY extends BaseTypes.DATEONLY {\n    static parse(value) {\n      return value.string();\n    }\n  }\n\n  class UUID extends BaseTypes.UUID {\n    toSql() {\n      return 'CHAR(36) BINARY';\n    }\n  }\n\n  class GEOMETRY extends BaseTypes.GEOMETRY {\n    constructor(type, srid) {\n      super(type, srid);\n      if (_.isEmpty(this.type)) {\n        this.sqlType = this.key;\n      }\n      else {\n        this.sqlType = this.type;\n      }\n    }\n    static parse(value) {\n      value = value.buffer();\n      // Empty buffer, MySQL doesn't support POINT EMPTY\n      // check, https://dev.mysql.com/worklog/task/?id=2381\n      if (!value || value.length === 0) {\n        return null;\n      }\n      // For some reason, discard the first 4 bytes\n      value = value.slice(4);\n      return wkx.Geometry.parse(value).toGeoJSON({ shortCrs: true });\n    }\n    toSql() {\n      return this.sqlType;\n    }\n  }\n\n  class ENUM extends BaseTypes.ENUM {\n    toSql(options) {\n      return `ENUM(${this.values.map(value => options.escape(value)).join(', ')})`;\n    }\n  }\n\n  class JSONTYPE extends BaseTypes.JSON {\n    _stringify(value, options) {\n      return options.operation === 'where' && typeof value === 'string' ? value\n        : JSON.stringify(value);\n    }\n  }\n\n  return {\n    ENUM,\n    DATE,\n    DATEONLY,\n    UUID,\n    GEOMETRY,\n    DECIMAL,\n    JSON: JSONTYPE\n  };\n};\n"], "mappings": ";AAEA,MAAM,MAAM,QAAQ;AACpB,MAAM,IAAI,QAAQ;AAClB,MAAM,WAAW,QAAQ;AACzB,MAAM,SAAS,QAAQ;AAEvB,OAAO,UAAU,eAAa;AAC5B,YAAU,SAAS,UAAU,eAAe;AAS5C,YAAU,KAAK,MAAM,UAAU,CAAC;AAChC,YAAU,OAAO,MAAM,UAAU,CAAC;AAClC,YAAU,KAAK,MAAM,UAAU,CAAC;AAChC,YAAU,KAAK,MAAM,UAAU,CAAC;AAChC,YAAU,QAAQ,MAAM,UAAU,CAAC;AACnC,YAAU,SAAS,MAAM,UAAU,CAAC;AACpC,YAAU,UAAU,MAAM,UAAU,CAAC;AACrC,YAAU,QAAQ,MAAM,UAAU,CAAC;AACnC,YAAU,OAAO,MAAM,UAAU,CAAC;AAClC,YAAU,MAAM,MAAM,UAAU,CAAC;AACjC,YAAU,KAAK,MAAM,UAAU,CAAC;AAChC,YAAU,SAAS,MAAM,UAAU,CAAC;AACpC,YAAU,QAAQ,MAAM,UAAU,CAAC;AACnC,YAAU,KAAK,MAAM,UAAU,CAAC,YAAY,QAAQ;AACpD,YAAU,QAAQ,MAAM,UAAU,CAAC;AACnC,YAAU,KAAK,MAAM,UAAU;AAC/B,YAAU,KAAK,MAAM,UAAU;AAC/B,YAAU,KAAK,MAAM,UAAU,CAAC;AAChC,YAAU,OAAO,MAAM,UAAU,CAAC;AAClC,YAAU,SAAS,MAAM,UAAU,CAAC;AACpC,YAAU,KAAK,MAAM,UAAU,CAAC;AAEhC,wBAAsB,UAAU,QAAQ;AAAA,IACtC,QAAQ;AACN,UAAI,aAAa,MAAM;AACvB,UAAI,KAAK,WAAW;AAClB,sBAAc;AAAA;AAEhB,UAAI,KAAK,WAAW;AAClB,sBAAc;AAAA;AAEhB,aAAO;AAAA;AAAA;AAIX,qBAAmB,UAAU,KAAK;AAAA,IAChC,QAAQ;AACN,aAAO,KAAK,UAAU,YAAY,KAAK,aAAa;AAAA;AAAA,IAEtD,WAAW,MAAM,SAAS;AACxB,UAAI,CAAC,OAAO,SAAS,OAAO;AAC1B,eAAO,KAAK,eAAe,MAAM;AAAA;AAGnC,aAAO,KAAK,OAAO;AAAA;AAAA,WAEd,MAAM,OAAO,SAAS;AAC3B,cAAQ,MAAM;AACd,UAAI,UAAU,MAAM;AAClB,eAAO;AAAA;AAET,UAAI,SAAS,GAAG,KAAK,QAAQ,WAAW;AACtC,gBAAQ,SAAS,GAAG,OAAO,QAAQ,UAAU;AAAA,aAE1C;AACH,gBAAQ,IAAI,KAAK,GAAG,SAAS,QAAQ;AAAA;AAEvC,aAAO;AAAA;AAAA;AAIX,yBAAuB,UAAU,SAAS;AAAA,WACjC,MAAM,OAAO;AAClB,aAAO,MAAM;AAAA;AAAA;AAIjB,qBAAmB,UAAU,KAAK;AAAA,IAChC,QAAQ;AACN,aAAO;AAAA;AAAA;AAIX,yBAAuB,UAAU,SAAS;AAAA,IACxC,YAAY,MAAM,MAAM;AACtB,YAAM,MAAM;AACZ,UAAI,EAAE,QAAQ,KAAK,OAAO;AACxB,aAAK,UAAU,KAAK;AAAA,aAEjB;AACH,aAAK,UAAU,KAAK;AAAA;AAAA;AAAA,WAGjB,MAAM,OAAO;AAClB,cAAQ,MAAM;AAGd,UAAI,CAAC,SAAS,MAAM,WAAW,GAAG;AAChC,eAAO;AAAA;AAGT,cAAQ,MAAM,MAAM;AACpB,aAAO,IAAI,SAAS,MAAM,OAAO,UAAU,EAAE,UAAU;AAAA;AAAA,IAEzD,QAAQ;AACN,aAAO,KAAK;AAAA;AAAA;AAIhB,qBAAmB,UAAU,KAAK;AAAA,IAChC,MAAM,SAAS;AACb,aAAO,QAAQ,KAAK,OAAO,IAAI,WAAS,QAAQ,OAAO,QAAQ,KAAK;AAAA;AAAA;AAIxE,yBAAuB,UAAU,KAAK;AAAA,IACpC,WAAW,OAAO,SAAS;AACzB,aAAO,QAAQ,cAAc,WAAW,OAAO,UAAU,WAAW,QAChE,KAAK,UAAU;AAAA;AAAA;AAIvB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM;AAAA;AAAA;", "names": []}