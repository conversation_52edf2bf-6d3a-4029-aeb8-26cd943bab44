// Debug script for vehicle form issues
console.log('🔧 Debugging Vehicle Form Issues...\n');

async function debugVehicleForm() {
    try {
        // Test 1: Check form structure
        console.log('1. Checking form structure...');
        const form = document.getElementById('addVehicleForm');
        console.log(`   📋 Form exists: ${form ? '✅ OK' : '❌ MISSING'}`);
        
        if (form) {
            const inputs = form.querySelectorAll('input, select, textarea');
            console.log(`   📋 Total form inputs: ${inputs.length}`);
            
            inputs.forEach((input, index) => {
                const name = input.name || input.id || `unnamed-${index}`;
                const type = input.type || input.tagName.toLowerCase();
                const required = input.required ? '(required)' : '';
                console.log(`      ${index + 1}. ${name} [${type}] ${required}`);
            });
        }

        // Test 2: Check if dropdowns are loaded
        console.log('\n2. Checking dropdown data...');
        
        const vehicleTypeSelect = document.querySelector('select[name="vehicle_type_id"]');
        if (vehicleTypeSelect) {
            console.log(`   📋 Vehicle types: ${vehicleTypeSelect.options.length} options`);
            for (let i = 0; i < vehicleTypeSelect.options.length; i++) {
                const option = vehicleTypeSelect.options[i];
                console.log(`      ${i + 1}. "${option.text}" (value: ${option.value})`);
            }
        }

        const depositorSelect = document.querySelector('select[name="depositor"]');
        if (depositorSelect) {
            console.log(`   📋 Depositors: ${depositorSelect.options.length} options`);
            for (let i = 0; i < depositorSelect.options.length; i++) {
                const option = depositorSelect.options[i];
                console.log(`      ${i + 1}. "${option.text}" (value: ${option.value})`);
            }
        }

        // Test 3: Test API endpoints directly
        console.log('\n3. Testing API endpoints...');
        
        try {
            const response = await fetch('/api/vehicle-types');
            console.log(`   📋 Vehicle types API: ${response.status} ${response.statusText}`);
            if (response.ok) {
                const data = await response.json();
                console.log(`      Response: ${JSON.stringify(data, null, 2)}`);
            } else {
                const errorText = await response.text();
                console.log(`      Error: ${errorText}`);
            }
        } catch (error) {
            console.log(`   ❌ Vehicle types API error: ${error.message}`);
        }

        try {
            const response = await fetch('/api/depositors');
            console.log(`   📋 Depositors API: ${response.status} ${response.statusText}`);
            if (response.ok) {
                const data = await response.json();
                console.log(`      Response: ${JSON.stringify(data, null, 2)}`);
            } else {
                const errorText = await response.text();
                console.log(`      Error: ${errorText}`);
            }
        } catch (error) {
            console.log(`   ❌ Depositors API error: ${error.message}`);
        }

        // Test 4: Test vehicle creation API
        console.log('\n4. Testing vehicle creation API...');
        
        const testVehicle = {
            depot_number: `DEBUG${Date.now()}`,
            license_plate: 'DEBUG-123',
            vehicle_type_id: 1,
            depositor: 'Police Municipale',
            brand: 'Toyota',
            entry_date: new Date().toISOString().split('T')[0],
            observations: 'Test de debug'
        };

        try {
            const response = await fetch('/api/vehicles', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(testVehicle)
            });
            
            console.log(`   📋 Vehicle creation API: ${response.status} ${response.statusText}`);
            
            if (response.ok) {
                const data = await response.json();
                console.log(`      Success: ${JSON.stringify(data, null, 2)}`);
            } else {
                const errorText = await response.text();
                console.log(`      Error: ${errorText}`);
            }
        } catch (error) {
            console.log(`   ❌ Vehicle creation API error: ${error.message}`);
        }

        // Test 5: Check app instance
        console.log('\n5. Checking app instance...');
        console.log(`   📋 Window.app exists: ${window.app ? '✅ OK' : '❌ MISSING'}`);
        
        if (window.app) {
            const methods = ['handleAddVehicle', 'apiCall', 'loadVehicleTypes', 'loadDepositors', 'showError', 'showSuccess'];
            methods.forEach(method => {
                console.log(`   📋 app.${method}: ${typeof window.app[method] === 'function' ? '✅ OK' : '❌ MISSING'}`);
            });
        }

        console.log('\n🎉 Debug completed!');

    } catch (error) {
        console.error('❌ Debug failed:', error);
    }
}

// Run debug when ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(debugVehicleForm, 1000);
    });
} else {
    setTimeout(debugVehicleForm, 1000);
}
