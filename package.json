{"name": "fourriere-management", "version": "1.0.0", "description": "Vehicle Impound Lot Management System", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "migrate": "node scripts/migrate.js"}, "keywords": ["vehicle", "impound", "management", "fourriere"], "author": "Fourriere Management System", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "connect-session-sequelize": "^7.1.7", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-session": "^1.18.1", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.5", "uuid": "^9.0.1"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}