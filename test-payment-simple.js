// Simple payment test to verify fixes
console.log('🧪 Simple Payment Test Loading...\n');

function testPaymentSimple() {
    console.log('=== SIMPLE PAYMENT TEST ===\n');
    
    // Test with minimal data to see if undefined issue is fixed
    const testData = {
        vehicle_id: 6,
        amount: 100,
        payment_date: '2025-06-09',
        receipt_number: 'TEST-SIMPLE-123',
        release_order_number: 'ORD-SIMPLE-123'
        // Intentionally NOT including optional fields to test undefined handling
    };
    
    console.log('🔄 Testing with minimal data (no optional fields):', testData);
    
    fetch('/api/payments/process-release', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(testData)
    })
    .then(response => {
        console.log(`\n📊 Response status: ${response.status} ${response.statusText}`);
        return response.text();
    })
    .then(responseText => {
        console.log('📄 Response body:', responseText);
        
        try {
            const result = JSON.parse(responseText);
            if (result.message) {
                console.log('🎉 SUCCESS! Payment processed:', result.message);
                console.log('✅ Vehicle ID:', result.vehicle_id);
                console.log('✅ Amount:', result.total_amount);
                console.log('✅ Status:', result.new_status);
            } else if (result.error) {
                console.error('❌ API Error:', result.error);
                if (result.missing_fields) {
                    console.error('Missing fields:', result.missing_fields);
                }
                if (result.received_data) {
                    console.error('Data received by server:', result.received_data);
                }
            }
        } catch (parseError) {
            console.error('❌ Failed to parse response as JSON');
            console.log('Raw response:', responseText);
        }
    })
    .catch(error => {
        console.error('❌ Network error:', error.message);
    });
}

function createSimpleTestButton() {
    const testBtn = document.createElement('button');
    testBtn.innerHTML = '🧪 Simple Payment Test';
    testBtn.className = 'bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded m-2';
    testBtn.onclick = testPaymentSimple;
    
    // Add to page
    const header = document.querySelector('h1, h2, .text-2xl');
    if (header && header.parentNode) {
        header.parentNode.insertBefore(testBtn, header.nextSibling);
        console.log('🧪 Simple payment test button added');
    }
}

// Auto-run setup
setTimeout(() => {
    console.log('🚀 Setting up simple payment test...');
    createSimpleTestButton();
    
    console.log('\n📋 Instructions:');
    console.log('1. Click "🧪 Simple Payment Test" to test with minimal data');
    console.log('2. This will test if the undefined parameter issue is fixed');
    console.log('3. Watch the console for results');
    console.log('4. Check server logs for detailed processing info');
    
}, 10000); // Wait 10 seconds
