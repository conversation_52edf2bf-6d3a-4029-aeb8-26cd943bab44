require=function o(s,a,h){function u(e,t){if(!a[e]){if(!s[e]){var r="function"==typeof require&&require;if(!t&&r)return r(e,!0);if(p)return p(e,!0);var n=new Error("Cannot find module '"+e+"'");throw n.code="MODULE_NOT_FOUND",n}var i=a[e]={exports:{}};s[e][0].call(i.exports,function(t){return u(s[e][1][t]||t)},i,i.exports,o,s,a,h)}return a[e].exports}for(var p="function"==typeof require&&require,t=0;t<h.length;t++)u(h[t]);return u}({1:[function(t,n,e){(function(t){function e(t,e){this.buffer=t,this.position=0,this.isBigEndian=e||!1}function r(e,r,n){return function(){var t;return t=this.isBigEndian?r.call(this.buffer,this.position):e.call(this.buffer,this.position),this.position+=n,t}}(n.exports=e).prototype.readUInt8=r(t.prototype.readUInt8,t.prototype.readUInt8,1),e.prototype.readUInt16=r(t.prototype.readUInt16LE,t.prototype.readUInt16BE,2),e.prototype.readUInt32=r(t.prototype.readUInt32LE,t.prototype.readUInt32BE,4),e.prototype.readInt8=r(t.prototype.readInt8,t.prototype.readInt8,1),e.prototype.readInt16=r(t.prototype.readInt16LE,t.prototype.readInt16BE,2),e.prototype.readInt32=r(t.prototype.readInt32LE,t.prototype.readInt32BE,4),e.prototype.readFloat=r(t.prototype.readFloatLE,t.prototype.readFloatBE,4),e.prototype.readDouble=r(t.prototype.readDoubleLE,t.prototype.readDoubleBE,8),e.prototype.readVarInt=function(){for(var t,e=0,r=0;e+=(127&(t=this.buffer[this.position+r]))<<7*r,r++,128<=t;);return this.position+=r,e}}).call(this,t("buffer").Buffer)},{buffer:"buffer"}],2:[function(t,n,e){(function(r){function t(t,e){this.buffer=new r(t),this.position=0,this.allowResize=e}function e(r,n){return function(t,e){this.ensureSize(n),r.call(this.buffer,t,this.position,e),this.position+=n}}(n.exports=t).prototype.writeUInt8=e(r.prototype.writeUInt8,1),t.prototype.writeUInt16LE=e(r.prototype.writeUInt16LE,2),t.prototype.writeUInt16BE=e(r.prototype.writeUInt16BE,2),t.prototype.writeUInt32LE=e(r.prototype.writeUInt32LE,4),t.prototype.writeUInt32BE=e(r.prototype.writeUInt32BE,4),t.prototype.writeInt8=e(r.prototype.writeInt8,1),t.prototype.writeInt16LE=e(r.prototype.writeInt16LE,2),t.prototype.writeInt16BE=e(r.prototype.writeInt16BE,2),t.prototype.writeInt32LE=e(r.prototype.writeInt32LE,4),t.prototype.writeInt32BE=e(r.prototype.writeInt32BE,4),t.prototype.writeFloatLE=e(r.prototype.writeFloatLE,4),t.prototype.writeFloatBE=e(r.prototype.writeFloatBE,4),t.prototype.writeDoubleLE=e(r.prototype.writeDoubleLE,8),t.prototype.writeDoubleBE=e(r.prototype.writeDoubleBE,8),t.prototype.writeBuffer=function(t){this.ensureSize(t.length),t.copy(this.buffer,this.position,0,t.length),this.position+=t.length},t.prototype.writeVarInt=function(t){for(var e=1;0!=(4294967168&t);)this.writeUInt8(127&t|128),t>>>=7,e++;return this.writeUInt8(127&t),e},t.prototype.ensureSize=function(t){if(this.buffer.length<this.position+t){if(!this.allowResize)throw new RangeError("index out of range");var e=new r(this.position+t);this.buffer.copy(e,0,0,this.buffer.length),this.buffer=e}}}).call(this,t("buffer").Buffer)},{buffer:"buffer"}],3:[function(t,e,r){(function(r){e.exports=i;var u=t("./types"),p=t("./point"),f=t("./linestring"),c=t("./polygon"),l=t("./multipoint"),g=t("./multilinestring"),y=t("./multipolygon"),w=t("./geometrycollection"),d=t("./binaryreader"),n=t("./binarywriter"),a=t("./wktparser"),b=t("./zigzag.js");function i(){this.srid=void 0,this.hasZ=!1,this.hasM=!1}i.parse=function(t,e){if("string"==typeof t||t instanceof a)return i._parseWkt(t);if(r.isBuffer(t)||t instanceof d)return i._parseWkb(t,e);throw new Error("first argument must be a string or Buffer")},i._parseWkt=function(t){var e,r,n=(e=t instanceof a?t:new a(t)).matchRegex([/^SRID=(\d+);/]);n&&(r=parseInt(n[1],10));var i=e.matchType(),o=e.matchDimension(),s={srid:r,hasZ:o.hasZ,hasM:o.hasM};switch(i){case u.wkt.Point:return p._parseWkt(e,s);case u.wkt.LineString:return f._parseWkt(e,s);case u.wkt.Polygon:return c._parseWkt(e,s);case u.wkt.MultiPoint:return l._parseWkt(e,s);case u.wkt.MultiLineString:return g._parseWkt(e,s);case u.wkt.MultiPolygon:return y._parseWkt(e,s);case u.wkt.GeometryCollection:return w._parseWkt(e,s)}},i._parseWkb=function(t,e){var r,n,i,o={};switch((r=t instanceof d?t:new d(t)).isBigEndian=!r.readInt8(),n=r.readUInt32(),o.hasSrid=536870912==(536870912&n),o.isEwkb=536870912&n||1073741824&n||2147483648&n,o.hasSrid&&(o.srid=r.readUInt32()),o.hasZ=!1,o.hasM=!1,i=o.isEwkb||e&&e.isEwkb?(2147483648&n&&(o.hasZ=!0),1073741824&n&&(o.hasM=!0),15&n):1e3<=n&&n<2e3?(o.hasZ=!0,n-1e3):2e3<=n&&n<3e3?(o.hasM=!0,n-2e3):3e3<=n&&n<4e3?(o.hasZ=!0,o.hasM=!0,n-3e3):n){case u.wkb.Point:return p._parseWkb(r,o);case u.wkb.LineString:return f._parseWkb(r,o);case u.wkb.Polygon:return c._parseWkb(r,o);case u.wkb.MultiPoint:return l._parseWkb(r,o);case u.wkb.MultiLineString:return g._parseWkb(r,o);case u.wkb.MultiPolygon:return y._parseWkb(r,o);case u.wkb.GeometryCollection:return w._parseWkb(r,o);default:throw new Error("GeometryType "+i+" not supported")}},i.parseTwkb=function(t){var e,r={},n=(e=t instanceof d?t:new d(t)).readUInt8(),i=e.readUInt8(),o=15&n;if(r.precision=b.decode(n>>4),r.precisionFactor=Math.pow(10,r.precision),r.hasBoundingBox=i>>0&1,r.hasSizeAttribute=i>>1&1,r.hasIdList=i>>2&1,r.hasExtendedPrecision=i>>3&1,r.isEmpty=i>>4&1,r.hasExtendedPrecision){var s=e.readUInt8();r.hasZ=1==(1&s),r.hasM=2==(2&s),r.zPrecision=b.decode((28&s)>>2),r.zPrecisionFactor=Math.pow(10,r.zPrecision),r.mPrecision=b.decode((224&s)>>5),r.mPrecisionFactor=Math.pow(10,r.mPrecision)}else r.hasZ=!1,r.hasM=!1;if(r.hasSizeAttribute&&e.readVarInt(),r.hasBoundingBox){var a=2;r.hasZ&&a++,r.hasM&&a++;for(var h=0;h<a;h++)e.readVarInt(),e.readVarInt()}switch(o){case u.wkb.Point:return p._parseTwkb(e,r);case u.wkb.LineString:return f._parseTwkb(e,r);case u.wkb.Polygon:return c._parseTwkb(e,r);case u.wkb.MultiPoint:return l._parseTwkb(e,r);case u.wkb.MultiLineString:return g._parseTwkb(e,r);case u.wkb.MultiPolygon:return y._parseTwkb(e,r);case u.wkb.GeometryCollection:return w._parseTwkb(e,r);default:throw new Error("GeometryType "+o+" not supported")}},i.parseGeoJSON=function(t){return i._parseGeoJSON(t)},i._parseGeoJSON=function(t,e){var r;switch(t.type){case u.geoJSON.Point:r=p._parseGeoJSON(t);break;case u.geoJSON.LineString:r=f._parseGeoJSON(t);break;case u.geoJSON.Polygon:r=c._parseGeoJSON(t);break;case u.geoJSON.MultiPoint:r=l._parseGeoJSON(t);break;case u.geoJSON.MultiLineString:r=g._parseGeoJSON(t);break;case u.geoJSON.MultiPolygon:r=y._parseGeoJSON(t);break;case u.geoJSON.GeometryCollection:r=w._parseGeoJSON(t);break;default:throw new Error("GeometryType "+t.type+" not supported")}if(t.crs&&t.crs.type&&"name"===t.crs.type&&t.crs.properties&&t.crs.properties.name){var n=t.crs.properties.name;if(0===n.indexOf("EPSG:"))r.srid=parseInt(n.substring(5));else{if(0!==n.indexOf("urn:ogc:def:crs:EPSG::"))throw new Error("Unsupported crs: "+n);r.srid=parseInt(n.substring(22))}}else e||(r.srid=4326);return r},i.prototype.toEwkt=function(){return"SRID="+this.srid+";"+this.toWkt()},i.prototype.toEwkb=function(){var t=new n(this._getWkbSize()+4),e=this.toWkb();return t.writeInt8(1),t.writeUInt32LE((536870912|e.slice(1,5).readUInt32LE(0))>>>0,!0),t.writeUInt32LE(this.srid),t.writeBuffer(e.slice(5)),t.buffer},i.prototype._getWktType=function(t,e){var r=t;return this.hasZ&&this.hasM?r+=" ZM ":this.hasZ?r+=" Z ":this.hasM&&(r+=" M "),!e||this.hasZ||this.hasM||(r+=" "),e&&(r+="EMPTY"),r},i.prototype._getWktCoordinate=function(t){var e=t.x+" "+t.y;return this.hasZ&&(e+=" "+t.z),this.hasM&&(e+=" "+t.m),e},i.prototype._writeWkbType=function(t,e,r){var n=0;void 0!==this.srid||r&&void 0!==r.srid?(this.hasZ&&(n|=2147483648),this.hasM&&(n|=1073741824)):this.hasZ&&this.hasM?n+=3e3:this.hasZ?n+=1e3:this.hasM&&(n+=2e3),t.writeUInt32LE(n+e>>>0,!0)},i.getTwkbPrecision=function(t,e,r){return{xy:t,z:e,m:r,xyFactor:Math.pow(10,t),zFactor:Math.pow(10,e),mFactor:Math.pow(10,r)}},i.prototype._writeTwkbHeader=function(t,e,r,n){var i=(b.encode(r.xy)<<4)+e,o=(this.hasZ||this.hasM)<<3;if(o+=n<<4,t.writeUInt8(i),t.writeUInt8(o),this.hasZ||this.hasM){var s=0;this.hasZ&&(s|=1),this.hasM&&(s|=2),t.writeUInt8(s)}},i.prototype.toGeoJSON=function(t){var e={};return this.srid&&t&&(t.shortCrs?e.crs={type:"name",properties:{name:"EPSG:"+this.srid}}:t.longCrs&&(e.crs={type:"name",properties:{name:"urn:ogc:def:crs:EPSG::"+this.srid}})),e}}).call(this,{isBuffer:t("../node_modules/is-buffer/index.js")})},{"../node_modules/is-buffer/index.js":17,"./binaryreader":1,"./binarywriter":2,"./geometrycollection":4,"./linestring":5,"./multilinestring":6,"./multipoint":7,"./multipolygon":8,"./point":9,"./polygon":10,"./types":11,"./wktparser":12,"./zigzag.js":13}],4:[function(t,e,r){e.exports=a;var n=t("util"),i=t("./types"),o=t("./geometry"),s=t("./binarywriter");function a(t,e){o.call(this),this.geometries=t||[],this.srid=e,0<this.geometries.length&&(this.hasZ=this.geometries[0].hasZ,this.hasM=this.geometries[0].hasM)}n.inherits(a,o),a.Z=function(t,e){var r=new a(t,e);return r.hasZ=!0,r},a.M=function(t,e){var r=new a(t,e);return r.hasM=!0,r},a.ZM=function(t,e){var r=new a(t,e);return r.hasZ=!0,r.hasM=!0,r},a._parseWkt=function(t,e){var r=new a;if(r.srid=e.srid,r.hasZ=e.hasZ,r.hasM=e.hasM,t.isMatch(["EMPTY"]))return r;for(t.expectGroupStart();r.geometries.push(o.parse(t)),t.isMatch([","]););return t.expectGroupEnd(),r},a._parseWkb=function(t,e){var r=new a;r.srid=e.srid,r.hasZ=e.hasZ,r.hasM=e.hasM;for(var n=t.readUInt32(),i=0;i<n;i++)r.geometries.push(o.parse(t,e));return r},a._parseTwkb=function(t,e){var r=new a;if(r.hasZ=e.hasZ,r.hasM=e.hasM,e.isEmpty)return r;for(var n=t.readVarInt(),i=0;i<n;i++)r.geometries.push(o.parseTwkb(t));return r},a._parseGeoJSON=function(t){for(var e=new a,r=0;r<t.geometries.length;r++)e.geometries.push(o._parseGeoJSON(t.geometries[r],!0));return 0<e.geometries.length&&(e.hasZ=e.geometries[0].hasZ),e},a.prototype.toWkt=function(){if(0===this.geometries.length)return this._getWktType(i.wkt.GeometryCollection,!0);for(var t=this._getWktType(i.wkt.GeometryCollection,!1)+"(",e=0;e<this.geometries.length;e++)t+=this.geometries[e].toWkt()+",";return t=t.slice(0,-1),t+=")"},a.prototype.toWkb=function(){var t=new s(this._getWkbSize());t.writeInt8(1),this._writeWkbType(t,i.wkb.GeometryCollection),t.writeUInt32LE(this.geometries.length);for(var e=0;e<this.geometries.length;e++)t.writeBuffer(this.geometries[e].toWkb({srid:this.srid}));return t.buffer},a.prototype.toTwkb=function(){var t=new s(0,!0),e=o.getTwkbPrecision(5,0,0),r=0===this.geometries.length;if(this._writeTwkbHeader(t,i.wkb.GeometryCollection,e,r),0<this.geometries.length){t.writeVarInt(this.geometries.length);for(var n=0;n<this.geometries.length;n++)t.writeBuffer(this.geometries[n].toTwkb())}return t.buffer},a.prototype._getWkbSize=function(){for(var t=9,e=0;e<this.geometries.length;e++)t+=this.geometries[e]._getWkbSize();return t},a.prototype.toGeoJSON=function(t){var e=o.prototype.toGeoJSON.call(this,t);e.type=i.geoJSON.GeometryCollection,e.geometries=[];for(var r=0;r<this.geometries.length;r++)e.geometries.push(this.geometries[r].toGeoJSON());return e}},{"./binarywriter":2,"./geometry":3,"./types":11,util:20}],5:[function(t,e,r){e.exports=u;var n=t("util"),o=t("./geometry"),s=t("./types"),a=t("./point"),h=t("./binarywriter");function u(t,e){o.call(this),this.points=t||[],this.srid=e,0<this.points.length&&(this.hasZ=this.points[0].hasZ,this.hasM=this.points[0].hasM)}n.inherits(u,o),u.Z=function(t,e){var r=new u(t,e);return r.hasZ=!0,r},u.M=function(t,e){var r=new u(t,e);return r.hasM=!0,r},u.ZM=function(t,e){var r=new u(t,e);return r.hasZ=!0,r.hasM=!0,r},u._parseWkt=function(t,e){var r=new u;return r.srid=e.srid,r.hasZ=e.hasZ,r.hasM=e.hasM,t.isMatch(["EMPTY"])||(t.expectGroupStart(),r.points.push.apply(r.points,t.matchCoordinates(e)),t.expectGroupEnd()),r},u._parseWkb=function(t,e){var r=new u;r.srid=e.srid,r.hasZ=e.hasZ,r.hasM=e.hasM;for(var n=t.readUInt32(),i=0;i<n;i++)r.points.push(a._readWkbPoint(t,e));return r},u._parseTwkb=function(t,e){var r=new u;if(r.hasZ=e.hasZ,r.hasM=e.hasM,e.isEmpty)return r;for(var n=new a(0,0,e.hasZ?0:void 0,e.hasM?0:void 0),i=t.readVarInt(),o=0;o<i;o++)r.points.push(a._readTwkbPoint(t,e,n));return r},u._parseGeoJSON=function(t){var e=new u;0<t.coordinates.length&&(e.hasZ=2<t.coordinates[0].length);for(var r=0;r<t.coordinates.length;r++)e.points.push(a._readGeoJSONPoint(t.coordinates[r]));return e},u.prototype.toWkt=function(){return 0===this.points.length?this._getWktType(s.wkt.LineString,!0):this._getWktType(s.wkt.LineString,!1)+this._toInnerWkt()},u.prototype._toInnerWkt=function(){for(var t="(",e=0;e<this.points.length;e++)t+=this._getWktCoordinate(this.points[e])+",";return t=t.slice(0,-1),t+=")"},u.prototype.toWkb=function(t){var e=new h(this._getWkbSize());e.writeInt8(1),this._writeWkbType(e,s.wkb.LineString,t),e.writeUInt32LE(this.points.length);for(var r=0;r<this.points.length;r++)this.points[r]._writeWkbPoint(e);return e.buffer},u.prototype.toTwkb=function(){var t=new h(0,!0),e=o.getTwkbPrecision(5,0,0),r=0===this.points.length;if(this._writeTwkbHeader(t,s.wkb.LineString,e,r),0<this.points.length){t.writeVarInt(this.points.length);for(var n=new a(0,0,0,0),i=0;i<this.points.length;i++)this.points[i]._writeTwkbPoint(t,e,n)}return t.buffer},u.prototype._getWkbSize=function(){var t=16;return this.hasZ&&(t+=8),this.hasM&&(t+=8),9+this.points.length*t},u.prototype.toGeoJSON=function(t){var e=o.prototype.toGeoJSON.call(this,t);e.type=s.geoJSON.LineString,e.coordinates=[];for(var r=0;r<this.points.length;r++)this.hasZ?e.coordinates.push([this.points[r].x,this.points[r].y,this.points[r].z]):e.coordinates.push([this.points[r].x,this.points[r].y]);return e}},{"./binarywriter":2,"./geometry":3,"./point":9,"./types":11,util:20}],6:[function(t,e,r){e.exports=f;var n=t("util"),s=t("./types"),a=t("./geometry"),u=t("./point"),p=t("./linestring"),h=t("./binarywriter");function f(t,e){a.call(this),this.lineStrings=t||[],this.srid=e,0<this.lineStrings.length&&(this.hasZ=this.lineStrings[0].hasZ,this.hasM=this.lineStrings[0].hasM)}n.inherits(f,a),f.Z=function(t,e){var r=new f(t,e);return r.hasZ=!0,r},f.M=function(t,e){var r=new f(t,e);return r.hasM=!0,r},f.ZM=function(t,e){var r=new f(t,e);return r.hasZ=!0,r.hasM=!0,r},f._parseWkt=function(t,e){var r=new f;if(r.srid=e.srid,r.hasZ=e.hasZ,r.hasM=e.hasM,t.isMatch(["EMPTY"]))return r;for(t.expectGroupStart();t.expectGroupStart(),r.lineStrings.push(new p(t.matchCoordinates(e))),t.expectGroupEnd(),t.isMatch([","]););return t.expectGroupEnd(),r},f._parseWkb=function(t,e){var r=new f;r.srid=e.srid,r.hasZ=e.hasZ,r.hasM=e.hasM;for(var n=t.readUInt32(),i=0;i<n;i++)r.lineStrings.push(a.parse(t,e));return r},f._parseTwkb=function(t,e){var r=new f;if(r.hasZ=e.hasZ,r.hasM=e.hasM,e.isEmpty)return r;for(var n=new u(0,0,e.hasZ?0:void 0,e.hasM?0:void 0),i=t.readVarInt(),o=0;o<i;o++){var s=new p;s.hasZ=e.hasZ,s.hasM=e.hasM;for(var a=t.readVarInt(),h=0;h<a;h++)s.points.push(u._readTwkbPoint(t,e,n));r.lineStrings.push(s)}return r},f._parseGeoJSON=function(t){var e=new f;0<t.coordinates.length&&0<t.coordinates[0].length&&(e.hasZ=2<t.coordinates[0][0].length);for(var r=0;r<t.coordinates.length;r++)e.lineStrings.push(p._parseGeoJSON({coordinates:t.coordinates[r]}));return e},f.prototype.toWkt=function(){if(0===this.lineStrings.length)return this._getWktType(s.wkt.MultiLineString,!0);for(var t=this._getWktType(s.wkt.MultiLineString,!1)+"(",e=0;e<this.lineStrings.length;e++)t+=this.lineStrings[e]._toInnerWkt()+",";return t=t.slice(0,-1),t+=")"},f.prototype.toWkb=function(){var t=new h(this._getWkbSize());t.writeInt8(1),this._writeWkbType(t,s.wkb.MultiLineString),t.writeUInt32LE(this.lineStrings.length);for(var e=0;e<this.lineStrings.length;e++)t.writeBuffer(this.lineStrings[e].toWkb({srid:this.srid}));return t.buffer},f.prototype.toTwkb=function(){var t=new h(0,!0),e=a.getTwkbPrecision(5,0,0),r=0===this.lineStrings.length;if(this._writeTwkbHeader(t,s.wkb.MultiLineString,e,r),0<this.lineStrings.length){t.writeVarInt(this.lineStrings.length);for(var n=new u(0,0,0,0),i=0;i<this.lineStrings.length;i++){t.writeVarInt(this.lineStrings[i].points.length);for(var o=0;o<this.lineStrings[i].points.length;o++)this.lineStrings[i].points[o]._writeTwkbPoint(t,e,n)}}return t.buffer},f.prototype._getWkbSize=function(){for(var t=9,e=0;e<this.lineStrings.length;e++)t+=this.lineStrings[e]._getWkbSize();return t},f.prototype.toGeoJSON=function(t){var e=a.prototype.toGeoJSON.call(this,t);e.type=s.geoJSON.MultiLineString,e.coordinates=[];for(var r=0;r<this.lineStrings.length;r++)e.coordinates.push(this.lineStrings[r].toGeoJSON().coordinates);return e}},{"./binarywriter":2,"./geometry":3,"./linestring":5,"./point":9,"./types":11,util:20}],7:[function(t,e,r){e.exports=u;var n=t("util"),o=t("./types"),s=t("./geometry"),a=t("./point"),h=t("./binarywriter");function u(t,e){s.call(this),this.points=t||[],this.srid=e,0<this.points.length&&(this.hasZ=this.points[0].hasZ,this.hasM=this.points[0].hasM)}n.inherits(u,s),u.Z=function(t,e){var r=new u(t,e);return r.hasZ=!0,r},u.M=function(t,e){var r=new u(t,e);return r.hasM=!0,r},u.ZM=function(t,e){var r=new u(t,e);return r.hasZ=!0,r.hasM=!0,r},u._parseWkt=function(t,e){var r=new u;return r.srid=e.srid,r.hasZ=e.hasZ,r.hasM=e.hasM,t.isMatch(["EMPTY"])||(t.expectGroupStart(),r.points.push.apply(r.points,t.matchCoordinates(e)),t.expectGroupEnd()),r},u._parseWkb=function(t,e){var r=new u;r.srid=e.srid,r.hasZ=e.hasZ,r.hasM=e.hasM;for(var n=t.readUInt32(),i=0;i<n;i++)r.points.push(s.parse(t,e));return r},u._parseTwkb=function(t,e){var r=new u;if(r.hasZ=e.hasZ,r.hasM=e.hasM,e.isEmpty)return r;for(var n=new a(0,0,e.hasZ?0:void 0,e.hasM?0:void 0),i=t.readVarInt(),o=0;o<i;o++)r.points.push(a._readTwkbPoint(t,e,n));return r},u._parseGeoJSON=function(t){var e=new u;0<t.coordinates.length&&(e.hasZ=2<t.coordinates[0].length);for(var r=0;r<t.coordinates.length;r++)e.points.push(a._parseGeoJSON({coordinates:t.coordinates[r]}));return e},u.prototype.toWkt=function(){if(0===this.points.length)return this._getWktType(o.wkt.MultiPoint,!0);for(var t=this._getWktType(o.wkt.MultiPoint,!1)+"(",e=0;e<this.points.length;e++)t+=this._getWktCoordinate(this.points[e])+",";return t=t.slice(0,-1),t+=")"},u.prototype.toWkb=function(){var t=new h(this._getWkbSize());t.writeInt8(1),this._writeWkbType(t,o.wkb.MultiPoint),t.writeUInt32LE(this.points.length);for(var e=0;e<this.points.length;e++)t.writeBuffer(this.points[e].toWkb({srid:this.srid}));return t.buffer},u.prototype.toTwkb=function(){var t=new h(0,!0),e=s.getTwkbPrecision(5,0,0),r=0===this.points.length;if(this._writeTwkbHeader(t,o.wkb.MultiPoint,e,r),0<this.points.length){t.writeVarInt(this.points.length);for(var n=new a(0,0,0,0),i=0;i<this.points.length;i++)this.points[i]._writeTwkbPoint(t,e,n)}return t.buffer},u.prototype._getWkbSize=function(){var t=16;return this.hasZ&&(t+=8),this.hasM&&(t+=8),t+=5,9+this.points.length*t},u.prototype.toGeoJSON=function(t){var e=s.prototype.toGeoJSON.call(this,t);e.type=o.geoJSON.MultiPoint,e.coordinates=[];for(var r=0;r<this.points.length;r++)e.coordinates.push(this.points[r].toGeoJSON().coordinates);return e}},{"./binarywriter":2,"./geometry":3,"./point":9,"./types":11,util:20}],8:[function(t,e,r){e.exports=y;var n=t("util"),a=t("./types"),h=t("./geometry"),l=t("./point"),g=t("./polygon"),u=t("./binarywriter");function y(t,e){h.call(this),this.polygons=t||[],this.srid=e,0<this.polygons.length&&(this.hasZ=this.polygons[0].hasZ,this.hasM=this.polygons[0].hasM)}n.inherits(y,h),y.Z=function(t,e){var r=new y(t,e);return r.hasZ=!0,r},y.M=function(t,e){var r=new y(t,e);return r.hasM=!0,r},y.ZM=function(t,e){var r=new y(t,e);return r.hasZ=!0,r.hasM=!0,r},y._parseWkt=function(t,e){var r=new y;if(r.srid=e.srid,r.hasZ=e.hasZ,r.hasM=e.hasM,t.isMatch(["EMPTY"]))return r;t.expectGroupStart();do{t.expectGroupStart();var n=[],i=[];for(t.expectGroupStart(),n.push.apply(n,t.matchCoordinates(e)),t.expectGroupEnd();t.isMatch([","]);)t.expectGroupStart(),i.push(t.matchCoordinates(e)),t.expectGroupEnd();r.polygons.push(new g(n,i)),t.expectGroupEnd()}while(t.isMatch([","]));return t.expectGroupEnd(),r},y._parseWkb=function(t,e){var r=new y;r.srid=e.srid,r.hasZ=e.hasZ,r.hasM=e.hasM;for(var n=t.readUInt32(),i=0;i<n;i++)r.polygons.push(h.parse(t,e));return r},y._parseTwkb=function(t,e){var r=new y;if(r.hasZ=e.hasZ,r.hasM=e.hasM,e.isEmpty)return r;for(var n=new l(0,0,e.hasZ?0:void 0,e.hasM?0:void 0),i=t.readVarInt(),o=0;o<i;o++){var s=new g;s.hasZ=e.hasZ,s.hasM=e.hasM;for(var a=t.readVarInt(),h=t.readVarInt(),u=0;u<h;u++)s.exteriorRing.push(l._readTwkbPoint(t,e,n));for(u=1;u<a;u++){for(var p=[],f=t.readVarInt(),c=0;c<f;c++)p.push(l._readTwkbPoint(t,e,n));s.interiorRings.push(p)}r.polygons.push(s)}return r},y._parseGeoJSON=function(t){var e=new y;0<t.coordinates.length&&0<t.coordinates[0].length&&0<t.coordinates[0][0].length&&(e.hasZ=2<t.coordinates[0][0][0].length);for(var r=0;r<t.coordinates.length;r++)e.polygons.push(g._parseGeoJSON({coordinates:t.coordinates[r]}));return e},y.prototype.toWkt=function(){if(0===this.polygons.length)return this._getWktType(a.wkt.MultiPolygon,!0);for(var t=this._getWktType(a.wkt.MultiPolygon,!1)+"(",e=0;e<this.polygons.length;e++)t+=this.polygons[e]._toInnerWkt()+",";return t=t.slice(0,-1),t+=")"},y.prototype.toWkb=function(){var t=new u(this._getWkbSize());t.writeInt8(1),this._writeWkbType(t,a.wkb.MultiPolygon),t.writeUInt32LE(this.polygons.length);for(var e=0;e<this.polygons.length;e++)t.writeBuffer(this.polygons[e].toWkb({srid:this.srid}));return t.buffer},y.prototype.toTwkb=function(){var t=new u(0,!0),e=h.getTwkbPrecision(5,0,0),r=0===this.polygons.length;if(this._writeTwkbHeader(t,a.wkb.MultiPolygon,e,r),0<this.polygons.length){t.writeVarInt(this.polygons.length);for(var n=new l(0,0,0,0),i=0;i<this.polygons.length;i++){t.writeVarInt(1+this.polygons[i].interiorRings.length),t.writeVarInt(this.polygons[i].exteriorRing.length);for(var o=0;o<this.polygons[i].exteriorRing.length;o++)this.polygons[i].exteriorRing[o]._writeTwkbPoint(t,e,n);for(o=0;o<this.polygons[i].interiorRings.length;o++){t.writeVarInt(this.polygons[i].interiorRings[o].length);for(var s=0;s<this.polygons[i].interiorRings[o].length;s++)this.polygons[i].interiorRings[o][s]._writeTwkbPoint(t,e,n)}}}return t.buffer},y.prototype._getWkbSize=function(){for(var t=9,e=0;e<this.polygons.length;e++)t+=this.polygons[e]._getWkbSize();return t},y.prototype.toGeoJSON=function(t){var e=h.prototype.toGeoJSON.call(this,t);e.type=a.geoJSON.MultiPolygon,e.coordinates=[];for(var r=0;r<this.polygons.length;r++)e.coordinates.push(this.polygons[r].toGeoJSON().coordinates);return e}},{"./binarywriter":2,"./geometry":3,"./point":9,"./polygon":10,"./types":11,util:20}],9:[function(t,e,r){e.exports=h;var n=t("util"),o=t("./geometry"),i=t("./types"),s=t("./binarywriter"),a=t("./zigzag.js");function h(t,e,r,n,i){o.call(this),this.x=t,this.y=e,this.z=r,this.m=n,this.srid=i,this.hasZ=void 0!==this.z,this.hasM=void 0!==this.m}n.inherits(h,o),h.Z=function(t,e,r,n){var i=new h(t,e,r,void 0,n);return i.hasZ=!0,i},h.M=function(t,e,r,n){var i=new h(t,e,void 0,r,n);return i.hasM=!0,i},h.ZM=function(t,e,r,n,i){var o=new h(t,e,r,n,i);return o.hasZ=!0,o.hasM=!0,o},h._parseWkt=function(t,e){var r=new h;if(r.srid=e.srid,r.hasZ=e.hasZ,r.hasM=e.hasM,t.isMatch(["EMPTY"]))return r;t.expectGroupStart();var n=t.matchCoordinate(e);return r.x=n.x,r.y=n.y,r.z=n.z,r.m=n.m,t.expectGroupEnd(),r},h._parseWkb=function(t,e){var r=h._readWkbPoint(t,e);return r.srid=e.srid,r},h._readWkbPoint=function(t,e){return new h(t.readDouble(),t.readDouble(),e.hasZ?t.readDouble():void 0,e.hasM?t.readDouble():void 0)},h._parseTwkb=function(t,e){var r=new h;return r.hasZ=e.hasZ,r.hasM=e.hasM,e.isEmpty||(r.x=a.decode(t.readVarInt())/e.precisionFactor,r.y=a.decode(t.readVarInt())/e.precisionFactor,r.z=e.hasZ?a.decode(t.readVarInt())/e.zPrecisionFactor:void 0,r.m=e.hasM?a.decode(t.readVarInt())/e.mPrecisionFactor:void 0),r},h._readTwkbPoint=function(t,e,r){return r.x+=a.decode(t.readVarInt())/e.precisionFactor,r.y+=a.decode(t.readVarInt())/e.precisionFactor,e.hasZ&&(r.z+=a.decode(t.readVarInt())/e.zPrecisionFactor),e.hasM&&(r.m+=a.decode(t.readVarInt())/e.mPrecisionFactor),new h(r.x,r.y,r.z,r.m)},h._parseGeoJSON=function(t){return h._readGeoJSONPoint(t.coordinates)},h._readGeoJSONPoint=function(t){return 0===t.length?new h:2<t.length?new h(t[0],t[1],t[2]):new h(t[0],t[1])},h.prototype.toWkt=function(){return void 0===this.x&&void 0===this.y&&void 0===this.z&&void 0===this.m?this._getWktType(i.wkt.Point,!0):this._getWktType(i.wkt.Point,!1)+"("+this._getWktCoordinate(this)+")"},h.prototype.toWkb=function(t){var e=new s(this._getWkbSize());return e.writeInt8(1),this._writeWkbType(e,i.wkb.Point,t),void 0===this.x&&void 0===this.y?(e.writeDoubleLE(NaN),e.writeDoubleLE(NaN),this.hasZ&&e.writeDoubleLE(NaN),this.hasM&&e.writeDoubleLE(NaN)):this._writeWkbPoint(e),e.buffer},h.prototype._writeWkbPoint=function(t){t.writeDoubleLE(this.x),t.writeDoubleLE(this.y),this.hasZ&&t.writeDoubleLE(this.z),this.hasM&&t.writeDoubleLE(this.m)},h.prototype.toTwkb=function(){var t=new s(0,!0),e=o.getTwkbPrecision(5,0,0),r=void 0===this.x&&void 0===this.y;return this._writeTwkbHeader(t,i.wkb.Point,e,r),r||this._writeTwkbPoint(t,e,new h(0,0,0,0)),t.buffer},h.prototype._writeTwkbPoint=function(t,e,r){var n=this.x*e.xyFactor,i=this.y*e.xyFactor,o=this.z*e.zFactor,s=this.m*e.mFactor;t.writeVarInt(a.encode(n-r.x)),t.writeVarInt(a.encode(i-r.y)),this.hasZ&&t.writeVarInt(a.encode(o-r.z)),this.hasM&&t.writeVarInt(a.encode(s-r.m)),r.x=n,r.y=i,r.z=o,r.m=s},h.prototype._getWkbSize=function(){var t=21;return this.hasZ&&(t+=8),this.hasM&&(t+=8),t},h.prototype.toGeoJSON=function(t){var e=o.prototype.toGeoJSON.call(this,t);return e.type=i.geoJSON.Point,void 0===this.x&&void 0===this.y?e.coordinates=[]:void 0!==this.z?e.coordinates=[this.x,this.y,this.z]:e.coordinates=[this.x,this.y],e}},{"./binarywriter":2,"./geometry":3,"./types":11,"./zigzag.js":13,util:20}],10:[function(t,e,r){e.exports=f;var n=t("util"),a=t("./geometry"),h=t("./types"),p=t("./point"),s=t("./binarywriter");function f(t,e,r){a.call(this),this.exteriorRing=t||[],this.interiorRings=e||[],this.srid=r,0<this.exteriorRing.length&&(this.hasZ=this.exteriorRing[0].hasZ,this.hasM=this.exteriorRing[0].hasM)}n.inherits(f,a),f.Z=function(t,e,r){var n=new f(t,e,r);return n.hasZ=!0,n},f.M=function(t,e,r){var n=new f(t,e,r);return n.hasM=!0,n},f.ZM=function(t,e,r){var n=new f(t,e,r);return n.hasZ=!0,n.hasM=!0,n},f._parseWkt=function(t,e){var r=new f;if(r.srid=e.srid,r.hasZ=e.hasZ,r.hasM=e.hasM,t.isMatch(["EMPTY"]))return r;for(t.expectGroupStart(),t.expectGroupStart(),r.exteriorRing.push.apply(r.exteriorRing,t.matchCoordinates(e)),t.expectGroupEnd();t.isMatch([","]);)t.expectGroupStart(),r.interiorRings.push(t.matchCoordinates(e)),t.expectGroupEnd();return t.expectGroupEnd(),r},f._parseWkb=function(t,e){var r=new f;r.srid=e.srid,r.hasZ=e.hasZ,r.hasM=e.hasM;var n=t.readUInt32();if(0<n){for(var i=t.readUInt32(),o=0;o<i;o++)r.exteriorRing.push(p._readWkbPoint(t,e));for(o=1;o<n;o++){for(var s=[],a=t.readUInt32(),h=0;h<a;h++)s.push(p._readWkbPoint(t,e));r.interiorRings.push(s)}}return r},f._parseTwkb=function(t,e){var r=new f;if(r.hasZ=e.hasZ,r.hasM=e.hasM,e.isEmpty)return r;for(var n=new p(0,0,e.hasZ?0:void 0,e.hasM?0:void 0),i=t.readVarInt(),o=t.readVarInt(),s=0;s<o;s++)r.exteriorRing.push(p._readTwkbPoint(t,e,n));for(s=1;s<i;s++){for(var a=[],h=t.readVarInt(),u=0;u<h;u++)a.push(p._readTwkbPoint(t,e,n));r.interiorRings.push(a)}return r},f._parseGeoJSON=function(t){var e=new f;0<t.coordinates.length&&0<t.coordinates[0].length&&(e.hasZ=2<t.coordinates[0][0].length);for(var r=0;r<t.coordinates.length;r++){0<r&&e.interiorRings.push([]);for(var n=0;n<t.coordinates[r].length;n++)0===r?e.exteriorRing.push(p._readGeoJSONPoint(t.coordinates[r][n])):e.interiorRings[r-1].push(p._readGeoJSONPoint(t.coordinates[r][n]))}return e},f.prototype.toWkt=function(){return 0===this.exteriorRing.length?this._getWktType(h.wkt.Polygon,!0):this._getWktType(h.wkt.Polygon,!1)+this._toInnerWkt()},f.prototype._toInnerWkt=function(){for(var t="((",e=0;e<this.exteriorRing.length;e++)t+=this._getWktCoordinate(this.exteriorRing[e])+",";for(t=t.slice(0,-1),t+=")",e=0;e<this.interiorRings.length;e++){t+=",(";for(var r=0;r<this.interiorRings[e].length;r++)t+=this._getWktCoordinate(this.interiorRings[e][r])+",";t=t.slice(0,-1),t+=")"}return t+=")"},f.prototype.toWkb=function(t){var e=new s(this._getWkbSize());e.writeInt8(1),this._writeWkbType(e,h.wkb.Polygon,t),0<this.exteriorRing.length?(e.writeUInt32LE(1+this.interiorRings.length),e.writeUInt32LE(this.exteriorRing.length)):e.writeUInt32LE(0);for(var r=0;r<this.exteriorRing.length;r++)this.exteriorRing[r]._writeWkbPoint(e);for(r=0;r<this.interiorRings.length;r++){e.writeUInt32LE(this.interiorRings[r].length);for(var n=0;n<this.interiorRings[r].length;n++)this.interiorRings[r][n]._writeWkbPoint(e)}return e.buffer},f.prototype.toTwkb=function(){var t=new s(0,!0),e=a.getTwkbPrecision(5,0,0),r=0===this.exteriorRing.length;if(this._writeTwkbHeader(t,h.wkb.Polygon,e,r),0<this.exteriorRing.length){t.writeVarInt(1+this.interiorRings.length),t.writeVarInt(this.exteriorRing.length);for(var n=new p(0,0,0,0),i=0;i<this.exteriorRing.length;i++)this.exteriorRing[i]._writeTwkbPoint(t,e,n);for(i=0;i<this.interiorRings.length;i++){t.writeVarInt(this.interiorRings[i].length);for(var o=0;o<this.interiorRings[i].length;o++)this.interiorRings[i][o]._writeTwkbPoint(t,e,n)}}return t.buffer},f.prototype._getWkbSize=function(){var t=16;this.hasZ&&(t+=8),this.hasM&&(t+=8);var e=9;0<this.exteriorRing.length&&(e+=4+this.exteriorRing.length*t);for(var r=0;r<this.interiorRings.length;r++)e+=4+this.interiorRings[r].length*t;return e},f.prototype.toGeoJSON=function(t){var e=a.prototype.toGeoJSON.call(this,t);if(e.type=h.geoJSON.Polygon,e.coordinates=[],0<this.exteriorRing.length){for(var r=[],n=0;n<this.exteriorRing.length;n++)this.hasZ?r.push([this.exteriorRing[n].x,this.exteriorRing[n].y,this.exteriorRing[n].z]):r.push([this.exteriorRing[n].x,this.exteriorRing[n].y]);e.coordinates.push(r)}for(var i=0;i<this.interiorRings.length;i++){for(var o=[],s=0;s<this.interiorRings[i].length;s++)this.hasZ?o.push([this.interiorRings[i][s].x,this.interiorRings[i][s].y,this.interiorRings[i][s].z]):o.push([this.interiorRings[i][s].x,this.interiorRings[i][s].y]);e.coordinates.push(o)}return e}},{"./binarywriter":2,"./geometry":3,"./point":9,"./types":11,util:20}],11:[function(t,e,r){e.exports={wkt:{Point:"POINT",LineString:"LINESTRING",Polygon:"POLYGON",MultiPoint:"MULTIPOINT",MultiLineString:"MULTILINESTRING",MultiPolygon:"MULTIPOLYGON",GeometryCollection:"GEOMETRYCOLLECTION"},wkb:{Point:1,LineString:2,Polygon:3,MultiPoint:4,MultiLineString:5,MultiPolygon:6,GeometryCollection:7},geoJSON:{Point:"Point",LineString:"LineString",Polygon:"Polygon",MultiPoint:"MultiPoint",MultiLineString:"MultiLineString",MultiPolygon:"MultiPolygon",GeometryCollection:"GeometryCollection"}}},{}],12:[function(t,e,r){e.exports=o;var n=t("./types"),i=t("./point");function o(t){this.value=t,this.position=0}o.prototype.match=function(t){this.skipWhitespaces();for(var e=0;e<t.length;e++)if(0===this.value.substring(this.position).indexOf(t[e]))return this.position+=t[e].length,t[e];return null},o.prototype.matchRegex=function(t){this.skipWhitespaces();for(var e=0;e<t.length;e++){var r=this.value.substring(this.position).match(t[e]);if(r)return this.position+=r[0].length,r}return null},o.prototype.isMatch=function(t){this.skipWhitespaces();for(var e=0;e<t.length;e++)if(0===this.value.substring(this.position).indexOf(t[e]))return this.position+=t[e].length,!0;return!1},o.prototype.matchType=function(){var t=this.match([n.wkt.Point,n.wkt.LineString,n.wkt.Polygon,n.wkt.MultiPoint,n.wkt.MultiLineString,n.wkt.MultiPolygon,n.wkt.GeometryCollection]);if(!t)throw new Error("Expected geometry type");return t},o.prototype.matchDimension=function(){switch(this.match(["ZM","Z","M"])){case"ZM":return{hasZ:!0,hasM:!0};case"Z":return{hasZ:!0,hasM:!1};case"M":return{hasZ:!1,hasM:!0};default:return{hasZ:!1,hasM:!1}}},o.prototype.expectGroupStart=function(){if(!this.isMatch(["("]))throw new Error("Expected group start")},o.prototype.expectGroupEnd=function(){if(!this.isMatch([")"]))throw new Error("Expected group end")},o.prototype.matchCoordinate=function(t){var e;if(!(e=t.hasZ&&t.hasM?this.matchRegex([/^(\S*)\s+(\S*)\s+(\S*)\s+([^\s,)]*)/]):t.hasZ||t.hasM?this.matchRegex([/^(\S*)\s+(\S*)\s+([^\s,)]*)/]):this.matchRegex([/^(\S*)\s+([^\s,)]*)/])))throw new Error("Expected coordinates");return t.hasZ&&t.hasM?new i(parseFloat(e[1]),parseFloat(e[2]),parseFloat(e[3]),parseFloat(e[4])):t.hasZ?new i(parseFloat(e[1]),parseFloat(e[2]),parseFloat(e[3])):t.hasM?new i(parseFloat(e[1]),parseFloat(e[2]),void 0,parseFloat(e[3])):new i(parseFloat(e[1]),parseFloat(e[2]))},o.prototype.matchCoordinates=function(t){var e=[];do{var r=this.isMatch(["("]);e.push(this.matchCoordinate(t)),r&&this.expectGroupEnd()}while(this.isMatch([","]));return e},o.prototype.skipWhitespaces=function(){for(;this.position<this.value.length&&" "===this.value[this.position];)this.position++}},{"./point":9,"./types":11}],13:[function(t,e,r){e.exports={encode:function(t){return t<<1^t>>31},decode:function(t){return t>>1^-(1&t)}}},{}],14:[function(t,e,r){"use strict";r.byteLength=function(t){var e=f(t),r=e[0],n=e[1];return 3*(r+n)/4-n},r.toByteArray=function(t){var e,r,n=f(t),i=n[0],o=n[1],s=new p(function(t,e){return 3*(t+e)/4-e}(i,o)),a=0,h=0<o?i-4:i;for(r=0;r<h;r+=4)e=u[t.charCodeAt(r)]<<18|u[t.charCodeAt(r+1)]<<12|u[t.charCodeAt(r+2)]<<6|u[t.charCodeAt(r+3)],s[a++]=e>>16&255,s[a++]=e>>8&255,s[a++]=255&e;2===o&&(e=u[t.charCodeAt(r)]<<2|u[t.charCodeAt(r+1)]>>4,s[a++]=255&e);1===o&&(e=u[t.charCodeAt(r)]<<10|u[t.charCodeAt(r+1)]<<4|u[t.charCodeAt(r+2)]>>2,s[a++]=e>>8&255,s[a++]=255&e);return s},r.fromByteArray=function(t){for(var e,r=t.length,n=r%3,i=[],o=0,s=r-n;o<s;o+=16383)i.push(h(t,o,s<o+16383?s:o+16383));1==n?(e=t[r-1],i.push(a[e>>2]+a[e<<4&63]+"==")):2==n&&(e=(t[r-2]<<8)+t[r-1],i.push(a[e>>10]+a[e>>4&63]+a[e<<2&63]+"="));return i.join("")};for(var a=[],u=[],p="undefined"!=typeof Uint8Array?Uint8Array:Array,n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",i=0,o=n.length;i<o;++i)a[i]=n[i],u[n.charCodeAt(i)]=i;function f(t){var e=t.length;if(0<e%4)throw new Error("Invalid string. Length must be a multiple of 4");var r=t.indexOf("=");return-1===r&&(r=e),[r,r===e?0:4-r%4]}function h(t,e,r){for(var n,i,o=[],s=e;s<r;s+=3)n=(t[s]<<16&16711680)+(t[s+1]<<8&65280)+(255&t[s+2]),o.push(a[(i=n)>>18&63]+a[i>>12&63]+a[i>>6&63]+a[63&i]);return o.join("")}u["-".charCodeAt(0)]=62,u["_".charCodeAt(0)]=63},{}],15:[function(t,e,r){r.read=function(t,e,r,n,i){var o,s,a=8*i-n-1,h=(1<<a)-1,u=h>>1,p=-7,f=r?i-1:0,c=r?-1:1,l=t[e+f];for(f+=c,o=l&(1<<-p)-1,l>>=-p,p+=a;0<p;o=256*o+t[e+f],f+=c,p-=8);for(s=o&(1<<-p)-1,o>>=-p,p+=n;0<p;s=256*s+t[e+f],f+=c,p-=8);if(0===o)o=1-u;else{if(o===h)return s?NaN:1/0*(l?-1:1);s+=Math.pow(2,n),o-=u}return(l?-1:1)*s*Math.pow(2,o-n)},r.write=function(t,e,r,n,i,o){var s,a,h,u=8*o-i-1,p=(1<<u)-1,f=p>>1,c=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,l=n?0:o-1,g=n?1:-1,y=e<0||0===e&&1/e<0?1:0;for(e=Math.abs(e),isNaN(e)||e===1/0?(a=isNaN(e)?1:0,s=p):(s=Math.floor(Math.log(e)/Math.LN2),e*(h=Math.pow(2,-s))<1&&(s--,h*=2),2<=(e+=1<=s+f?c/h:c*Math.pow(2,1-f))*h&&(s++,h/=2),p<=s+f?(a=0,s=p):1<=s+f?(a=(e*h-1)*Math.pow(2,i),s+=f):(a=e*Math.pow(2,f-1)*Math.pow(2,i),s=0));8<=i;t[r+l]=255&a,l+=g,a/=256,i-=8);for(s=s<<i|a,u+=i;0<u;t[r+l]=255&s,l+=g,s/=256,u-=8);t[r+l-g]|=128*y}},{}],16:[function(t,e,r){"function"==typeof Object.create?e.exports=function(t,e){t.super_=e,t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}})}:e.exports=function(t,e){t.super_=e;function r(){}r.prototype=e.prototype,t.prototype=new r,t.prototype.constructor=t}},{}],17:[function(t,e,r){function n(t){return!!t.constructor&&"function"==typeof t.constructor.isBuffer&&t.constructor.isBuffer(t)}e.exports=function(t){return null!=t&&(n(t)||"function"==typeof(e=t).readFloatLE&&"function"==typeof e.slice&&n(e.slice(0,0))||!!t._isBuffer);var e}},{}],18:[function(t,e,r){var n,i,o=e.exports={};function s(){throw new Error("setTimeout has not been defined")}function a(){throw new Error("clearTimeout has not been defined")}function h(e){if(n===setTimeout)return setTimeout(e,0);if((n===s||!n)&&setTimeout)return n=setTimeout,setTimeout(e,0);try{return n(e,0)}catch(t){try{return n.call(null,e,0)}catch(t){return n.call(this,e,0)}}}!function(){try{n="function"==typeof setTimeout?setTimeout:s}catch(t){n=s}try{i="function"==typeof clearTimeout?clearTimeout:a}catch(t){i=a}}();var u,p=[],f=!1,c=-1;function l(){f&&u&&(f=!1,u.length?p=u.concat(p):c=-1,p.length&&g())}function g(){if(!f){var t=h(l);f=!0;for(var e=p.length;e;){for(u=p,p=[];++c<e;)u&&u[c].run();c=-1,e=p.length}u=null,f=!1,function(e){if(i===clearTimeout)return clearTimeout(e);if((i===a||!i)&&clearTimeout)return i=clearTimeout,clearTimeout(e);try{i(e)}catch(t){try{return i.call(null,e)}catch(t){return i.call(this,e)}}}(t)}}function y(t,e){this.fun=t,this.array=e}function w(){}o.nextTick=function(t){var e=new Array(arguments.length-1);if(1<arguments.length)for(var r=1;r<arguments.length;r++)e[r-1]=arguments[r];p.push(new y(t,e)),1!==p.length||f||h(g)},y.prototype.run=function(){this.fun.apply(null,this.array)},o.title="browser",o.browser=!0,o.env={},o.argv=[],o.version="",o.versions={},o.on=w,o.addListener=w,o.once=w,o.off=w,o.removeListener=w,o.removeAllListeners=w,o.emit=w,o.prependListener=w,o.prependOnceListener=w,o.listeners=function(t){return[]},o.binding=function(t){throw new Error("process.binding is not supported")},o.cwd=function(){return"/"},o.chdir=function(t){throw new Error("process.chdir is not supported")},o.umask=function(){return 0}},{}],19:[function(t,e,r){e.exports=function(t){return t&&"object"==typeof t&&"function"==typeof t.copy&&"function"==typeof t.fill&&"function"==typeof t.readUInt8}},{}],20:[function(c,t,x){(function(n,i){var a=/%[sdj%]/g;x.format=function(t){if(!m(t)){for(var e=[],r=0;r<arguments.length;r++)e.push(h(arguments[r]));return e.join(" ")}r=1;for(var n=arguments,i=n.length,o=String(t).replace(a,function(t){if("%%"===t)return"%";if(i<=r)return t;switch(t){case"%s":return String(n[r++]);case"%d":return Number(n[r++]);case"%j":try{return JSON.stringify(n[r++])}catch(t){return"[Circular]"}default:return t}}),s=n[r];r<i;s=n[++r])b(s)||!p(s)?o+=" "+s:o+=" "+h(s);return o},x.deprecate=function(t,e){if(k(i.process))return function(){return x.deprecate(t,e).apply(this,arguments)};if(!0===n.noDeprecation)return t;var r=!1;return function(){if(!r){if(n.throwDeprecation)throw new Error(e);n.traceDeprecation?console.trace(e):console.error(e),r=!0}return t.apply(this,arguments)}};var t,o={};function h(t,e){var r={seen:[],stylize:u};return 3<=arguments.length&&(r.depth=arguments[2]),4<=arguments.length&&(r.colors=arguments[3]),d(e)?r.showHidden=e:e&&x._extend(r,e),k(r.showHidden)&&(r.showHidden=!1),k(r.depth)&&(r.depth=2),k(r.colors)&&(r.colors=!1),k(r.customInspect)&&(r.customInspect=!0),r.colors&&(r.stylize=s),l(r,t,r.depth)}function s(t,e){var r=h.styles[e];return r?"["+h.colors[r][0]+"m"+t+"["+h.colors[r][1]+"m":t}function u(t,e){return t}function l(e,r,n){if(e.customInspect&&r&&I(r.inspect)&&r.inspect!==x.inspect&&(!r.constructor||r.constructor.prototype!==r)){var t=r.inspect(n,e);return m(t)||(t=l(e,t,n)),t}var i=function(t,e){if(k(e))return t.stylize("undefined","undefined");if(m(e)){var r="'"+JSON.stringify(e).replace(/^"|"$/g,"").replace(/'/g,"\\'").replace(/\\"/g,'"')+"'";return t.stylize(r,"string")}if(v(e))return t.stylize(""+e,"number");if(d(e))return t.stylize(""+e,"boolean");if(b(e))return t.stylize("null","null")}(e,r);if(i)return i;var o,s=Object.keys(r),a=(o={},s.forEach(function(t,e){o[t]=!0}),o);if(e.showHidden&&(s=Object.getOwnPropertyNames(r)),S(r)&&(0<=s.indexOf("message")||0<=s.indexOf("description")))return g(r);if(0===s.length){if(I(r)){var h=r.name?": "+r.name:"";return e.stylize("[Function"+h+"]","special")}if(M(r))return e.stylize(RegExp.prototype.toString.call(r),"regexp");if(E(r))return e.stylize(Date.prototype.toString.call(r),"date");if(S(r))return g(r)}var u,p="",f=!1,c=["{","}"];w(r)&&(f=!0,c=["[","]"]),I(r)&&(p=" [Function"+(r.name?": "+r.name:"")+"]");return M(r)&&(p=" "+RegExp.prototype.toString.call(r)),E(r)&&(p=" "+Date.prototype.toUTCString.call(r)),S(r)&&(p=" "+g(r)),0!==s.length||f&&0!=r.length?n<0?M(r)?e.stylize(RegExp.prototype.toString.call(r),"regexp"):e.stylize("[Object]","special"):(e.seen.push(r),u=f?function(e,r,n,i,t){for(var o=[],s=0,a=r.length;s<a;++s)_(r,String(s))?o.push(y(e,r,n,i,String(s),!0)):o.push("");return t.forEach(function(t){t.match(/^\d+$/)||o.push(y(e,r,n,i,t,!0))}),o}(e,r,n,a,s):s.map(function(t){return y(e,r,n,a,t,f)}),e.seen.pop(),function(t,e,r){if(60<t.reduce(function(t,e){return 0<=e.indexOf("\n")&&0,t+e.replace(/\u001b\[\d\d?m/g,"").length+1},0))return r[0]+(""===e?"":e+"\n ")+" "+t.join(",\n  ")+" "+r[1];return r[0]+e+" "+t.join(", ")+" "+r[1]}(u,p,c)):c[0]+p+c[1]}function g(t){return"["+Error.prototype.toString.call(t)+"]"}function y(t,e,r,n,i,o){var s,a,h;if((h=Object.getOwnPropertyDescriptor(e,i)||{value:e[i]}).get?a=h.set?t.stylize("[Getter/Setter]","special"):t.stylize("[Getter]","special"):h.set&&(a=t.stylize("[Setter]","special")),_(n,i)||(s="["+i+"]"),a||(t.seen.indexOf(h.value)<0?-1<(a=b(r)?l(t,h.value,null):l(t,h.value,r-1)).indexOf("\n")&&(a=o?a.split("\n").map(function(t){return"  "+t}).join("\n").substr(2):"\n"+a.split("\n").map(function(t){return"   "+t}).join("\n")):a=t.stylize("[Circular]","special")),k(s)){if(o&&i.match(/^\d+$/))return a;s=(s=JSON.stringify(""+i)).match(/^"([a-zA-Z_][a-zA-Z_0-9]*)"$/)?(s=s.substr(1,s.length-2),t.stylize(s,"name")):(s=s.replace(/'/g,"\\'").replace(/\\"/g,'"').replace(/(^"|"$)/g,"'"),t.stylize(s,"string"))}return s+": "+a}function w(t){return Array.isArray(t)}function d(t){return"boolean"==typeof t}function b(t){return null===t}function v(t){return"number"==typeof t}function m(t){return"string"==typeof t}function k(t){return void 0===t}function M(t){return p(t)&&"[object RegExp]"===e(t)}function p(t){return"object"==typeof t&&null!==t}function E(t){return p(t)&&"[object Date]"===e(t)}function S(t){return p(t)&&("[object Error]"===e(t)||t instanceof Error)}function I(t){return"function"==typeof t}function e(t){return Object.prototype.toString.call(t)}function r(t){return t<10?"0"+t.toString(10):t.toString(10)}x.debuglog=function(e){if(k(t)&&(t=n.env.NODE_DEBUG||""),e=e.toUpperCase(),!o[e])if(new RegExp("\\b"+e+"\\b","i").test(t)){var r=n.pid;o[e]=function(){var t=x.format.apply(x,arguments);console.error("%s %d: %s",e,r,t)}}else o[e]=function(){};return o[e]},(x.inspect=h).colors={bold:[1,22],italic:[3,23],underline:[4,24],inverse:[7,27],white:[37,39],grey:[90,39],black:[30,39],blue:[34,39],cyan:[36,39],green:[32,39],magenta:[35,39],red:[31,39],yellow:[33,39]},h.styles={special:"cyan",number:"yellow",boolean:"yellow",undefined:"grey",null:"bold",string:"green",date:"magenta",regexp:"red"},x.isArray=w,x.isBoolean=d,x.isNull=b,x.isNullOrUndefined=function(t){return null==t},x.isNumber=v,x.isString=m,x.isSymbol=function(t){return"symbol"==typeof t},x.isUndefined=k,x.isRegExp=M,x.isObject=p,x.isDate=E,x.isError=S,x.isFunction=I,x.isPrimitive=function(t){return null===t||"boolean"==typeof t||"number"==typeof t||"string"==typeof t||"symbol"==typeof t||void 0===t},x.isBuffer=c("./support/isBuffer");var f=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function _(t,e){return Object.prototype.hasOwnProperty.call(t,e)}x.log=function(){var t,e;console.log("%s - %s",(t=new Date,e=[r(t.getHours()),r(t.getMinutes()),r(t.getSeconds())].join(":"),[t.getDate(),f[t.getMonth()],e].join(" ")),x.format.apply(x,arguments))},x.inherits=c("inherits"),x._extend=function(t,e){if(!e||!p(e))return t;for(var r=Object.keys(e),n=r.length;n--;)t[r[n]]=e[r[n]];return t}}).call(this,c("_process"),"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./support/isBuffer":19,_process:18,inherits:16}],buffer:[function(N,t,z){(function(f){"use strict";var n=N("base64-js"),o=N("ieee754"),t="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;z.Buffer=f,z.SlowBuffer=function(t){+t!=t&&(t=0);return f.alloc(+t)},z.INSPECT_MAX_BYTES=50;var r=2147483647;function s(t){if(r<t)throw new RangeError('The value "'+t+'" is invalid for option "size"');var e=new Uint8Array(t);return Object.setPrototypeOf(e,f.prototype),e}function f(t,e,r){if("number"!=typeof t)return i(t,e,r);if("string"==typeof e)throw new TypeError('The "string" argument must be of type string. Received type number');return h(t)}function i(t,e,r){if("string"==typeof t)return function(t,e){"string"==typeof e&&""!==e||(e="utf8");if(!f.isEncoding(e))throw new TypeError("Unknown encoding: "+e);var r=0|l(t,e),n=s(r),i=n.write(t,e);i!==r&&(n=n.slice(0,i));return n}(t,e);if(ArrayBuffer.isView(t))return u(t);if(null==t)throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t);if(R(t,ArrayBuffer)||t&&R(t.buffer,ArrayBuffer))return p(t,e,r);if("undefined"!=typeof SharedArrayBuffer&&(R(t,SharedArrayBuffer)||t&&R(t.buffer,SharedArrayBuffer)))return p(t,e,r);if("number"==typeof t)throw new TypeError('The "value" argument must not be of type number. Received type number');var n=t.valueOf&&t.valueOf();if(null!=n&&n!==t)return f.from(n,e,r);var i=function(t){if(f.isBuffer(t)){var e=0|c(t.length),r=s(e);return 0===r.length?r:(t.copy(r,0,0,e),r)}if(void 0!==t.length)return"number"!=typeof t.length||U(t.length)?s(0):u(t);if("Buffer"===t.type&&Array.isArray(t.data))return u(t.data)}(t);if(i)return i;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof t[Symbol.toPrimitive])return f.from(t[Symbol.toPrimitive]("string"),e,r);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t)}function a(t){if("number"!=typeof t)throw new TypeError('"size" argument must be of type number');if(t<0)throw new RangeError('The value "'+t+'" is invalid for option "size"')}function h(t){return a(t),s(t<0?0:0|c(t))}function u(t){for(var e=t.length<0?0:0|c(t.length),r=s(e),n=0;n<e;n+=1)r[n]=255&t[n];return r}function p(t,e,r){if(e<0||t.byteLength<e)throw new RangeError('"offset" is outside of buffer bounds');if(t.byteLength<e+(r||0))throw new RangeError('"length" is outside of buffer bounds');var n;return n=void 0===e&&void 0===r?new Uint8Array(t):void 0===r?new Uint8Array(t,e):new Uint8Array(t,e,r),Object.setPrototypeOf(n,f.prototype),n}function c(t){if(r<=t)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+r.toString(16)+" bytes");return 0|t}function l(t,e){if(f.isBuffer(t))return t.length;if(ArrayBuffer.isView(t)||R(t,ArrayBuffer))return t.byteLength;if("string"!=typeof t)throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof t);var r=t.length,n=2<arguments.length&&!0===arguments[2];if(!n&&0===r)return 0;for(var i=!1;;)switch(e){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return W(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return L(t).length;default:if(i)return n?-1:W(t).length;e=(""+e).toLowerCase(),i=!0}}function g(t,e,r){var n=t[e];t[e]=t[r],t[r]=n}function y(t,e,r,n,i){if(0===t.length)return-1;if("string"==typeof r?(n=r,r=0):2147483647<r?r=2147483647:r<-2147483648&&(r=-2147483648),U(r=+r)&&(r=i?0:t.length-1),r<0&&(r=t.length+r),r>=t.length){if(i)return-1;r=t.length-1}else if(r<0){if(!i)return-1;r=0}if("string"==typeof e&&(e=f.from(e,n)),f.isBuffer(e))return 0===e.length?-1:w(t,e,r,n,i);if("number"==typeof e)return e&=255,"function"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(t,e,r):Uint8Array.prototype.lastIndexOf.call(t,e,r):w(t,[e],r,n,i);throw new TypeError("val must be string, number or Buffer")}function w(t,e,r,n,i){var o,s=1,a=t.length,h=e.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(t.length<2||e.length<2)return-1;a/=s=2,h/=2,r/=2}function u(t,e){return 1===s?t[e]:t.readUInt16BE(e*s)}if(i){var p=-1;for(o=r;o<a;o++)if(u(t,o)===u(e,-1===p?0:o-p)){if(-1===p&&(p=o),o-p+1===h)return p*s}else-1!==p&&(o-=o-p),p=-1}else for(a<r+h&&(r=a-h),o=r;0<=o;o--){for(var f=!0,c=0;c<h;c++)if(u(t,o+c)!==u(e,c)){f=!1;break}if(f)return o}return-1}function d(t,e,r,n){r=Number(r)||0;var i=t.length-r;(!n||i<(n=Number(n)))&&(n=i);var o=e.length;o/2<n&&(n=o/2);for(var s=0;s<n;++s){var a=parseInt(e.substr(2*s,2),16);if(U(a))return s;t[r+s]=a}return s}function b(t,e,r,n){return B(function(t){for(var e=[],r=0;r<t.length;++r)e.push(255&t.charCodeAt(r));return e}(e),t,r,n)}function v(t,e,r,n){return B(function(t,e){for(var r,n,i,o=[],s=0;s<t.length&&!((e-=2)<0);++s)r=t.charCodeAt(s),n=r>>8,i=r%256,o.push(i),o.push(n);return o}(e,t.length-r),t,r,n)}function m(t,e,r){return 0===e&&r===t.length?n.fromByteArray(t):n.fromByteArray(t.slice(e,r))}function k(t,e,r){r=Math.min(t.length,r);for(var n=[],i=e;i<r;){var o,s,a,h,u=t[i],p=null,f=239<u?4:223<u?3:191<u?2:1;if(i+f<=r)switch(f){case 1:u<128&&(p=u);break;case 2:128==(192&(o=t[i+1]))&&127<(h=(31&u)<<6|63&o)&&(p=h);break;case 3:o=t[i+1],s=t[i+2],128==(192&o)&&128==(192&s)&&2047<(h=(15&u)<<12|(63&o)<<6|63&s)&&(h<55296||57343<h)&&(p=h);break;case 4:o=t[i+1],s=t[i+2],a=t[i+3],128==(192&o)&&128==(192&s)&&128==(192&a)&&65535<(h=(15&u)<<18|(63&o)<<12|(63&s)<<6|63&a)&&h<1114112&&(p=h)}null===p?(p=65533,f=1):65535<p&&(p-=65536,n.push(p>>>10&1023|55296),p=56320|1023&p),n.push(p),i+=f}return function(t){var e=t.length;if(e<=M)return String.fromCharCode.apply(String,t);var r="",n=0;for(;n<e;)r+=String.fromCharCode.apply(String,t.slice(n,n+=M));return r}(n)}z.kMaxLength=r,(f.TYPED_ARRAY_SUPPORT=function(){try{var t=new Uint8Array(1),e={foo:function(){return 42}};return Object.setPrototypeOf(e,Uint8Array.prototype),Object.setPrototypeOf(t,e),42===t.foo()}catch(t){return!1}}())||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(f.prototype,"parent",{enumerable:!0,get:function(){if(f.isBuffer(this))return this.buffer}}),Object.defineProperty(f.prototype,"offset",{enumerable:!0,get:function(){if(f.isBuffer(this))return this.byteOffset}}),"undefined"!=typeof Symbol&&null!=Symbol.species&&f[Symbol.species]===f&&Object.defineProperty(f,Symbol.species,{value:null,configurable:!0,enumerable:!1,writable:!1}),f.poolSize=8192,f.from=function(t,e,r){return i(t,e,r)},Object.setPrototypeOf(f.prototype,Uint8Array.prototype),Object.setPrototypeOf(f,Uint8Array),f.alloc=function(t,e,r){return i=e,o=r,a(n=t),n<=0||void 0===i?s(n):"string"==typeof o?s(n).fill(i,o):s(n).fill(i);var n,i,o},f.allocUnsafe=function(t){return h(t)},f.allocUnsafeSlow=function(t){return h(t)},f.isBuffer=function(t){return null!=t&&!0===t._isBuffer&&t!==f.prototype},f.compare=function(t,e){if(R(t,Uint8Array)&&(t=f.from(t,t.offset,t.byteLength)),R(e,Uint8Array)&&(e=f.from(e,e.offset,e.byteLength)),!f.isBuffer(t)||!f.isBuffer(e))throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(t===e)return 0;for(var r=t.length,n=e.length,i=0,o=Math.min(r,n);i<o;++i)if(t[i]!==e[i]){r=t[i],n=e[i];break}return r<n?-1:n<r?1:0},f.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},f.concat=function(t,e){if(!Array.isArray(t))throw new TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return f.alloc(0);var r;if(void 0===e)for(r=e=0;r<t.length;++r)e+=t[r].length;var n=f.allocUnsafe(e),i=0;for(r=0;r<t.length;++r){var o=t[r];if(R(o,Uint8Array)&&(o=f.from(o)),!f.isBuffer(o))throw new TypeError('"list" argument must be an Array of Buffers');o.copy(n,i),i+=o.length}return n},f.byteLength=l,f.prototype._isBuffer=!0,f.prototype.swap16=function(){var t=this.length;if(t%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var e=0;e<t;e+=2)g(this,e,e+1);return this},f.prototype.swap32=function(){var t=this.length;if(t%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var e=0;e<t;e+=4)g(this,e,e+3),g(this,e+1,e+2);return this},f.prototype.swap64=function(){var t=this.length;if(t%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var e=0;e<t;e+=8)g(this,e,e+7),g(this,e+1,e+6),g(this,e+2,e+5),g(this,e+3,e+4);return this},f.prototype.toLocaleString=f.prototype.toString=function(){var t=this.length;return 0===t?"":0===arguments.length?k(this,0,t):function(t,e,r){var n=!1;if((void 0===e||e<0)&&(e=0),e>this.length)return"";if((void 0===r||r>this.length)&&(r=this.length),r<=0)return"";if((r>>>=0)<=(e>>>=0))return"";for(t=t||"utf8";;)switch(t){case"hex":return I(this,e,r);case"utf8":case"utf-8":return k(this,e,r);case"ascii":return E(this,e,r);case"latin1":case"binary":return S(this,e,r);case"base64":return m(this,e,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return _(this,e,r);default:if(n)throw new TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),n=!0}}.apply(this,arguments)},f.prototype.equals=function(t){if(!f.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t||0===f.compare(this,t)},f.prototype.inspect=function(){var t="",e=z.INSPECT_MAX_BYTES;return t=this.toString("hex",0,e).replace(/(.{2})/g,"$1 ").trim(),this.length>e&&(t+=" ... "),"<Buffer "+t+">"},t&&(f.prototype[t]=f.prototype.inspect),f.prototype.compare=function(t,e,r,n,i){if(R(t,Uint8Array)&&(t=f.from(t,t.offset,t.byteLength)),!f.isBuffer(t))throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof t);if(void 0===e&&(e=0),void 0===r&&(r=t?t.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),e<0||r>t.length||n<0||i>this.length)throw new RangeError("out of range index");if(i<=n&&r<=e)return 0;if(i<=n)return-1;if(r<=e)return 1;if(this===t)return 0;for(var o=(i>>>=0)-(n>>>=0),s=(r>>>=0)-(e>>>=0),a=Math.min(o,s),h=this.slice(n,i),u=t.slice(e,r),p=0;p<a;++p)if(h[p]!==u[p]){o=h[p],s=u[p];break}return o<s?-1:s<o?1:0},f.prototype.includes=function(t,e,r){return-1!==this.indexOf(t,e,r)},f.prototype.indexOf=function(t,e,r){return y(this,t,e,r,!0)},f.prototype.lastIndexOf=function(t,e,r){return y(this,t,e,r,!1)},f.prototype.write=function(t,e,r,n){if(void 0===e)n="utf8",r=this.length,e=0;else if(void 0===r&&"string"==typeof e)n=e,r=this.length,e=0;else{if(!isFinite(e))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");e>>>=0,isFinite(r)?(r>>>=0,void 0===n&&(n="utf8")):(n=r,r=void 0)}var i=this.length-e;if((void 0===r||i<r)&&(r=i),0<t.length&&(r<0||e<0)||e>this.length)throw new RangeError("Attempt to write outside buffer bounds");n=n||"utf8";for(var o,s,a,h,u,p,f=!1;;)switch(n){case"hex":return d(this,t,e,r);case"utf8":case"utf-8":return u=e,p=r,B(W(t,(h=this).length-u),h,u,p);case"ascii":return b(this,t,e,r);case"latin1":case"binary":return b(this,t,e,r);case"base64":return o=this,s=e,a=r,B(L(t),o,s,a);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return v(this,t,e,r);default:if(f)throw new TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),f=!0}},f.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var M=4096;function E(t,e,r){var n="";r=Math.min(t.length,r);for(var i=e;i<r;++i)n+=String.fromCharCode(127&t[i]);return n}function S(t,e,r){var n="";r=Math.min(t.length,r);for(var i=e;i<r;++i)n+=String.fromCharCode(t[i]);return n}function I(t,e,r){var n=t.length;(!e||e<0)&&(e=0),(!r||r<0||n<r)&&(r=n);for(var i="",o=e;o<r;++o)i+=G[t[o]];return i}function _(t,e,r){for(var n=t.slice(e,r),i="",o=0;o<n.length;o+=2)i+=String.fromCharCode(n[o]+256*n[o+1]);return i}function x(t,e,r){if(t%1!=0||t<0)throw new RangeError("offset is not uint");if(r<t+e)throw new RangeError("Trying to access beyond buffer length")}function T(t,e,r,n,i,o){if(!f.isBuffer(t))throw new TypeError('"buffer" argument must be a Buffer instance');if(i<e||e<o)throw new RangeError('"value" argument is out of bounds');if(r+n>t.length)throw new RangeError("Index out of range")}function P(t,e,r,n){if(r+n>t.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("Index out of range")}function Z(t,e,r,n,i){return e=+e,r>>>=0,i||P(t,0,r,4),o.write(t,e,r,n,23,4),r+4}function O(t,e,r,n,i){return e=+e,r>>>=0,i||P(t,0,r,8),o.write(t,e,r,n,52,8),r+8}f.prototype.slice=function(t,e){var r=this.length;(t=~~t)<0?(t+=r)<0&&(t=0):r<t&&(t=r),(e=void 0===e?r:~~e)<0?(e+=r)<0&&(e=0):r<e&&(e=r),e<t&&(e=t);var n=this.subarray(t,e);return Object.setPrototypeOf(n,f.prototype),n},f.prototype.readUIntLE=function(t,e,r){t>>>=0,e>>>=0,r||x(t,e,this.length);for(var n=this[t],i=1,o=0;++o<e&&(i*=256);)n+=this[t+o]*i;return n},f.prototype.readUIntBE=function(t,e,r){t>>>=0,e>>>=0,r||x(t,e,this.length);for(var n=this[t+--e],i=1;0<e&&(i*=256);)n+=this[t+--e]*i;return n},f.prototype.readUInt8=function(t,e){return t>>>=0,e||x(t,1,this.length),this[t]},f.prototype.readUInt16LE=function(t,e){return t>>>=0,e||x(t,2,this.length),this[t]|this[t+1]<<8},f.prototype.readUInt16BE=function(t,e){return t>>>=0,e||x(t,2,this.length),this[t]<<8|this[t+1]},f.prototype.readUInt32LE=function(t,e){return t>>>=0,e||x(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+16777216*this[t+3]},f.prototype.readUInt32BE=function(t,e){return t>>>=0,e||x(t,4,this.length),16777216*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},f.prototype.readIntLE=function(t,e,r){t>>>=0,e>>>=0,r||x(t,e,this.length);for(var n=this[t],i=1,o=0;++o<e&&(i*=256);)n+=this[t+o]*i;return(i*=128)<=n&&(n-=Math.pow(2,8*e)),n},f.prototype.readIntBE=function(t,e,r){t>>>=0,e>>>=0,r||x(t,e,this.length);for(var n=e,i=1,o=this[t+--n];0<n&&(i*=256);)o+=this[t+--n]*i;return(i*=128)<=o&&(o-=Math.pow(2,8*e)),o},f.prototype.readInt8=function(t,e){return t>>>=0,e||x(t,1,this.length),128&this[t]?-1*(255-this[t]+1):this[t]},f.prototype.readInt16LE=function(t,e){t>>>=0,e||x(t,2,this.length);var r=this[t]|this[t+1]<<8;return 32768&r?4294901760|r:r},f.prototype.readInt16BE=function(t,e){t>>>=0,e||x(t,2,this.length);var r=this[t+1]|this[t]<<8;return 32768&r?4294901760|r:r},f.prototype.readInt32LE=function(t,e){return t>>>=0,e||x(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},f.prototype.readInt32BE=function(t,e){return t>>>=0,e||x(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},f.prototype.readFloatLE=function(t,e){return t>>>=0,e||x(t,4,this.length),o.read(this,t,!0,23,4)},f.prototype.readFloatBE=function(t,e){return t>>>=0,e||x(t,4,this.length),o.read(this,t,!1,23,4)},f.prototype.readDoubleLE=function(t,e){return t>>>=0,e||x(t,8,this.length),o.read(this,t,!0,52,8)},f.prototype.readDoubleBE=function(t,e){return t>>>=0,e||x(t,8,this.length),o.read(this,t,!1,52,8)},f.prototype.writeUIntLE=function(t,e,r,n){t=+t,e>>>=0,r>>>=0,n||T(this,t,e,r,Math.pow(2,8*r)-1,0);var i=1,o=0;for(this[e]=255&t;++o<r&&(i*=256);)this[e+o]=t/i&255;return e+r},f.prototype.writeUIntBE=function(t,e,r,n){t=+t,e>>>=0,r>>>=0,n||T(this,t,e,r,Math.pow(2,8*r)-1,0);var i=r-1,o=1;for(this[e+i]=255&t;0<=--i&&(o*=256);)this[e+i]=t/o&255;return e+r},f.prototype.writeUInt8=function(t,e,r){return t=+t,e>>>=0,r||T(this,t,e,1,255,0),this[e]=255&t,e+1},f.prototype.writeUInt16LE=function(t,e,r){return t=+t,e>>>=0,r||T(this,t,e,2,65535,0),this[e]=255&t,this[e+1]=t>>>8,e+2},f.prototype.writeUInt16BE=function(t,e,r){return t=+t,e>>>=0,r||T(this,t,e,2,65535,0),this[e]=t>>>8,this[e+1]=255&t,e+2},f.prototype.writeUInt32LE=function(t,e,r){return t=+t,e>>>=0,r||T(this,t,e,4,4294967295,0),this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t,e+4},f.prototype.writeUInt32BE=function(t,e,r){return t=+t,e>>>=0,r||T(this,t,e,4,4294967295,0),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},f.prototype.writeIntLE=function(t,e,r,n){if(t=+t,e>>>=0,!n){var i=Math.pow(2,8*r-1);T(this,t,e,r,i-1,-i)}var o=0,s=1,a=0;for(this[e]=255&t;++o<r&&(s*=256);)t<0&&0===a&&0!==this[e+o-1]&&(a=1),this[e+o]=(t/s>>0)-a&255;return e+r},f.prototype.writeIntBE=function(t,e,r,n){if(t=+t,e>>>=0,!n){var i=Math.pow(2,8*r-1);T(this,t,e,r,i-1,-i)}var o=r-1,s=1,a=0;for(this[e+o]=255&t;0<=--o&&(s*=256);)t<0&&0===a&&0!==this[e+o+1]&&(a=1),this[e+o]=(t/s>>0)-a&255;return e+r},f.prototype.writeInt8=function(t,e,r){return t=+t,e>>>=0,r||T(this,t,e,1,127,-128),t<0&&(t=255+t+1),this[e]=255&t,e+1},f.prototype.writeInt16LE=function(t,e,r){return t=+t,e>>>=0,r||T(this,t,e,2,32767,-32768),this[e]=255&t,this[e+1]=t>>>8,e+2},f.prototype.writeInt16BE=function(t,e,r){return t=+t,e>>>=0,r||T(this,t,e,2,32767,-32768),this[e]=t>>>8,this[e+1]=255&t,e+2},f.prototype.writeInt32LE=function(t,e,r){return t=+t,e>>>=0,r||T(this,t,e,4,2147483647,-2147483648),this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24,e+4},f.prototype.writeInt32BE=function(t,e,r){return t=+t,e>>>=0,r||T(this,t,e,4,2147483647,-2147483648),t<0&&(t=4294967295+t+1),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},f.prototype.writeFloatLE=function(t,e,r){return Z(this,t,e,!0,r)},f.prototype.writeFloatBE=function(t,e,r){return Z(this,t,e,!1,r)},f.prototype.writeDoubleLE=function(t,e,r){return O(this,t,e,!0,r)},f.prototype.writeDoubleBE=function(t,e,r){return O(this,t,e,!1,r)},f.prototype.copy=function(t,e,r,n){if(!f.isBuffer(t))throw new TypeError("argument should be a Buffer");if(r=r||0,n||0===n||(n=this.length),e>=t.length&&(e=t.length),e=e||0,0<n&&n<r&&(n=r),n===r)return 0;if(0===t.length||0===this.length)return 0;if(e<0)throw new RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),t.length-e<n-r&&(n=t.length-e+r);var i=n-r;if(this===t&&"function"==typeof Uint8Array.prototype.copyWithin)this.copyWithin(e,r,n);else if(this===t&&r<e&&e<n)for(var o=i-1;0<=o;--o)t[o+e]=this[o+r];else Uint8Array.prototype.set.call(t,this.subarray(r,n),e);return i},f.prototype.fill=function(t,e,r,n){if("string"==typeof t){if("string"==typeof e?(n=e,e=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),void 0!==n&&"string"!=typeof n)throw new TypeError("encoding must be a string");if("string"==typeof n&&!f.isEncoding(n))throw new TypeError("Unknown encoding: "+n);if(1===t.length){var i=t.charCodeAt(0);("utf8"===n&&i<128||"latin1"===n)&&(t=i)}}else"number"==typeof t?t&=255:"boolean"==typeof t&&(t=Number(t));if(e<0||this.length<e||this.length<r)throw new RangeError("Out of range index");if(r<=e)return this;var o;if(e>>>=0,r=void 0===r?this.length:r>>>0,"number"==typeof(t=t||0))for(o=e;o<r;++o)this[o]=t;else{var s=f.isBuffer(t)?t:f.from(t,n),a=s.length;if(0===a)throw new TypeError('The value "'+t+'" is invalid for argument "value"');for(o=0;o<r-e;++o)this[o+e]=s[o%a]}return this};var e=/[^+/0-9A-Za-z-_]/g;function W(t,e){var r;e=e||1/0;for(var n=t.length,i=null,o=[],s=0;s<n;++s){if(55295<(r=t.charCodeAt(s))&&r<57344){if(!i){if(56319<r){-1<(e-=3)&&o.push(239,191,189);continue}if(s+1===n){-1<(e-=3)&&o.push(239,191,189);continue}i=r;continue}if(r<56320){-1<(e-=3)&&o.push(239,191,189),i=r;continue}r=65536+(i-55296<<10|r-56320)}else i&&-1<(e-=3)&&o.push(239,191,189);if(i=null,r<128){if(--e<0)break;o.push(r)}else if(r<2048){if((e-=2)<0)break;o.push(r>>6|192,63&r|128)}else if(r<65536){if((e-=3)<0)break;o.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(r<1114112))throw new Error("Invalid code point");if((e-=4)<0)break;o.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return o}function L(t){return n.toByteArray(function(t){if((t=(t=t.split("=")[0]).trim().replace(e,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function B(t,e,r,n){for(var i=0;i<n&&!(i+r>=e.length||i>=t.length);++i)e[i+r]=t[i];return i}function R(t,e){return t instanceof e||null!=t&&null!=t.constructor&&null!=t.constructor.name&&t.constructor.name===e.name}function U(t){return t!=t}var G=function(){for(var t="0123456789abcdef",e=new Array(256),r=0;r<16;++r)for(var n=16*r,i=0;i<16;++i)e[n+i]=t[r]+t[i];return e}()}).call(this,N("buffer").Buffer)},{"base64-js":14,buffer:"buffer",ieee754:15}],wkx:[function(t,e,r){r.Types=t("./types"),r.Geometry=t("./geometry"),r.Point=t("./point"),r.LineString=t("./linestring"),r.Polygon=t("./polygon"),r.MultiPoint=t("./multipoint"),r.MultiLineString=t("./multilinestring"),r.MultiPolygon=t("./multipolygon"),r.GeometryCollection=t("./geometrycollection")},{"./geometry":3,"./geometrycollection":4,"./linestring":5,"./multilinestring":6,"./multipoint":7,"./multipolygon":8,"./point":9,"./polygon":10,"./types":11}]},{},["wkx"]);
