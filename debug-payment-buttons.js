// Debug payment buttons functionality
console.log('🔍 Debugging Payment Buttons...\n');

async function debugPaymentButtons() {
    try {
        // Test 1: Check if app instance exists
        console.log('1. Checking app instance...');
        console.log(`   📋 window.app exists: ${window.app ? '✅ OK' : '❌ MISSING'}`);
        
        if (window.app) {
            console.log(`   📋 app.processPayment: ${typeof window.app.processPayment === 'function' ? '✅ OK' : '❌ MISSING'}`);
        }

        // Test 2: Check current section and vehicles
        console.log('\n2. Checking current section...');
        const activeNavItem = document.querySelector('.nav-item.active-nav-item');
        const currentSection = activeNavItem ? activeNavItem.dataset.target : 'unknown';
        console.log(`   📋 Current section: ${currentSection}`);

        // Test 3: Look for payment buttons
        console.log('\n3. Searching for payment buttons...');
        
        // Check for buttons with onclick containing processPayment
        const onclickButtons = document.querySelectorAll('button[onclick*="processPayment"]');
        console.log(`   📋 Buttons with onclick processPayment: ${onclickButtons.length}`);
        
        // Check for buttons with credit card icon
        const creditCardButtons = document.querySelectorAll('button i.fa-credit-card');
        console.log(`   📋 Buttons with credit card icon: ${creditCardButtons.length}`);
        
        // Check all buttons in vehicle tables
        const allButtons = document.querySelectorAll('table button');
        console.log(`   📋 Total buttons in tables: ${allButtons.length}`);

        // Test 4: Check specific tables
        console.log('\n4. Checking specific tables...');
        const tables = [
            'vehicles-table',
            'pending-vehicles-table', 
            'released-vehicles-table',
            'overdue-vehicles-table',
            'recent-vehicles-table'
        ];
        
        tables.forEach(tableId => {
            const table = document.getElementById(tableId);
            if (table) {
                const buttons = table.querySelectorAll('button');
                const paymentButtons = table.querySelectorAll('button[onclick*="processPayment"]');
                console.log(`   📋 ${tableId}: ${buttons.length} buttons, ${paymentButtons.length} payment buttons`);
            } else {
                console.log(`   📋 ${tableId}: ❌ NOT FOUND`);
            }
        });

        // Test 5: Check if vehicles are loaded
        console.log('\n5. Checking if vehicles are loaded...');
        const vehicleRows = document.querySelectorAll('table tbody tr');
        console.log(`   📋 Vehicle rows found: ${vehicleRows.length}`);
        
        if (vehicleRows.length > 0) {
            console.log('   📋 Sample vehicle row content:');
            const firstRow = vehicleRows[0];
            const buttons = firstRow.querySelectorAll('button');
            console.log(`      - Buttons in first row: ${buttons.length}`);
            buttons.forEach((button, index) => {
                const onclick = button.getAttribute('onclick');
                const title = button.getAttribute('title');
                const icon = button.querySelector('i') ? button.querySelector('i').className : 'no icon';
                console.log(`      - Button ${index + 1}: onclick="${onclick}", title="${title}", icon="${icon}"`);
            });
        }

        // Test 6: Try to manually trigger processPayment
        console.log('\n6. Testing manual processPayment call...');
        if (window.app && typeof window.app.processPayment === 'function') {
            console.log('   📋 Attempting to call processPayment with test ID...');
            try {
                // Don't actually call it, just check if it would work
                console.log('   📋 processPayment method is callable');
                console.log('   📋 To test manually, try: app.processPayment(1)');
            } catch (error) {
                console.log(`   ❌ Error testing processPayment: ${error.message}`);
            }
        }

        // Test 7: Check modal
        console.log('\n7. Checking payment modal...');
        const paymentModal = document.getElementById('paymentModal');
        console.log(`   📋 Payment modal exists: ${paymentModal ? '✅ OK' : '❌ MISSING'}`);
        
        if (paymentModal) {
            const isHidden = paymentModal.classList.contains('hidden');
            console.log(`   📋 Modal is hidden: ${isHidden ? '✅ OK' : '❌ VISIBLE'}`);
        }

        // Test 8: Create a test button to verify functionality
        console.log('\n8. Creating test payment button...');
        const testButton = document.createElement('button');
        testButton.innerHTML = '🧪 Test Payment Button';
        testButton.className = 'bg-blue-500 text-white px-4 py-2 rounded m-2';
        testButton.onclick = () => {
            console.log('🧪 Test button clicked!');
            if (window.app && window.app.processPayment) {
                console.log('🧪 Calling app.processPayment(1)...');
                window.app.processPayment(1);
            } else {
                console.log('❌ app.processPayment not available');
            }
        };
        
        // Add test button to page
        const header = document.querySelector('h1, h2, .text-xl');
        if (header) {
            header.parentNode.insertBefore(testButton, header.nextSibling);
            console.log('   ✅ Test button added to page');
        }

        console.log('\n🎉 Payment buttons debug completed!');
        console.log('\n📋 Next steps:');
        console.log('1. Check if vehicles are loaded in the current section');
        console.log('2. Look for the 💳 or credit card icon buttons');
        console.log('3. Try clicking the test button created above');
        console.log('4. Check browser console for any JavaScript errors');

    } catch (error) {
        console.error('❌ Debug failed:', error);
    }
}

// Run debug when ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(debugPaymentButtons, 4000);
    });
} else {
    setTimeout(debugPaymentButtons, 4000);
}
