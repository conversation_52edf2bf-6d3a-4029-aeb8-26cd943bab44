const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');
require('dotenv').config();

async function runMigration() {
    let connection;

    try {
        console.log('🔄 Starting database migration...');

        // Create connection without specifying database first
        const connectionConfig = {
            host: process.env.DB_HOST,
            port: process.env.DB_PORT,
            user: process.env.DB_USER,
            password: process.env.DB_PASSWORD,
            multipleStatements: true
        };

        connection = await mysql.createConnection(connectionConfig);
        console.log('✅ Connected to MySQL server');

        // Read and execute schema file
        const schemaPath = path.join(__dirname, 'schema.sql');
        const schemaSql = fs.readFileSync(schemaPath, 'utf8');

        console.log('📄 Executing schema file...');
        await connection.query(schemaSql);
        console.log('✅ Database schema created successfully');

        // Execute additional migration files
        await executeMigrationFiles(connection);

        // Verify tables were created
        await connection.query(`USE ${process.env.DB_NAME}`);
        const [tables] = await connection.query('SHOW TABLES');

        console.log('📊 Created tables:');
        tables.forEach(table => {
            const tableName = Object.values(table)[0];
            console.log(`  - ${tableName}`);
        });

        // Insert sample data if needed
        await insertSampleData(connection);

        console.log('🎉 Migration completed successfully!');

    } catch (error) {
        console.error('❌ Migration failed:', error.message);
        process.exit(1);
    } finally {
        if (connection) {
            await connection.end();
        }
    }
}

async function executeMigrationFiles(connection) {
    try {
        console.log('📄 Executing additional migration files...');

        // Get migrations directory
        const migrationsDir = path.join(__dirname, '..', 'migrations');

        if (!fs.existsSync(migrationsDir)) {
            console.log('ℹ️  No migrations directory found, skipping...');
            return;
        }

        // Read all migration files
        const migrationFiles = fs.readdirSync(migrationsDir)
            .filter(file => file.endsWith('.sql'))
            .sort(); // Execute in alphabetical order

        if (migrationFiles.length === 0) {
            console.log('ℹ️  No migration files found, skipping...');
            return;
        }

        // Execute each migration file
        for (const file of migrationFiles) {
            console.log(`📄 Executing migration: ${file}`);
            const migrationPath = path.join(migrationsDir, file);
            const migrationSql = fs.readFileSync(migrationPath, 'utf8');

            try {
                await connection.query(migrationSql);
                console.log(`✅ Migration ${file} executed successfully`);
            } catch (error) {
                console.error(`❌ Migration ${file} failed:`, error.message);
                // Continue with other migrations
            }
        }

        console.log('✅ All migrations executed');

    } catch (error) {
        console.error('❌ Failed to execute migrations:', error.message);
        throw error;
    }
}

async function insertSampleData(connection) {
    try {
        console.log('📝 Inserting sample data...');

        // Check if we already have data
        const [vehicleCount] = await connection.query('SELECT COUNT(*) as count FROM vehicles');
        if (vehicleCount[0].count > 0) {
            console.log('ℹ️  Sample data already exists, skipping...');
            return;
        }

        // Insert sample vehicle owners
        const sampleOwners = [
            ['Ahmed', 'Benali', '+212 661 234 567', '<EMAIL>', 'Rue Mohammed V, Casablanca', 'BE123456'],
            ['Fatima', 'Alaoui', '+212 662 345 678', '<EMAIL>', 'Avenue Hassan II, Rabat', 'FA789012'],
            ['Omar', 'Tazi', '+212 663 456 789', '<EMAIL>', 'Boulevard Zerktouni, Marrakech', 'OT345678']
        ];

        for (const owner of sampleOwners) {
            await connection.query(
                'INSERT INTO vehicle_owners (first_name, last_name, phone, email, address, id_number) VALUES (?, ?, ?, ?, ?, ?)',
                owner
            );
        }

        // Insert sample vehicles
        const sampleVehicles = [
            ['DEP001', 'ABC-1234', 1, 'Toyota', 'Corolla', 'Blanc', 2020, 'VIN123456789', 'Police Municipale', '2023-06-15', '14:30:00', 'Avenue Mohammed V', 'Stationnement interdit', 'Véhicule en bon état', 'impounded', 1],
            ['DEP002', 'XYZ-5678', 2, 'Honda', 'CBR600', 'Rouge', 2019, 'VIN987654321', 'Gendarmerie Royale', '2023-06-14', '09:15:00', 'Place Jamaa El Fna', 'Conduite sans permis', 'Quelques égratignures', 'pending_release', 2],
            ['DEP003', 'DEF-9012', 1, 'Renault', 'Clio', 'Bleu', 2018, 'VIN456789123', 'Police de la Route', '2022-05-10', '16:45:00', 'Autoroute A1', 'Excès de vitesse', 'Véhicule accidenté', 'overdue', 3]
        ];

        for (const vehicle of sampleVehicles) {
            await connection.query(
                'INSERT INTO vehicles (depot_number, license_plate, vehicle_type_id, brand, model, color, year, vin, depositor, entry_date, entry_time, location_found, reason_impounded, observations, status, owner_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
                vehicle
            );
        }

        // Insert sample payments
        const samplePayments = [
            [2, 750.00, '2023-06-16', 'REC001', 'cash', 'Paiement complet pour sortie'],
            [1, 375.00, '2023-06-20', 'REC002', 'bank_transfer', 'Paiement partiel']
        ];

        for (const payment of samplePayments) {
            await connection.query(
                'INSERT INTO payments (vehicle_id, amount, payment_date, receipt_number, payment_method, notes) VALUES (?, ?, ?, ?, ?, ?)',
                payment
            );
        }

        console.log('✅ Sample data inserted successfully');

    } catch (error) {
        console.error('❌ Failed to insert sample data:', error.message);
        throw error;
    }
}

// Run migration if this file is executed directly
if (require.main === module) {
    runMigration();
}

module.exports = { runMigration };
