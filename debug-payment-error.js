// Debug payment processing errors
console.log('🔍 Debugging Payment Processing Errors...\n');

function debugPaymentError() {
    console.log('=== PAYMENT ERROR DIAGNOSTIC ===\n');
    
    // Test 1: Check form data extraction
    console.log('1. Testing form data extraction...');
    const paymentForm = document.getElementById('paymentForm');
    if (!paymentForm) {
        console.error('❌ Payment form not found!');
        return;
    }
    
    console.log('✅ Payment form found');
    
    // Test 2: Simulate form data collection
    console.log('\n2. Simulating form data collection...');
    const formData = new FormData(paymentForm);
    const paymentData = Object.fromEntries(formData.entries());
    
    console.log('Form data collected:', paymentData);
    
    // Check required fields
    const requiredFields = ['vehicle_id', 'amount', 'payment_date', 'receipt_number', 'release_order_number'];
    const missingFields = requiredFields.filter(field => !paymentData[field]);
    
    if (missingFields.length > 0) {
        console.error('❌ Missing required fields:', missingFields);
    } else {
        console.log('✅ All required fields present');
    }
    
    // Test 3: Check API endpoint
    console.log('\n3. Testing payment API endpoint...');
    
    // Create test payload
    const testPayload = {
        vehicle_id: paymentData.vehicle_id || 1,
        amount: paymentData.amount || 100,
        payment_date: paymentData.payment_date || new Date().toISOString().split('T')[0],
        receipt_number: paymentData.receipt_number || 'TEST-123',
        release_order_number: paymentData.release_order_number || 'ORD-123',
        payment_method: 'cash',
        notes: 'Test payment'
    };
    
    console.log('Test payload:', testPayload);
    
    // Test API call
    fetch('/api/payments/process-release', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(testPayload)
    })
    .then(response => {
        console.log(`API Response status: ${response.status} ${response.statusText}`);
        return response.text();
    })
    .then(responseText => {
        console.log('API Response body:', responseText);
        
        try {
            const jsonResponse = JSON.parse(responseText);
            if (jsonResponse.error) {
                console.error('❌ API Error:', jsonResponse.error);
            } else {
                console.log('✅ API Success:', jsonResponse.message);
            }
        } catch (parseError) {
            console.error('❌ Failed to parse API response as JSON');
            console.log('Raw response:', responseText);
        }
    })
    .catch(error => {
        console.error('❌ Network error:', error.message);
    });
}

function createPaymentDebugButton() {
    const testBtn = document.createElement('button');
    testBtn.innerHTML = '🔍 Debug Payment Error';
    testBtn.className = 'bg-orange-500 hover:bg-orange-700 text-white font-bold py-2 px-4 rounded m-2';
    testBtn.onclick = debugPaymentError;
    
    // Add to page
    const header = document.querySelector('h1, h2, .text-2xl');
    if (header && header.parentNode) {
        header.parentNode.insertBefore(testBtn, header.nextSibling);
        console.log('🔍 Payment debug button added');
    }
}

function interceptPaymentSubmission() {
    console.log('\n4. Setting up payment form interception...');
    
    const paymentForm = document.getElementById('paymentForm');
    if (paymentForm) {
        // Remove existing event listeners and add our debug version
        const newForm = paymentForm.cloneNode(true);
        paymentForm.parentNode.replaceChild(newForm, paymentForm);
        
        newForm.addEventListener('submit', function(e) {
            e.preventDefault();
            console.log('🔍 Payment form submitted - intercepted for debugging');
            
            const formData = new FormData(newForm);
            const paymentData = Object.fromEntries(formData.entries());
            
            console.log('Submitted data:', paymentData);
            
            // Check for missing fields
            const requiredFields = ['vehicle_id', 'amount', 'payment_date', 'receipt_number', 'release_order_number'];
            const missingFields = requiredFields.filter(field => !paymentData[field] || paymentData[field].trim() === '');
            
            if (missingFields.length > 0) {
                console.error('❌ Form validation failed - missing fields:', missingFields);
                alert('Champs manquants: ' + missingFields.join(', '));
                return;
            }
            
            console.log('✅ Form validation passed');
            
            // Call the original handlePayment method
            if (window.app && typeof window.app.handlePayment === 'function') {
                console.log('🔄 Calling original handlePayment method...');
                window.app.handlePayment(e);
            } else {
                console.error('❌ handlePayment method not found');
            }
        });
        
        console.log('✅ Payment form interception set up');
    }
}

// Auto-run diagnostic
setTimeout(() => {
    console.log('🚀 Starting payment error diagnostic...');
    createPaymentDebugButton();
    interceptPaymentSubmission();
    
    console.log('\n📋 Instructions:');
    console.log('1. Click "🔍 Debug Payment Error" to test API directly');
    console.log('2. Try submitting a payment normally - it will be intercepted and debugged');
    console.log('3. Check console for detailed error information');
    
}, 8000); // Wait 8 seconds for everything to load
