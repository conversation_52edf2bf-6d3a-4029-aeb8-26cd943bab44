{"version": 3, "sources": ["../../../src/errors/database/exclusion-constraint-error.ts"], "sourcesContent": ["import DatabaseError, { DatabaseErrorSubclassOptions } from '../database-error';\n\ninterface ExclusionConstraintErrorOptions {\n  constraint?: string;\n  fields?: Record<string, string | number>;\n  table?: string;\n}\n\n/**\n * Thrown when an exclusion constraint is violated in the database\n */\nclass ExclusionConstraintError extends DatabaseError implements ExclusionConstraintErrorOptions {\n  constraint: string | undefined;\n  fields: Record<string, string | number> | undefined;\n  table: string | undefined;\n\n  constructor(\n    options: DatabaseErrorSubclassOptions & ExclusionConstraintErrorOptions\n  ) {\n    options = options || {};\n    options.parent = options.parent || { sql: '', name: '', message: '' };\n\n    super(options.parent, { stack: options.stack });\n    this.name = 'SequelizeExclusionConstraintError';\n\n    this.message = options.message || options.parent.message || '';\n    this.constraint = options.constraint;\n    this.fields = options.fields;\n    this.table = options.table;\n  }\n}\n\nexport default ExclusionConstraintError;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA,4BAA4D;AAW5D,uCAAuC,8BAAyD;AAAA,EAK9F,YACE,SACA;AACA,cAAU,WAAW;AACrB,YAAQ,SAAS,QAAQ,UAAU,EAAE,KAAK,IAAI,MAAM,IAAI,SAAS;AAEjE,UAAM,QAAQ,QAAQ,EAAE,OAAO,QAAQ;AAVzC;AACA;AACA;AASE,SAAK,OAAO;AAEZ,SAAK,UAAU,QAAQ,WAAW,QAAQ,OAAO,WAAW;AAC5D,SAAK,aAAa,QAAQ;AAC1B,SAAK,SAAS,QAAQ;AACtB,SAAK,QAAQ,QAAQ;AAAA;AAAA;AAIzB,IAAO,qCAAQ;", "names": []}