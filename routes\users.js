const express = require('express');
const router = express.Router();
const { body, validationResult } = require('express-validator');
const db = require('../config/database');
const { requireAuth, requireAdmin, hashPassword, logUserAction } = require('../middleware/auth');

// Apply authentication to all routes
router.use(requireAuth);

// Validation middleware for user creation
const validateUser = [
    body('username').isLength({ min: 3, max: 50 }).withMessage('Username must be 3-50 characters'),
    body('email').isEmail().withMessage('Valid email is required'),
    body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters'),
    body('first_name').notEmpty().withMessage('First name is required'),
    body('last_name').notEmpty().withMessage('Last name is required'),
    body('role_id').isInt({ min: 1 }).withMessage('Valid role is required')
];

// Validation middleware for user update
const validateUserUpdate = [
    body('username').optional().isLength({ min: 3, max: 50 }).withMessage('Username must be 3-50 characters'),
    body('email').optional().isEmail().withMessage('Valid email is required'),
    body('password').optional().isLength({ min: 6 }).withMessage('Password must be at least 6 characters'),
    body('first_name').optional().notEmpty().withMessage('First name cannot be empty'),
    body('last_name').optional().notEmpty().withMessage('Last name cannot be empty'),
    body('role_id').optional().isInt({ min: 1 }).withMessage('Valid role is required')
];

// GET /api/users - Get all users (Admin only)
router.get('/', requireAdmin, async (req, res) => {
    try {
        const users = await db.executeQuery(`
            SELECT u.id, u.username, u.email, u.first_name, u.last_name, 
                   u.is_active, u.last_login, u.created_at,
                   r.name as role_name, r.description as role_description
            FROM users u
            JOIN roles r ON u.role_id = r.id
            ORDER BY u.created_at DESC
        `);

        res.json(users);

    } catch (error) {
        console.error('Error fetching users:', error);
        res.status(500).json({ error: 'Failed to fetch users' });
    }
});

// GET /api/users/:id - Get single user (Admin only)
router.get('/:id', requireAdmin, async (req, res) => {
    try {
        const { id } = req.params;

        const [user] = await db.executeQuery(`
            SELECT u.id, u.username, u.email, u.first_name, u.last_name, 
                   u.is_active, u.last_login, u.created_at, u.role_id,
                   r.name as role_name, r.description as role_description
            FROM users u
            JOIN roles r ON u.role_id = r.id
            WHERE u.id = ?
        `, [id]);

        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }

        res.json(user);

    } catch (error) {
        console.error('Error fetching user:', error);
        res.status(500).json({ error: 'Failed to fetch user' });
    }
});

// POST /api/users - Create new user (Admin only)
router.post('/', requireAdmin, validateUser, async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }

        const { username, email, password, first_name, last_name, role_id } = req.body;

        // Check if username or email already exists
        const [existingUser] = await db.executeQuery(
            'SELECT id FROM users WHERE username = ? OR email = ?',
            [username, email]
        );

        if (existingUser) {
            return res.status(400).json({ error: 'Username or email already exists' });
        }

        // Hash password
        const passwordHash = await hashPassword(password);

        // Create user
        const result = await db.executeQuery(
            'INSERT INTO users (username, email, password_hash, first_name, last_name, role_id) VALUES (?, ?, ?, ?, ?, ?)',
            [username, email, passwordHash, first_name, last_name, role_id]
        );

        // Log action
        await logUserAction(req.user.id, 'user_created', 'users', result.insertId, `Created user: ${username}`, req);

        res.status(201).json({
            message: 'User created successfully',
            user_id: result.insertId
        });

    } catch (error) {
        console.error('Error creating user:', error);
        res.status(500).json({ error: 'Failed to create user' });
    }
});

// PUT /api/users/:id - Update user (Admin only)
router.put('/:id', requireAdmin, validateUserUpdate, async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }

        const { id } = req.params;
        const { username, email, password, first_name, last_name, role_id, is_active } = req.body;

        // Check if user exists
        const [existingUser] = await db.executeQuery('SELECT username FROM users WHERE id = ?', [id]);
        if (!existingUser) {
            return res.status(404).json({ error: 'User not found' });
        }

        // Prevent admin from deactivating themselves
        if (req.user.id == id && is_active === false) {
            return res.status(400).json({ error: 'Cannot deactivate your own account' });
        }

        // Build update query dynamically
        const updates = [];
        const values = [];

        if (username !== undefined) {
            updates.push('username = ?');
            values.push(username);
        }
        if (email !== undefined) {
            updates.push('email = ?');
            values.push(email);
        }
        if (password !== undefined) {
            const passwordHash = await hashPassword(password);
            updates.push('password_hash = ?');
            values.push(passwordHash);
        }
        if (first_name !== undefined) {
            updates.push('first_name = ?');
            values.push(first_name);
        }
        if (last_name !== undefined) {
            updates.push('last_name = ?');
            values.push(last_name);
        }
        if (role_id !== undefined) {
            updates.push('role_id = ?');
            values.push(role_id);
        }
        if (is_active !== undefined) {
            updates.push('is_active = ?');
            values.push(is_active);
        }

        if (updates.length === 0) {
            return res.status(400).json({ error: 'No fields to update' });
        }

        updates.push('updated_at = CURRENT_TIMESTAMP');
        values.push(id);

        const result = await db.executeQuery(
            `UPDATE users SET ${updates.join(', ')} WHERE id = ?`,
            values
        );

        if (result.affectedRows === 0) {
            return res.status(404).json({ error: 'User not found' });
        }

        // Log action
        await logUserAction(req.user.id, 'user_updated', 'users', id, `Updated user: ${existingUser.username}`, req);

        res.json({ message: 'User updated successfully' });

    } catch (error) {
        console.error('Error updating user:', error);
        if (error.code === 'ER_DUP_ENTRY') {
            res.status(400).json({ error: 'Username or email already exists' });
        } else {
            res.status(500).json({ error: 'Failed to update user' });
        }
    }
});

// DELETE /api/users/:id - Delete user (Admin only)
router.delete('/:id', requireAdmin, async (req, res) => {
    try {
        const { id } = req.params;

        // Prevent admin from deleting themselves
        if (req.user.id == id) {
            return res.status(400).json({ error: 'Cannot delete your own account' });
        }

        // Get user info before deletion
        const [user] = await db.executeQuery('SELECT username FROM users WHERE id = ?', [id]);
        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }

        // Delete user
        const result = await db.executeQuery('DELETE FROM users WHERE id = ?', [id]);

        if (result.affectedRows === 0) {
            return res.status(404).json({ error: 'User not found' });
        }

        // Log action
        await logUserAction(req.user.id, 'user_deleted', 'users', id, `Deleted user: ${user.username}`, req);

        res.json({ message: 'User deleted successfully' });

    } catch (error) {
        console.error('Error deleting user:', error);
        res.status(500).json({ error: 'Failed to delete user' });
    }
});

// GET /api/users/roles - Get all roles
router.get('/roles/list', async (req, res) => {
    try {
        const roles = await db.executeQuery('SELECT id, name, description FROM roles ORDER BY name');
        res.json(roles);

    } catch (error) {
        console.error('Error fetching roles:', error);
        res.status(500).json({ error: 'Failed to fetch roles' });
    }
});

module.exports = router;
