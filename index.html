<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion Fourrière Véhicules</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#10B981',
                        accent: '#8B5CF6',
                        dark: '#1F2937',
                        light: '#F9FAFB'
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

        * {
            font-family: 'Poppins', sans-serif;
        }

        .active-nav-item {
            background: rgba(59, 130, 246, 0.1);
            border-right: 4px solid #3B82F6;
        }

        .sidebar-minimized {
            width: 60px !important;
        }

        .sidebar-minimized span {
            display: none !important;
        }

        .sidebar-minimized .brand {
            justify-content: center;
        }

        .sidebar-minimized .brand-text {
            display: none;
        }

        .card-hover {
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .vehicle-tag {
            display: inline-block;
            padding: 0.2rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .photo-placeholder {
            background: #E5E7EB;
            border-radius: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6B7280;
        }

        .btn-primary {
            background: #3B82F6;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            border: none;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .btn-primary:hover {
            background: #2563EB;
        }

        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .error-message {
            background: #FEE2E2;
            color: #DC2626;
            padding: 0.75rem;
            border-radius: 0.375rem;
            margin: 0.5rem 0;
        }

        .success-message {
            background: #D1FAE5;
            color: #065F46;
            padding: 0.75rem;
            border-radius: 0.375rem;
            margin: 0.5rem 0;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        <div id="sidebar" class="w-64 bg-white shadow-md flex flex-col transition-all duration-300">
            <div class="p-4 border-b">
                <div class="brand flex items-center">
                    <div class="bg-primary w-10 h-10 rounded-lg flex items-center justify-center">
                        <i class="fas fa-car text-white text-xl"></i>
                    </div>
                    <span class="brand-text ml-3 text-xl font-bold text-gray-800">Fourrière Véhicules</span>
                </div>
            </div>

            <div class="flex-1 overflow-y-auto py-4">
                <nav class="space-y-1">
                    <a href="#" data-target="dashboard" class="nav-item flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 active-nav-item">
                        <i class="fas fa-chart-line text-gray-500"></i>
                        <span class="ml-3">Tableau de bord</span>
                    </a>
                    <a href="#" data-target="impounded" class="nav-item flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50">
                        <i class="fas fa-car text-gray-500"></i>
                        <span class="ml-3">Véhicules en fourrière</span>
                    </a>
                    <a href="#" data-target="pending" class="nav-item flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50">
                        <i class="fas fa-hourglass-half text-gray-500"></i>
                        <span class="ml-3">En attente de sortie</span>
                    </a>
                    <a href="#" data-target="released" class="nav-item flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50">
                        <i class="fas fa-sign-out-alt text-gray-500"></i>
                        <span class="ml-3">Véhicules sortis</span>
                    </a>
                    <a href="#" data-target="overdue" class="nav-item flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50">
                        <i class="fas fa-exclamation-triangle text-gray-500"></i>
                        <span class="ml-3">Délais dépassés (1+ an)</span>
                    </a>
                    <a href="#" data-target="reports" class="nav-item flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50">
                        <i class="fas fa-chart-pie text-gray-500"></i>
                        <span class="ml-3">Rapports</span>
                    </a>
                    <a href="#" data-target="settings" class="nav-item flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50">
                        <i class="fas fa-cog text-gray-500"></i>
                        <span class="ml-3">Paramètres</span>
                    </a>
                </nav>

                <div class="p-4 mt-6">
                    <div class="bg-blue-50 rounded-lg p-4">
                        <h3 class="text-sm font-medium text-blue-800">Besoins d'aide?</h3>
                        <p class="text-xs text-blue-600 mt-1">Notre équipe est disponible pour vous aider</p>
                        <button class="mt-3 w-full bg-primary hover:bg-blue-700 text-white text-xs py-1.5 rounded-md transition">
                            Contacter le support
                        </button>
                    </div>
                </div>
            </div>

            <div class="p-4 border-t">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-blue-600"></i>
                        </div>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-900">Adeline Dubois</p>
                        <p class="text-xs text-gray-500">Responsable fourrière</p>
                    </div>
                    <button id="sidebar-toggle" class="ml-auto p-1 rounded-md hover:bg-gray-100">
                        <i class="fas fa-chevron-left text-gray-500"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <header class="bg-white shadow">
                <div class="px-6 py-4 flex items-center">
                    <h1 id="page-title" class="text-2xl font-bold text-gray-800">Tableau de bord</h1>
                    <div class="ml-auto flex items-center">
                        <div class="relative">
                            <input type="text" id="global-search" class="border rounded-full px-4 py-1.5 text-sm w-64 focus:outline-none focus:ring-1 focus:ring-primary" placeholder="Rechercher...">
                            <i class="fas fa-search absolute right-3 top-2 text-gray-400"></i>
                        </div>
                        <div class="ml-6 flex items-center space-x-3">
                            <button id="notifications-btn" class="p-2 rounded-full hover:bg-gray-100 relative">
                                <i class="fas fa-bell text-gray-500"></i>
                                <span id="notification-badge" class="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full hidden"></span>
                            </button>
                            <button class="p-2 rounded-full hover:bg-gray-100">
                                <i class="fas fa-question-circle text-gray-500"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </header>

            <main class="flex-1 overflow-y-auto p-6">
                <!-- Dashboard Section -->
                <section id="dashboard" class="page-section">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                        <div class="bg-white rounded-xl shadow-sm p-5 card-hover">
                            <div class="flex items-center">
                                <div class="bg-blue-100 p-3 rounded-lg">
                                    <i class="fas fa-car text-blue-600 text-xl"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-gray-500 text-sm">Véhicules immobilisés</p>
                                    <h3 id="stat-impounded" class="text-2xl font-bold text-gray-800">-</h3>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white rounded-xl shadow-sm p-5 card-hover">
                            <div class="flex items-center">
                                <div class="bg-yellow-100 p-3 rounded-lg">
                                    <i class="fas fa-hourglass-half text-yellow-600 text-xl"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-gray-500 text-sm">En attente de sortie</p>
                                    <h3 id="stat-pending" class="text-2xl font-bold text-gray-800">-</h3>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white rounded-xl shadow-sm p-5 card-hover">
                            <div class="flex items-center">
                                <div class="bg-green-100 p-3 rounded-lg">
                                    <i class="fas fa-sign-out-alt text-green-600 text-xl"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-gray-500 text-sm">Sortis ce mois</p>
                                    <h3 id="stat-released" class="text-2xl font-bold text-gray-800">-</h3>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white rounded-xl shadow-sm p-5 card-hover">
                            <div class="flex items-center">
                                <div class="bg-red-100 p-3 rounded-lg">
                                    <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-gray-500 text-sm">En fourrière +1 an</p>
                                    <h3 id="stat-overdue" class="text-2xl font-bold text-gray-800">-</h3>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                        <div class="bg-white rounded-xl shadow-sm p-5">
                            <div class="flex justify-between items-center mb-4">
                                <h2 class="text-lg font-semibold text-gray-800">Statistiques mensuelles</h2>
                                <select id="stats-period" class="text-sm border rounded px-2 py-1">
                                    <option value="30">30 derniers jours</option>
                                    <option value="60">60 derniers jours</option>
                                    <option value="90">90 derniers jours</option>
                                </select>
                            </div>
                            <div class="h-64">
                                <canvas id="statsChart"></canvas>
                            </div>
                        </div>
                        <div class="bg-white rounded-xl shadow-sm p-5">
                            <div class="flex justify-between items-center mb-4">
                                <h2 class="text-lg font-semibold text-gray-800">Types de véhicules</h2>
                                <button class="text-primary text-sm">
                                    <i class="fas fa-download mr-1"></i> Exporter
                                </button>
                            </div>
                            <div class="flex">
                                <div class="w-32 h-32">
                                    <canvas id="vehicleTypesChart"></canvas>
                                </div>
                                <div class="ml-6 flex-1">
                                    <div id="vehicle-types-legend" class="space-y-2">
                                        <!-- Legend will be populated by JavaScript -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-xl shadow-sm p-5 mb-6">
                        <div class="flex justify-between items-center mb-4">
                            <h2 class="text-lg font-semibold text-gray-800">Véhicules récemment enregistrés</h2>
                            <div class="flex space-x-2">
                                <button class="btn-primary" onclick="openAddVehicleModal()">
                                    <i class="fas fa-plus mr-1"></i> Nouvelle entrée
                                </button>
                                <button class="text-primary text-sm">
                                    Voir tous
                                </button>
                            </div>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">N° Dépôt</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Véhicule</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date d'entrée</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="recent-vehicles-table" class="bg-white divide-y divide-gray-200">
                                    <!-- Content will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </section>

                <!-- Impounded Vehicles Section -->
                <section id="impounded" class="page-section hidden">
                    <div class="mb-4 flex justify-between items-center">
                        <h2 class="text-xl font-bold text-gray-800">Véhicules en fourrière</h2>
                        <button class="btn-primary" onclick="openAddVehicleModal()">
                            <i class="fas fa-plus mr-2"></i> Nouvelle entrée
                        </button>
                    </div>
                    <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                        <div class="p-4 border-b">
                            <div class="flex flex-wrap gap-4">
                                <input type="text" id="vehicle-search" placeholder="Rechercher par plaque, marque..." class="border rounded px-3 py-2 w-64">
                                <select id="vehicle-type-filter" class="border rounded px-3 py-2">
                                    <option value="">Tous les types</option>
                                </select>
                                <select id="vehicle-status-filter" class="border rounded px-3 py-2">
                                    <option value="">Tous les statuts</option>
                                    <option value="impounded">En fourrière</option>
                                    <option value="pending_release">En attente de sortie</option>
                                    <option value="released">Sorti</option>
                                    <option value="overdue">En retard</option>
                                </select>
                                <button id="apply-filters" class="btn-primary">Filtrer</button>
                            </div>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">N° Dépôt</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Immatriculation</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Marque/Modèle</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date d'entrée</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Jours</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Coût total</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="vehicles-table" class="bg-white divide-y divide-gray-200">
                                    <!-- Content will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                        <div id="vehicles-pagination" class="px-6 py-4 border-t">
                            <!-- Pagination will be populated by JavaScript -->
                        </div>
                    </div>
                </section>

                <!-- Pending Release Section -->
                <section id="pending" class="page-section hidden">
                    <div class="mb-4">
                        <h2 class="text-xl font-bold text-gray-800">Véhicules en attente de sortie</h2>
                        <div class="mt-2 text-sm text-gray-600">Véhicules avec paiement en attente</div>
                    </div>
                    <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">N° Dépôt</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Véhicule</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date d'entrée</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Jours</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Montant dû</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="pending-vehicles-table" class="bg-white divide-y divide-gray-200">
                                    <!-- Content will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </section>

                <!-- Released Vehicles Section -->
                <section id="released" class="page-section hidden">
                    <div class="mb-4">
                        <h2 class="text-xl font-bold text-gray-800">Véhicules sortis</h2>
                    </div>
                    <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">N° Dépôt</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Véhicule</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date d'entrée</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date de sortie</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Durée</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Montant payé</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="released-vehicles-table" class="bg-white divide-y divide-gray-200">
                                    <!-- Content will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </section>

                <!-- Overdue Vehicles Section -->
                <section id="overdue" class="page-section hidden">
                    <div class="mb-4">
                        <h2 class="text-xl font-bold text-gray-800">Véhicules en fourrière depuis plus d'un an</h2>
                        <div class="mt-2 text-sm text-red-600">Ces véhicules nécessitent une attention particulière</div>
                    </div>
                    <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">N° Dépôt</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Véhicule</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date d'entrée</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Jours</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Coût accumulé</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="overdue-vehicles-table" class="bg-white divide-y divide-gray-200">
                                    <!-- Content will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </section>

                <!-- Reports Section -->
                <section id="reports" class="page-section hidden">
                    <div class="mb-6">
                        <h2 class="text-xl font-bold text-gray-800">Rapports et Statistiques</h2>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                        <div class="bg-white rounded-xl shadow-sm p-6 card-hover cursor-pointer" onclick="generateReport('summary')">
                            <div class="flex items-center">
                                <div class="bg-blue-100 p-3 rounded-lg">
                                    <i class="fas fa-chart-bar text-blue-600 text-xl"></i>
                                </div>
                                <div class="ml-4">
                                    <h3 class="text-lg font-semibold text-gray-800">Rapport de synthèse</h3>
                                    <p class="text-sm text-gray-600">Vue d'ensemble des activités</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl shadow-sm p-6 card-hover cursor-pointer" onclick="generateReport('financial')">
                            <div class="flex items-center">
                                <div class="bg-green-100 p-3 rounded-lg">
                                    <i class="fas fa-euro-sign text-green-600 text-xl"></i>
                                </div>
                                <div class="ml-4">
                                    <h3 class="text-lg font-semibold text-gray-800">Rapport financier</h3>
                                    <p class="text-sm text-gray-600">Revenus et paiements</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl shadow-sm p-6 card-hover cursor-pointer" onclick="generateReport('vehicles')">
                            <div class="flex items-center">
                                <div class="bg-purple-100 p-3 rounded-lg">
                                    <i class="fas fa-car text-purple-600 text-xl"></i>
                                </div>
                                <div class="ml-4">
                                    <h3 class="text-lg font-semibold text-gray-800">Rapport véhicules</h3>
                                    <p class="text-sm text-gray-600">Détails des véhicules</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="report-content" class="bg-white rounded-xl shadow-sm p-6 hidden">
                        <div class="flex justify-between items-center mb-4">
                            <h3 id="report-title" class="text-lg font-semibold text-gray-800"></h3>
                            <button id="export-report" class="btn-primary">
                                <i class="fas fa-download mr-2"></i> Exporter
                            </button>
                        </div>
                        <div id="report-data">
                            <!-- Report content will be populated by JavaScript -->
                        </div>
                    </div>
                </section>

                <!-- Settings Section -->
                <section id="settings" class="page-section hidden">
                    <div class="mb-6">
                        <h2 class="text-xl font-bold text-gray-800">Paramètres du système</h2>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div class="bg-white rounded-xl shadow-sm p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">Paramètres généraux</h3>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Coût de stockage par jour (DH)</label>
                                    <input type="number" id="storage-cost" class="w-full border rounded-md px-3 py-2" value="25.00" step="0.01">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Seuil de retard (jours)</label>
                                    <input type="number" id="overdue-threshold" class="w-full border rounded-md px-3 py-2" value="365">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Devise</label>
                                    <input type="text" id="currency" class="w-full border rounded-md px-3 py-2" value="DH">
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl shadow-sm p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">Informations de l'organisation</h3>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Nom de la fourrière</label>
                                    <input type="text" id="company-name" class="w-full border rounded-md px-3 py-2" value="Fourrière Communale">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Adresse</label>
                                    <textarea id="company-address" class="w-full border rounded-md px-3 py-2" rows="3">Adresse de la fourrière</textarea>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Téléphone</label>
                                    <input type="text" id="company-phone" class="w-full border rounded-md px-3 py-2" value="+212 XXX XXX XXX">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                                    <input type="email" id="company-email" class="w-full border rounded-md px-3 py-2" value="<EMAIL>">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6 bg-white rounded-xl shadow-sm p-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">Liste des déposants</h3>
                        <div class="mb-4">
                            <div class="flex gap-2">
                                <input type="text" id="new-depositor" placeholder="Nom du déposant" class="flex-1 border rounded-md px-3 py-2">
                                <button id="add-depositor" class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600">
                                    <i class="fas fa-plus mr-1"></i> Ajouter
                                </button>
                            </div>
                        </div>
                        <div class="border rounded-md">
                            <div class="bg-gray-50 px-4 py-2 border-b">
                                <h4 class="font-medium text-gray-700">Déposants configurés</h4>
                            </div>
                            <div id="depositors-list" class="divide-y">
                                <!-- Liste des déposants sera générée par JavaScript -->
                            </div>
                        </div>
                    </div>

                    <!-- User Management Section -->
                    <div class="mt-8">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">
                            <i class="fas fa-users mr-2"></i>Gestion des utilisateurs
                        </h3>
                        <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <p class="text-sm text-gray-600">Gérer les utilisateurs et leurs rôles d'accès</p>
                                <button id="add-user-btn" class="btn-primary">
                                    <i class="fas fa-user-plus mr-2"></i>Ajouter un utilisateur
                                </button>
                            </div>
                            <div class="border rounded-md">
                                <div class="bg-gray-50 px-4 py-2 border-b">
                                    <h4 class="font-medium text-gray-700">Utilisateurs du système</h4>
                                </div>
                                <div id="users-list" class="divide-y">
                                    <!-- Liste des utilisateurs sera générée par JavaScript -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6 flex justify-end">
                        <button id="save-settings" class="btn-primary">
                            <i class="fas fa-save mr-2"></i> Sauvegarder les paramètres
                        </button>
                    </div>
                </section>
            </main>

            <!-- Footer -->
            <footer class="bg-white border-t py-4 px-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500">© 2023 Fourrière Communale. Tous droits réservés.</p>
                    </div>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-gray-500">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-gray-500">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-gray-500">
                            <i class="fab fa-instagram"></i>
                        </a>
                    </div>
                </div>
            </footer>
        </div>
    </div>

    <!-- Modals -->
    <div id="addVehicleModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg w-full max-w-2xl max-h-screen overflow-y-auto">
            <div class="border-b px-6 py-4 flex justify-between items-center">
                <h3 class="text-lg font-medium text-gray-900">Nouvelle entrée de véhicule</h3>
                <button onclick="closeModal('addVehicleModal')" class="text-gray-400 hover:text-gray-500">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="addVehicleForm" class="px-6 py-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">N° de dépôt *</label>
                        <input type="text" name="depot_number" class="w-full border rounded-md px-3 py-2" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Immatriculation *</label>
                        <input type="text" name="license_plate" class="w-full border rounded-md px-3 py-2" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Type de véhicule *</label>
                        <select name="vehicle_type_id" class="w-full border rounded-md px-3 py-2" required>
                            <option value="">Sélectionner...</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Déposant *</label>
                        <select name="depositor" class="w-full border rounded-md px-3 py-2" required>
                            <option value="">Sélectionner...</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Marque</label>
                        <input type="text" name="brand" class="w-full border rounded-md px-3 py-2">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Date d'entrée *</label>
                        <input type="date" name="entry_date" class="w-full border rounded-md px-3 py-2" required>
                    </div>
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Observations</label>
                        <textarea name="observations" rows="3" class="w-full border rounded-md px-3 py-2"></textarea>
                    </div>
                </div>

                <div class="border-t px-6 py-4 flex justify-end space-x-3 -mx-6 mt-6">
                    <button type="button" onclick="closeModal('addVehicleModal')" class="px-4 py-2 border rounded-md text-gray-700 hover:bg-gray-50">
                        Annuler
                    </button>
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        Enregistrer
                    </button>
                </div>
            </form>
        </div>
    </div>

    <div id="paymentModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg w-full max-w-md">
            <div class="border-b px-6 py-4 flex justify-between items-center">
                <h3 class="text-lg font-medium text-gray-900">Paiement et sortie de véhicule</h3>
                <button onclick="closeModal('paymentModal')" class="text-gray-400 hover:text-gray-500">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="paymentForm" class="px-6 py-4">
                <input type="hidden" name="vehicle_id" id="payment-vehicle-id">
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Véhicule</label>
                        <div id="payment-vehicle-info" class="text-sm text-gray-600 bg-gray-50 p-2 rounded"></div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Montant à payer</label>
                        <input type="number" name="amount" id="payment-amount" class="w-full border rounded-md px-3 py-2" step="0.01" readonly>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Date de paiement</label>
                        <input type="date" name="payment_date" class="w-full border rounded-md px-3 py-2" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">N° de quittance</label>
                        <input type="text" name="receipt_number" class="w-full border rounded-md px-3 py-2" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Méthode de paiement</label>
                        <select name="payment_method" class="w-full border rounded-md px-3 py-2" required>
                            <option value="cash">Espèces</option>
                            <option value="check">Chèque</option>
                            <option value="bank_transfer">Virement bancaire</option>
                            <option value="card">Carte bancaire</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">N° d'ordre de sortie</label>
                        <input type="text" name="release_order_number" class="w-full border rounded-md px-3 py-2" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Date de sortie</label>
                        <input type="date" name="release_date" class="w-full border rounded-md px-3 py-2" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Libéré à</label>
                        <input type="text" name="released_to_name" class="w-full border rounded-md px-3 py-2" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Téléphone</label>
                        <input type="text" name="released_to_phone" class="w-full border rounded-md px-3 py-2">
                    </div>
                </div>
                <div class="border-t px-6 py-4 flex justify-end space-x-3 -mx-6 mt-6">
                    <button type="button" onclick="closeModal('paymentModal')" class="px-4 py-2 border rounded-md text-gray-700 hover:bg-gray-50">
                        Annuler
                    </button>
                    <button type="submit" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                        Valider le paiement
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- User Management Modal -->
    <div id="userModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg w-full max-w-md">
            <div class="border-b px-6 py-4 flex justify-between items-center">
                <h3 id="userModalTitle" class="text-lg font-medium text-gray-900">Ajouter un utilisateur</h3>
                <button onclick="closeModal('userModal')" class="text-gray-400 hover:text-gray-500">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="userForm" class="px-6 py-4">
                <input type="hidden" id="user-id" name="user_id">
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Nom d'utilisateur *</label>
                        <input type="text" id="user-username" name="username" class="w-full border rounded-md px-3 py-2" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Email *</label>
                        <input type="email" id="user-email" name="email" class="w-full border rounded-md px-3 py-2" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Mot de passe *</label>
                        <input type="password" id="user-password" name="password" class="w-full border rounded-md px-3 py-2" required>
                        <p class="text-xs text-gray-500 mt-1">Minimum 6 caractères</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Prénom *</label>
                        <input type="text" id="user-first-name" name="first_name" class="w-full border rounded-md px-3 py-2" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Nom *</label>
                        <input type="text" id="user-last-name" name="last_name" class="w-full border rounded-md px-3 py-2" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Rôle *</label>
                        <select id="user-role" name="role_id" class="w-full border rounded-md px-3 py-2" required>
                            <option value="">Sélectionner un rôle...</option>
                        </select>
                    </div>
                    <div id="user-active-field" class="hidden">
                        <label class="flex items-center">
                            <input type="checkbox" id="user-active" name="is_active" class="mr-2">
                            <span class="text-sm text-gray-700">Compte actif</span>
                        </label>
                    </div>
                </div>
                <div class="border-t px-6 py-4 flex justify-end space-x-3 -mx-6 mt-6">
                    <button type="button" onclick="closeModal('userModal')" class="px-4 py-2 border rounded-md text-gray-700 hover:bg-gray-50">
                        Annuler
                    </button>
                    <button type="submit" id="userSubmitBtn" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        Ajouter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Debug Script -->
    <script>
        // Debug script to ensure modal functions work
        window.addEventListener('load', function() {
            console.log('Page fully loaded');
            console.log('Modal element:', document.getElementById('addVehicleModal'));
            console.log('openAddVehicleModal function:', typeof window.openAddVehicleModal);

            // Add a backup event listener for all "Nouvelle entrée" buttons
            setTimeout(() => {
                const buttons = document.querySelectorAll('button');
                buttons.forEach(button => {
                    if (button.textContent.includes('Nouvelle entrée')) {
                        console.log('Found "Nouvelle entrée" button:', button);
                        button.addEventListener('click', function(e) {
                            console.log('Button clicked!');
                            e.preventDefault();
                            e.stopPropagation();

                            const modal = document.getElementById('addVehicleModal');
                            if (modal) {
                                console.log('Opening modal...');
                                modal.classList.remove('hidden');

                                // Set default values
                                const dateInput = document.querySelector('input[name="entry_date"]');
                                if (dateInput) {
                                    dateInput.value = new Date().toISOString().split('T')[0];
                                }

                                const timeInput = document.querySelector('input[name="entry_time"]');
                                if (timeInput) {
                                    const now = new Date();
                                    const timeString = now.getHours().toString().padStart(2, '0') + ':' + now.getMinutes().toString().padStart(2, '0');
                                    timeInput.value = timeString;
                                }
                            } else {
                                console.error('Modal not found!');
                            }
                        });
                    }
                });
            }, 1000);
        });
    </script>

    <!-- Load the main application JavaScript -->
    <script src="/js/app.js"></script>
</body>
</html>