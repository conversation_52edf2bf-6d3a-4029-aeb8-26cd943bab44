<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion Fourrière Véhicules</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .app-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .glass-effect {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }
        .glass-card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .glass-sidebar {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-right: 1px solid rgba(255, 255, 255, 0.2);
        }
        .modern-input {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
        }
        .modern-input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }
        .modern-input:focus {
            outline: none;
            ring: 2px;
            ring-color: rgba(255, 255, 255, 0.5);
            border-color: transparent;
            background: rgba(255, 255, 255, 0.25);
        }
        .modern-button {
            background: rgba(255, 255, 255, 0.9);
            color: #667eea;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .modern-button:hover {
            background: white;
            transform: translateY(-1px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }
        .modern-button-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
        }
        .modern-button-secondary:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
        }
        .sidebar-item {
            transition: all 0.3s ease;
            border-radius: 12px;
            margin: 4px 0;
        }
        .sidebar-item:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateX(4px);
        }
        .sidebar-item.active {
            background: rgba(255, 255, 255, 0.25);
            border-left: 4px solid white;
        }
        .stat-card {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.25);
            transition: all 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-4px);
            background: rgba(255, 255, 255, 0.25);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        .table-glass {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        .modal-glass {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        .text-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .header-glass {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        .footer-glass {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#10B981',
                        accent: '#8B5CF6',
                        dark: '#1F2937',
                        light: '#F9FAFB'
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

        * {
            font-family: 'Poppins', sans-serif;
        }

        .active-nav-item {
            background: rgba(59, 130, 246, 0.1);
            border-right: 4px solid #3B82F6;
        }

        .sidebar-minimized {
            width: 60px !important;
        }

        .sidebar-minimized span {
            display: none !important;
        }

        .sidebar-minimized .brand {
            justify-content: center;
        }

        .sidebar-minimized .brand-text {
            display: none;
        }

        .card-hover {
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .vehicle-tag {
            display: inline-block;
            padding: 0.2rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .photo-placeholder {
            background: #E5E7EB;
            border-radius: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6B7280;
        }

        .btn-primary {
            background: #3B82F6;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            border: none;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .btn-primary:hover {
            background: #2563EB;
        }

        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .error-message {
            background: #FEE2E2;
            color: #DC2626;
            padding: 0.75rem;
            border-radius: 0.375rem;
            margin: 0.5rem 0;
        }

        .success-message {
            background: #D1FAE5;
            color: #065F46;
            padding: 0.75rem;
            border-radius: 0.375rem;
            margin: 0.5rem 0;
        }

        /* Professional Blue Theme */
        .app-bg {
            background: #f8fafc;
            min-height: 100vh;
        }
        .blue-header {
            background: linear-gradient(135deg, #4f46e5 0%, #3b82f6 100%);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .white-sidebar {
            background: white;
            border-right: 1px solid #e5e7eb;
            box-shadow: 2px 0 4px rgba(0, 0, 0, 0.05);
        }
        .professional-input {
            background: white;
            border: 1px solid #d1d5db;
            color: #374151;
        }
        .professional-input::placeholder {
            color: #9ca3af;
        }
        .professional-input:focus {
            outline: none;
            border-color: #4f46e5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }
        .btn-blue {
            background: #4f46e5;
            color: white;
            font-weight: 600;
            transition: all 0.2s ease;
            border: none;
            cursor: pointer;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
        }
        .btn-blue:hover {
            background: #4338ca;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
        }
        .btn-green {
            background: #10b981;
            color: white;
            font-weight: 600;
            transition: all 0.2s ease;
            border: none;
            cursor: pointer;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
        }
        .btn-green:hover {
            background: #059669;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }
        .btn-outline {
            background: transparent;
            color: #6b7280;
            border: 1px solid #d1d5db;
            transition: all 0.2s ease;
            cursor: pointer;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
        }
        .btn-outline:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }
        .sidebar-item {
            transition: all 0.2s ease;
            border-radius: 8px;
            margin: 2px 0;
        }
        .sidebar-item:hover {
            background: #f3f4f6;
        }
        .sidebar-item.active {
            background: #eff6ff;
            color: #2563eb;
            border-left: 4px solid #2563eb;
        }
        .stat-card {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            transition: all 0.2s ease;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        .content-card {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .modal-professional {
            background: white;
            border: 1px solid #e5e7eb;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }
        .text-blue {
            color: #2563eb;
        }
        .text-green {
            color: #059669;
        }
        .text-purple {
            color: #7c3aed;
        }
        .text-orange {
            color: #ea580c;
        }
        .bg-blue-light {
            background: #eff6ff;
        }
        .bg-green-light {
            background: #ecfdf5;
        }
        .bg-purple-light {
            background: #f3e8ff;
        }
        .bg-orange-light {
            background: #fff7ed;
        }
        .table-header {
            background: #f8fafc;
            border-bottom: 2px solid #e5e7eb;
        }
        .action-button {
            padding: 0.25rem 0.75rem;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            transition: all 0.2s ease;
        }
    </style>
</head>
<body class="app-bg">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        <div id="sidebar" class="w-64 white-sidebar flex flex-col transition-all duration-300">
            <div class="p-4 border-b border-gray-200">
                <div class="brand flex items-center">
                    <div class="bg-blue-600 w-10 h-10 rounded-lg flex items-center justify-center">
                        <i class="fas fa-car text-white text-xl"></i>
                    </div>
                    <span class="brand-text ml-3 text-xl font-bold text-gray-800">Fourrière Communale</span>
                </div>
                <p class="text-gray-500 text-sm mt-2">Gestion des véhicules en fourrière</p>
            </div>

            <div class="flex-1 overflow-y-auto py-4">
                <nav class="space-y-1 px-2">
                    <a href="#" data-target="dashboard" class="nav-item sidebar-item flex items-center px-4 py-3 text-gray-700 active-nav-item">
                        <i class="fas fa-chart-line text-blue-600"></i>
                        <span class="ml-3">Tableau de bord</span>
                    </a>
                    <a href="#" data-target="impounded" class="nav-item sidebar-item flex items-center px-4 py-3 text-gray-700">
                        <i class="fas fa-car text-gray-500"></i>
                        <span class="ml-3">Véhicules en fourrière</span>
                    </a>
                    <a href="#" data-target="pending" class="nav-item sidebar-item flex items-center px-4 py-3 text-gray-700">
                        <i class="fas fa-hourglass-half text-gray-500"></i>
                        <span class="ml-3">En attente de sortie</span>
                    </a>
                    <a href="#" data-target="released" class="nav-item sidebar-item flex items-center px-4 py-3 text-gray-700">
                        <i class="fas fa-sign-out-alt text-gray-500"></i>
                        <span class="ml-3">Véhicules sortis</span>
                    </a>
                    <a href="#" data-target="overdue" class="nav-item sidebar-item flex items-center px-4 py-3 text-gray-700">
                        <i class="fas fa-exclamation-triangle text-gray-500"></i>
                        <span class="ml-3">Délais dépassés (1+ an)</span>
                    </a>
                    <a href="#" data-target="reports" class="nav-item sidebar-item flex items-center px-4 py-3 text-gray-700">
                        <i class="fas fa-chart-pie text-gray-500"></i>
                        <span class="ml-3">Rapports</span>
                    </a>
                    <a href="#" data-target="settings" class="nav-item sidebar-item flex items-center px-4 py-3 text-gray-700">
                        <i class="fas fa-cog text-gray-500"></i>
                        <span class="ml-3">Paramètres</span>
                    </a>
                </nav>

                <div class="p-4 mt-6">
                    <div class="bg-blue-50 rounded-lg p-4 border border-blue-200">
                        <h3 class="text-sm font-medium text-blue-800">Besoins d'aide?</h3>
                        <p class="text-xs text-blue-600 mt-1">Notre équipe est disponible pour vous aider</p>
                        <button class="mt-3 w-full btn-blue text-xs py-1.5 rounded-md transition">
                            Contacter le support
                        </button>
                    </div>
                </div>
            </div>

            <div class="p-4 border-t border-gray-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-blue-600"></i>
                        </div>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-900">Admin</p>
                        <p class="text-xs text-gray-500">Administrateur</p>
                    </div>
                    <button id="sidebar-toggle" class="ml-auto p-1 rounded-md hover:bg-gray-100">
                        <i class="fas fa-chevron-left text-gray-500"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <header class="blue-header">
                <div class="px-6 py-4 flex items-center">
                    <h1 id="page-title" class="text-2xl font-bold text-white">Fourrière Communale</h1>
                    <p class="text-blue-100 text-sm ml-4">Gestion des véhicules en fourrière</p>
                    <div class="ml-auto flex items-center">
                        <div class="relative">
                            <input type="text" id="global-search" class="professional-input rounded-full px-4 py-1.5 text-sm w-64 focus:outline-none" placeholder="Rechercher...">
                            <i class="fas fa-search absolute right-3 top-2 text-gray-400"></i>
                        </div>
                        <div class="ml-6 flex items-center space-x-3">
                            <button id="notifications-btn" class="p-2 rounded-full hover:bg-white hover:bg-opacity-20 relative">
                                <i class="fas fa-bell text-white"></i>
                                <span id="notification-badge" class="absolute top-1 right-1 w-3 h-3 bg-red-500 rounded-full text-xs text-white flex items-center justify-center hidden">1</span>
                            </button>
                            <button class="p-2 rounded-full hover:bg-white hover:bg-opacity-20">
                                <i class="fas fa-question-circle text-white"></i>
                            </button>
                            <div class="flex items-center ml-4">
                                <div class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-2">
                                    <i class="fas fa-user text-white text-sm"></i>
                                </div>
                                <span class="text-white text-sm mr-3">Admin</span>
                                <button id="logout-btn" class="btn-outline bg-white bg-opacity-20 border-white border-opacity-30 text-white hover:bg-opacity-30 px-3 py-1.5 rounded-md text-sm">
                                    <i class="fas fa-sign-out-alt mr-1"></i>Déconnexion
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <main class="flex-1 overflow-y-auto p-6">
                <!-- Dashboard Section -->
                <section id="dashboard" class="page-section">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                        <div class="stat-card p-6">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-gray-500 text-sm">Véhicules en fourrière</p>
                                    <h3 id="stat-impounded" class="text-3xl font-bold text-gray-800 mt-1">42</h3>
                                </div>
                                <div class="bg-blue-light p-3 rounded-lg">
                                    <i class="fas fa-car text-blue text-2xl"></i>
                                </div>
                            </div>
                        </div>
                        <div class="stat-card p-6">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-gray-500 text-sm">Sorties ce mois</p>
                                    <h3 id="stat-pending" class="text-3xl font-bold text-gray-800 mt-1">28</h3>
                                </div>
                                <div class="bg-green-light p-3 rounded-lg">
                                    <i class="fas fa-sign-out-alt text-green text-2xl"></i>
                                </div>
                            </div>
                        </div>
                        <div class="stat-card p-6">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-gray-500 text-sm">Revenus ce mois</p>
                                    <h3 id="stat-released" class="text-3xl font-bold text-gray-800 mt-1">9,750 DA</h3>
                                </div>
                                <div class="bg-purple-light p-3 rounded-lg">
                                    <i class="fas fa-coins text-purple text-2xl"></i>
                                </div>
                            </div>
                        </div>
                        <div class="stat-card p-6">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-gray-500 text-sm">Véhicules abandonnés</p>
                                    <h3 id="stat-overdue" class="text-3xl font-bold text-gray-800 mt-1">7</h3>
                                </div>
                                <div class="bg-orange-light p-3 rounded-lg">
                                    <i class="fas fa-exclamation-triangle text-orange text-2xl"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                        <div class="content-card p-6">
                            <div class="flex justify-between items-center mb-4">
                                <h2 class="text-lg font-semibold text-gray-800">Statistiques mensuelles</h2>
                                <select id="stats-period" class="professional-input text-sm rounded px-2 py-1">
                                    <option value="30">30 derniers jours</option>
                                    <option value="60">60 derniers jours</option>
                                    <option value="90">90 derniers jours</option>
                                </select>
                            </div>
                            <div class="h-64">
                                <canvas id="statsChart"></canvas>
                            </div>
                        </div>
                        <div class="content-card p-6">
                            <div class="flex justify-between items-center mb-4">
                                <h2 class="text-lg font-semibold text-gray-800">Types de véhicules</h2>
                                <button class="btn-outline text-sm px-3 py-1 rounded">
                                    <i class="fas fa-download mr-1"></i> Exporter
                                </button>
                            </div>
                            <div class="flex">
                                <div class="w-32 h-32">
                                    <canvas id="vehicleTypesChart"></canvas>
                                </div>
                                <div class="ml-6 flex-1">
                                    <div id="vehicle-types-legend" class="space-y-2">
                                        <!-- Legend will be populated by JavaScript -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="content-card p-6 mb-6">
                        <div class="flex justify-between items-center mb-6">
                            <h2 class="text-lg font-semibold text-blue-600">Derniers véhicules entrés</h2>
                            <button class="btn-green" onclick="openAddVehicleModal()">
                                <i class="fas fa-plus mr-2"></i> Ajouter
                            </button>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="min-w-full">
                                <thead>
                                    <tr class="table-header">
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">PLAQUE</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">TYPE</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">DATE ENTRÉE</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">NBR JOURS</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">MONTANT</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">ACTIONS</th>
                                    </tr>
                                </thead>
                                <tbody id="recent-vehicles-table" class="bg-white divide-y divide-gray-100">
                                    <!-- Sample data matching the reference image -->
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-4 py-4">
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">1234567</div>
                                                <div class="text-sm text-gray-500">Algérie</div>
                                            </div>
                                        </td>
                                        <td class="px-4 py-4">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                Voiture
                                            </span>
                                        </td>
                                        <td class="px-4 py-4 text-sm text-gray-900">15/06/2023</td>
                                        <td class="px-4 py-4 text-sm font-medium text-red-600">5</td>
                                        <td class="px-4 py-4 text-sm font-medium text-gray-900">2,500 DA</td>
                                        <td class="px-4 py-4 text-sm font-medium">
                                            <div class="flex space-x-2">
                                                <button class="text-blue-600 hover:text-blue-900"><i class="fas fa-eye"></i></button>
                                                <button class="text-green-600 hover:text-green-900"><i class="fas fa-edit"></i></button>
                                                <button class="text-red-600 hover:text-red-900"><i class="fas fa-trash"></i></button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Actions rapides et dernières sorties -->
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- Actions rapides -->
                        <div class="content-card p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">Actions rapides</h3>
                            <div class="grid grid-cols-2 gap-3">
                                <button class="btn-blue p-4 rounded-lg text-center" onclick="openAddVehicleModal()">
                                    <i class="fas fa-car text-2xl mb-2"></i>
                                    <div class="text-sm font-medium">Nouvelle entrée</div>
                                </button>
                                <button class="btn-green p-4 rounded-lg text-center">
                                    <i class="fas fa-sign-out-alt text-2xl mb-2"></i>
                                    <div class="text-sm font-medium">Sortie véhicule</div>
                                </button>
                                <button class="bg-purple-600 hover:bg-purple-700 text-white p-4 rounded-lg text-center transition-all">
                                    <i class="fas fa-coins text-2xl mb-2"></i>
                                    <div class="text-sm font-medium">Encaissement</div>
                                </button>
                                <button class="bg-orange-600 hover:bg-orange-700 text-white p-4 rounded-lg text-center transition-all">
                                    <i class="fas fa-file-alt text-2xl mb-2"></i>
                                    <div class="text-sm font-medium">Rapports</div>
                                </button>
                            </div>
                        </div>

                        <!-- Dernières sorties -->
                        <div class="content-card p-6 lg:col-span-2">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">Dernières sorties</h3>
                            <div class="space-y-3">
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">1231232</div>
                                        <div class="text-xs text-gray-500">Voiture - 14/06/2023</div>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-sm font-medium text-green-600">3,500 DA</div>
                                        <div class="text-xs text-gray-500">Espèces</div>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">4564564</div>
                                        <div class="text-xs text-gray-500">Camion - 13/06/2023</div>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-sm font-medium text-green-600">5,000 DA</div>
                                        <div class="text-xs text-gray-500">Chèque</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tarifs de la fourrière -->
                    <div class="content-card p-6 mt-6">
                        <h2 class="text-lg font-semibold text-blue-600 mb-6">Tarifs de la fourrière</h2>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div class="text-center">
                                <div class="bg-blue-light p-4 rounded-lg mb-3">
                                    <i class="fas fa-car text-blue text-3xl"></i>
                                </div>
                                <h3 class="font-semibold text-gray-800">Voitures</h3>
                                <p class="text-gray-600 text-sm mt-1">Véhicules légers</p>
                            </div>
                            <div class="text-center">
                                <div class="bg-purple-light p-4 rounded-lg mb-3">
                                    <i class="fas fa-truck text-purple text-3xl"></i>
                                </div>
                                <h3 class="font-semibold text-gray-800">Camions</h3>
                                <p class="text-gray-600 text-sm mt-1">Véhicules lourds</p>
                            </div>
                            <div class="text-center">
                                <div class="bg-orange-light p-4 rounded-lg mb-3">
                                    <i class="fas fa-motorcycle text-orange text-3xl"></i>
                                </div>
                                <h3 class="font-semibold text-gray-800">Motos</h3>
                                <p class="text-gray-600 text-sm mt-1">Deux roues</p>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Impounded Vehicles Section -->
                <section id="impounded" class="page-section hidden">
                    <div class="mb-6 flex justify-between items-center">
                        <div>
                            <h2 class="text-xl font-bold text-gray-800">Véhicules en fourrière</h2>
                            <p class="text-gray-600 text-sm mt-1">Gestion des véhicules actuellement en fourrière</p>
                        </div>
                        <div class="flex space-x-3">
                            <button id="add-vehicle-btn" class="btn-green">
                                <i class="fas fa-plus mr-2"></i> Nouvelle entrée
                            </button>
                            <button class="btn-blue" onclick="app.refreshVehiclesList()">
                                <i class="fas fa-sync-alt mr-2"></i> Actualiser
                            </button>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                        <div class="p-4 border-b">
                            <div class="flex flex-wrap gap-4">
                                <input type="text" id="vehicle-search" placeholder="Rechercher par plaque, marque..." class="border rounded px-3 py-2 w-64">
                                <select id="vehicle-type-filter" class="border rounded px-3 py-2">
                                    <option value="">Tous les types</option>
                                </select>
                                <select id="vehicle-status-filter" class="border rounded px-3 py-2">
                                    <option value="">Tous les statuts</option>
                                    <option value="impounded">En fourrière</option>
                                    <option value="pending_release">En attente de sortie</option>
                                    <option value="released">Sorti</option>
                                    <option value="overdue">En retard</option>
                                </select>
                                <button id="apply-filters" class="btn-primary">Filtrer</button>
                            </div>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">N° Dépôt</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Immatriculation</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Marque/Modèle</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date d'entrée</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Jours</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Coût total</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="vehicles-table" class="bg-white divide-y divide-gray-200">
                                    <!-- Content will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                        <div id="vehicles-pagination" class="px-6 py-4 border-t">
                            <!-- Pagination will be populated by JavaScript -->
                        </div>
                    </div>
                </section>

                <!-- Pending Release Section -->
                <section id="pending" class="page-section hidden">
                    <div class="mb-4">
                        <h2 class="text-xl font-bold text-gray-800">Véhicules en attente de sortie</h2>
                        <div class="mt-2 text-sm text-gray-600">Véhicules avec paiement en attente</div>
                    </div>
                    <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">N° Dépôt</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Véhicule</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date d'entrée</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Jours</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Montant dû</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="pending-vehicles-table" class="bg-white divide-y divide-gray-200">
                                    <!-- Content will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </section>

                <!-- Released Vehicles Section -->
                <section id="released" class="page-section hidden">
                    <div class="mb-4">
                        <h2 class="text-xl font-bold text-gray-800">Véhicules sortis</h2>
                    </div>
                    <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">N° Dépôt</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Véhicule</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date d'entrée</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date de sortie</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Durée</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Montant payé</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="released-vehicles-table" class="bg-white divide-y divide-gray-200">
                                    <!-- Content will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </section>

                <!-- Overdue Vehicles Section -->
                <section id="overdue" class="page-section hidden">
                    <div class="mb-4">
                        <h2 class="text-xl font-bold text-gray-800">Véhicules en fourrière depuis plus d'un an</h2>
                        <div class="mt-2 text-sm text-red-600">Ces véhicules nécessitent une attention particulière</div>
                    </div>
                    <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">N° Dépôt</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Véhicule</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date d'entrée</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Jours</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Coût accumulé</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="overdue-vehicles-table" class="bg-white divide-y divide-gray-200">
                                    <!-- Content will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </section>

                <!-- Reports Section -->
                <section id="reports" class="page-section hidden">
                    <div class="mb-6">
                        <h2 class="text-xl font-bold text-gray-800">Rapports et Statistiques</h2>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                        <div class="bg-white rounded-xl shadow-sm p-6 card-hover cursor-pointer" onclick="generateReport('summary')">
                            <div class="flex items-center">
                                <div class="bg-blue-100 p-3 rounded-lg">
                                    <i class="fas fa-chart-bar text-blue-600 text-xl"></i>
                                </div>
                                <div class="ml-4">
                                    <h3 class="text-lg font-semibold text-gray-800">Rapport de synthèse</h3>
                                    <p class="text-sm text-gray-600">Vue d'ensemble des activités</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl shadow-sm p-6 card-hover cursor-pointer" onclick="generateReport('financial')">
                            <div class="flex items-center">
                                <div class="bg-green-100 p-3 rounded-lg">
                                    <i class="fas fa-euro-sign text-green-600 text-xl"></i>
                                </div>
                                <div class="ml-4">
                                    <h3 class="text-lg font-semibold text-gray-800">Rapport financier</h3>
                                    <p class="text-sm text-gray-600">Revenus et paiements</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl shadow-sm p-6 card-hover cursor-pointer" onclick="generateReport('vehicles')">
                            <div class="flex items-center">
                                <div class="bg-purple-100 p-3 rounded-lg">
                                    <i class="fas fa-car text-purple-600 text-xl"></i>
                                </div>
                                <div class="ml-4">
                                    <h3 class="text-lg font-semibold text-gray-800">Rapport véhicules</h3>
                                    <p class="text-sm text-gray-600">Détails des véhicules</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="report-content" class="bg-white rounded-xl shadow-sm p-6 hidden">
                        <div class="flex justify-between items-center mb-4">
                            <h3 id="report-title" class="text-lg font-semibold text-gray-800"></h3>
                            <button id="export-report" class="btn-primary">
                                <i class="fas fa-download mr-2"></i> Exporter
                            </button>
                        </div>
                        <div id="report-data">
                            <!-- Report content will be populated by JavaScript -->
                        </div>
                    </div>
                </section>

                <!-- Settings Section -->
                <section id="settings" class="page-section hidden">
                    <div class="mb-6">
                        <h2 class="text-xl font-bold text-gray-800">Paramètres du système</h2>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div class="bg-white rounded-xl shadow-sm p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">Paramètres généraux</h3>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Coût de stockage par jour (DH)</label>
                                    <input type="number" id="storage-cost" class="w-full border rounded-md px-3 py-2" value="25.00" step="0.01">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Seuil de retard (jours)</label>
                                    <input type="number" id="overdue-threshold" class="w-full border rounded-md px-3 py-2" value="365">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Devise</label>
                                    <input type="text" id="currency" class="w-full border rounded-md px-3 py-2" value="DH">
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl shadow-sm p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">Informations de l'organisation</h3>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Nom de la fourrière</label>
                                    <input type="text" id="company-name" class="w-full border rounded-md px-3 py-2" value="Fourrière Communale">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Adresse</label>
                                    <textarea id="company-address" class="w-full border rounded-md px-3 py-2" rows="3">Adresse de la fourrière</textarea>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Téléphone</label>
                                    <input type="text" id="company-phone" class="w-full border rounded-md px-3 py-2" value="+212 XXX XXX XXX">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                                    <input type="email" id="company-email" class="w-full border rounded-md px-3 py-2" value="<EMAIL>">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6 bg-white rounded-xl shadow-sm p-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">Liste des déposants</h3>
                        <div class="mb-4">
                            <div class="flex gap-2">
                                <input type="text" id="new-depositor" placeholder="Nom du déposant" class="flex-1 border rounded-md px-3 py-2">
                                <button id="add-depositor" class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600">
                                    <i class="fas fa-plus mr-1"></i> Ajouter
                                </button>
                            </div>
                        </div>
                        <div class="border rounded-md">
                            <div class="bg-gray-50 px-4 py-2 border-b">
                                <h4 class="font-medium text-gray-700">Déposants configurés</h4>
                            </div>
                            <div id="depositors-list" class="divide-y">
                                <!-- Liste des déposants sera générée par JavaScript -->
                            </div>
                        </div>
                    </div>

                    <!-- User Management Section -->
                    <div class="mt-8">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">
                            <i class="fas fa-users mr-2"></i>Gestion des utilisateurs
                        </h3>
                        <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <p class="text-sm text-gray-600">Gérer les utilisateurs et leurs rôles d'accès</p>
                                <button id="add-user-btn" class="btn-primary">
                                    <i class="fas fa-user-plus mr-2"></i>Ajouter un utilisateur
                                </button>
                            </div>
                            <div class="border rounded-md">
                                <div class="bg-gray-50 px-4 py-2 border-b">
                                    <h4 class="font-medium text-gray-700">Utilisateurs du système</h4>
                                </div>
                                <div id="users-list" class="divide-y">
                                    <!-- Liste des utilisateurs sera générée par JavaScript -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6 flex justify-end">
                        <button id="save-settings" class="btn-primary">
                            <i class="fas fa-save mr-2"></i> Sauvegarder les paramètres
                        </button>
                    </div>
                </section>
            </main>

            <!-- Footer -->
            <footer class="bg-white border-t py-4 px-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500">© 2023 Fourrière Communale. Tous droits réservés.</p>
                    </div>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-gray-500">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-gray-500">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-gray-500">
                            <i class="fab fa-instagram"></i>
                        </a>
                    </div>
                </div>
            </footer>
        </div>
    </div>

    <!-- Modals -->
    <div id="addVehicleModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="modal-professional rounded-lg w-full max-w-2xl max-h-screen overflow-y-auto">
            <div class="border-b border-gray-200 px-6 py-4 flex justify-between items-center">
                <h3 class="text-lg font-medium text-blue-600">Nouvelle entrée de véhicule</h3>
                <button id="close-add-vehicle-modal" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="addVehicleForm" class="px-6 py-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">N° de dépôt *</label>
                        <input type="text" name="depot_number" class="w-full border rounded-md px-3 py-2" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Immatriculation *</label>
                        <input type="text" name="license_plate" class="w-full border rounded-md px-3 py-2" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Type de véhicule *</label>
                        <select name="vehicle_type_id" class="w-full border rounded-md px-3 py-2" required>
                            <option value="">Sélectionner...</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Déposant *</label>
                        <select name="depositor" class="w-full border rounded-md px-3 py-2" required>
                            <option value="">Sélectionner...</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Marque</label>
                        <input type="text" name="brand" class="w-full border rounded-md px-3 py-2">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Date d'entrée *</label>
                        <input type="date" name="entry_date" class="w-full border rounded-md px-3 py-2" required>
                    </div>
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Observations</label>
                        <textarea name="observations" rows="3" class="w-full border rounded-md px-3 py-2"></textarea>
                    </div>
                </div>

                <div class="border-t border-gray-200 px-6 py-4 flex justify-end space-x-3 -mx-6 mt-6">
                    <button type="button" id="cancel-add-vehicle" class="btn-outline px-4 py-2 rounded-md">
                        Annuler
                    </button>
                    <button type="submit" class="btn-blue px-4 py-2 rounded-md">
                        Enregistrer
                    </button>
                </div>
            </form>
        </div>
    </div>

    <div id="paymentModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="modal-professional rounded-lg w-full max-w-md">
            <div class="border-b border-gray-200 px-6 py-4 flex justify-between items-center">
                <h3 class="text-lg font-medium text-blue-600">Paiement et sortie de véhicule</h3>
                <button id="close-payment-modal" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="paymentForm" class="px-6 py-4">
                <input type="hidden" name="vehicle_id" id="payment-vehicle-id">
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Véhicule</label>
                        <div id="payment-vehicle-info" class="text-sm text-gray-600 bg-gray-50 p-3 rounded border">
                            <!-- Vehicle info will be populated by JavaScript -->
                        </div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Jours en fourrière</label>
                            <input type="number" id="payment-days" class="w-full border rounded-md px-3 py-2 bg-gray-50" readonly>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Coût par jour</label>
                            <input type="number" id="payment-daily-cost" class="w-full border rounded-md px-3 py-2 bg-gray-50" step="0.01" readonly>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Montant total</label>
                            <input type="number" name="amount" id="payment-amount" class="w-full border rounded-md px-3 py-2 bg-blue-50 font-semibold text-blue-800" step="0.01" readonly>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Date de paiement</label>
                        <input type="date" name="payment_date" class="w-full border rounded-md px-3 py-2" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">N° de quittance</label>
                        <input type="text" name="receipt_number" class="w-full border rounded-md px-3 py-2" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Méthode de paiement</label>
                        <select name="payment_method" class="w-full border rounded-md px-3 py-2" required>
                            <option value="cash">Espèces</option>
                            <option value="check">Chèque</option>
                            <option value="bank_transfer">Virement bancaire</option>
                            <option value="card">Carte bancaire</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">N° d'ordre de sortie</label>
                        <input type="text" name="release_order_number" class="w-full border rounded-md px-3 py-2" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Date de sortie</label>
                        <input type="date" name="release_date" class="w-full border rounded-md px-3 py-2" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Libéré à</label>
                        <input type="text" name="released_to_name" class="w-full border rounded-md px-3 py-2" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Téléphone</label>
                        <input type="text" name="released_to_phone" class="w-full border rounded-md px-3 py-2">
                    </div>
                </div>
                <div class="border-t border-gray-200 px-6 py-4 flex justify-end space-x-3 -mx-6 mt-6">
                    <button type="button" id="cancel-payment" class="btn-outline px-4 py-2 rounded-md">
                        Annuler
                    </button>
                    <button type="submit" class="btn-green px-4 py-2 rounded-md">
                        Valider le paiement
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Edit Vehicle Modal -->
    <div id="editVehicleModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="modal-professional rounded-lg w-full max-w-2xl max-h-screen overflow-y-auto">
            <div class="border-b border-gray-200 px-6 py-4 flex justify-between items-center">
                <h3 class="text-lg font-medium text-blue-600">Modifier le véhicule</h3>
                <button id="close-edit-vehicle-modal" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="editVehicleForm" class="px-6 py-4">
                <input type="hidden" id="edit-vehicle-id" name="vehicle_id">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">N° de dépôt *</label>
                        <input type="text" id="edit-depot-number" name="depot_number" class="w-full border rounded-md px-3 py-2" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Immatriculation *</label>
                        <input type="text" id="edit-license-plate" name="license_plate" class="w-full border rounded-md px-3 py-2" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Type de véhicule *</label>
                        <select id="edit-vehicle-type" name="vehicle_type_id" class="w-full border rounded-md px-3 py-2" required>
                            <option value="">Sélectionner...</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Déposant *</label>
                        <select id="edit-depositor" name="depositor" class="w-full border rounded-md px-3 py-2" required>
                            <option value="">Sélectionner...</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Marque</label>
                        <input type="text" id="edit-brand" name="brand" class="w-full border rounded-md px-3 py-2">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Modèle</label>
                        <input type="text" id="edit-model" name="model" class="w-full border rounded-md px-3 py-2">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Couleur</label>
                        <input type="text" id="edit-color" name="color" class="w-full border rounded-md px-3 py-2">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Année</label>
                        <input type="number" id="edit-year" name="year" class="w-full border rounded-md px-3 py-2" min="1900" max="2030">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Date d'entrée *</label>
                        <input type="date" id="edit-entry-date" name="entry_date" class="w-full border rounded-md px-3 py-2" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Statut</label>
                        <select id="edit-status" name="status" class="w-full border rounded-md px-3 py-2">
                            <option value="impounded">En fourrière</option>
                            <option value="pending_release">En attente de sortie</option>
                            <option value="released">Sorti</option>
                            <option value="overdue">En retard</option>
                        </select>
                    </div>
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Lieu de découverte</label>
                        <input type="text" id="edit-location-found" name="location_found" class="w-full border rounded-md px-3 py-2">
                    </div>
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Raison de la mise en fourrière</label>
                        <input type="text" id="edit-reason-impounded" name="reason_impounded" class="w-full border rounded-md px-3 py-2">
                    </div>
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Observations</label>
                        <textarea id="edit-observations" name="observations" rows="3" class="w-full border rounded-md px-3 py-2"></textarea>
                    </div>
                </div>

                <div class="border-t border-gray-200 px-6 py-4 flex justify-end space-x-3 -mx-6 mt-6">
                    <button type="button" id="cancel-edit-vehicle" class="btn-outline px-4 py-2 rounded-md">
                        Annuler
                    </button>
                    <button type="submit" class="btn-blue px-4 py-2 rounded-md">
                        Mettre à jour
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- View Vehicle Modal -->
    <div id="viewVehicleModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="modal-professional rounded-lg w-full max-w-2xl max-h-screen overflow-y-auto">
            <div class="border-b border-gray-200 px-6 py-4 flex justify-between items-center">
                <h3 class="text-lg font-medium text-blue-600">Détails du véhicule</h3>
                <button id="close-view-vehicle-modal" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="px-6 py-4">
                <div id="vehicle-details-content">
                    <!-- Content will be populated by JavaScript -->
                </div>
                <div class="border-t border-gray-200 px-6 py-4 flex justify-end space-x-3 -mx-6 mt-6">
                    <button id="close-view-vehicle" class="btn-outline px-4 py-2 rounded-md">
                        Fermer
                    </button>
                    <button id="edit-from-view-btn" class="btn-blue px-4 py-2 rounded-md">
                        <i class="fas fa-edit mr-2"></i>Modifier
                    </button>
                    <button id="payment-from-view-btn" class="btn-green px-4 py-2 rounded-md">
                        <i class="fas fa-credit-card mr-2"></i>Paiement
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- User Management Modal -->
    <div id="userModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="modal-professional rounded-lg w-full max-w-md">
            <div class="border-b border-gray-200 px-6 py-4 flex justify-between items-center">
                <h3 id="userModalTitle" class="text-lg font-medium text-blue-600">Ajouter un utilisateur</h3>
                <button id="close-user-modal" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="userForm" class="px-6 py-4">
                <input type="hidden" id="user-id" name="user_id">
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Nom d'utilisateur *</label>
                        <input type="text" id="user-username" name="username" class="w-full border rounded-md px-3 py-2" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Email *</label>
                        <input type="email" id="user-email" name="email" class="w-full border rounded-md px-3 py-2" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Mot de passe *</label>
                        <input type="password" id="user-password" name="password" class="w-full border rounded-md px-3 py-2" required>
                        <p class="text-xs text-gray-500 mt-1">Minimum 6 caractères</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Prénom *</label>
                        <input type="text" id="user-first-name" name="first_name" class="w-full border rounded-md px-3 py-2" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Nom *</label>
                        <input type="text" id="user-last-name" name="last_name" class="w-full border rounded-md px-3 py-2" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Rôle *</label>
                        <select id="user-role" name="role_id" class="w-full border rounded-md px-3 py-2" required>
                            <option value="">Sélectionner un rôle...</option>
                        </select>
                    </div>
                    <div id="user-active-field" class="hidden">
                        <label class="flex items-center">
                            <input type="checkbox" id="user-active" name="is_active" class="mr-2">
                            <span class="text-sm text-gray-700">Compte actif</span>
                        </label>
                    </div>
                </div>
                <div class="border-t border-gray-200 px-6 py-4 flex justify-end space-x-3 -mx-6 mt-6">
                    <button type="button" id="cancel-user" class="btn-outline px-4 py-2 rounded-md">
                        Annuler
                    </button>
                    <button type="submit" id="userSubmitBtn" class="btn-blue px-4 py-2 rounded-md">
                        Ajouter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Debug Script -->
    <script>
        // Debug script to ensure modal functions work
        window.addEventListener('load', function() {
            console.log('Page fully loaded');
            console.log('Modal element:', document.getElementById('addVehicleModal'));
            console.log('openAddVehicleModal function:', typeof window.openAddVehicleModal);

            // Add a backup event listener for all "Nouvelle entrée" buttons
            setTimeout(() => {
                const buttons = document.querySelectorAll('button');
                buttons.forEach(button => {
                    if (button.textContent.includes('Nouvelle entrée')) {
                        console.log('Found "Nouvelle entrée" button:', button);
                        button.addEventListener('click', function(e) {
                            console.log('Button clicked!');
                            e.preventDefault();
                            e.stopPropagation();

                            const modal = document.getElementById('addVehicleModal');
                            if (modal) {
                                console.log('Opening modal...');
                                modal.classList.remove('hidden');

                                // Set default values
                                const dateInput = document.querySelector('input[name="entry_date"]');
                                if (dateInput) {
                                    dateInput.value = new Date().toISOString().split('T')[0];
                                }

                                const timeInput = document.querySelector('input[name="entry_time"]');
                                if (timeInput) {
                                    const now = new Date();
                                    const timeString = now.getHours().toString().padStart(2, '0') + ':' + now.getMinutes().toString().padStart(2, '0');
                                    timeInput.value = timeString;
                                }
                            } else {
                                console.error('Modal not found!');
                            }
                        });
                    }
                });
            }, 1000);
        });
    </script>

    <!-- Load the main application JavaScript -->
    <script src="/js/app.js"></script>
</body>
</html>