const express = require('express');
const router = express.Router();
const { body, validationResult } = require('express-validator');
const db = require('../config/database');
const moment = require('moment');

// Validation middleware
const validateVehicle = [
    body('depot_number').notEmpty().withMessage('Numéro de dépôt requis'),
    body('license_plate').notEmpty().withMessage('Plaque d\'immatriculation requise'),
    body('vehicle_type_id').isInt().withMessage('Type de véhicule invalide'),
    body('depositor').notEmpty().withMessage('Déposant requis'),
    body('entry_date').isDate().withMessage('Date d\'entrée invalide')
];

// GET /api/vehicles - Get all vehicles with filters
router.get('/', async (req, res) => {
    try {
        const { status, search, page = 1, limit = 10 } = req.query;
        const offset = (page - 1) * limit;

        let query = `
            SELECT v.*, vt.name as vehicle_type_name,
                   vo.first_name, vo.last_name, vo.phone,
                   DATEDIFF(CURDATE(), v.entry_date) as days_in_storage,
                   (DATEDIFF(CURDATE(), v.entry_date) * v.storage_cost_per_day) as total_storage_cost,
                   p.receipt_number, p.payment_date, p.amount as total_amount,
                   vr.release_order_number
            FROM vehicles v
            LEFT JOIN vehicle_types vt ON v.vehicle_type_id = vt.id
            LEFT JOIN vehicle_owners vo ON v.owner_id = vo.id
            LEFT JOIN payments p ON v.id = p.vehicle_id
            LEFT JOIN vehicle_releases vr ON v.id = vr.vehicle_id
            WHERE 1=1
        `;

        const params = [];

        if (status) {
            query += ' AND v.status = ?';
            params.push(status);
        }

        if (search) {
            query += ' AND (v.license_plate LIKE ? OR v.depot_number LIKE ? OR v.brand LIKE ?)';
            const searchTerm = `%${search}%`;
            params.push(searchTerm, searchTerm, searchTerm);
        }

        // Add overdue condition for overdue status
        if (status === 'overdue') {
            query += ' AND DATEDIFF(CURDATE(), v.entry_date) > 365';
        }

        query += ' ORDER BY v.entry_date DESC LIMIT ? OFFSET ?';
        params.push(parseInt(limit), parseInt(offset));

        const vehicles = await db.executeQuery(query, params);

        // Get total count for pagination
        let countQuery = `
            SELECT COUNT(*) as total
            FROM vehicles v
            WHERE 1=1
        `;
        const countParams = [];

        if (status) {
            countQuery += ' AND v.status = ?';
            countParams.push(status);
        }

        if (search) {
            countQuery += ' AND (v.license_plate LIKE ? OR v.depot_number LIKE ? OR v.brand LIKE ?)';
            const searchTerm = `%${search}%`;
            countParams.push(searchTerm, searchTerm, searchTerm);
        }

        if (status === 'overdue') {
            countQuery += ' AND DATEDIFF(CURDATE(), v.entry_date) > 365';
        }

        const [countResult] = await db.executeQuery(countQuery, countParams);
        const total = countResult.total;

        res.json({
            vehicles,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                pages: Math.ceil(total / limit)
            }
        });

    } catch (error) {
        console.error('Error fetching vehicles:', error);
        res.status(500).json({ error: 'Erreur lors de la récupération des véhicules' });
    }
});

// GET /api/vehicles/:id - Get single vehicle
router.get('/:id', async (req, res) => {
    try {
        const { id } = req.params;

        const query = `
            SELECT v.*, vt.name as vehicle_type_name,
                   vo.first_name, vo.last_name, vo.phone, vo.email, vo.address,
                   DATEDIFF(CURDATE(), v.entry_date) as days_in_storage,
                   (DATEDIFF(CURDATE(), v.entry_date) * v.storage_cost_per_day) as total_storage_cost
            FROM vehicles v
            LEFT JOIN vehicle_types vt ON v.vehicle_type_id = vt.id
            LEFT JOIN vehicle_owners vo ON v.owner_id = vo.id
            WHERE v.id = ?
        `;

        const [vehicle] = await db.executeQuery(query, [id]);

        if (!vehicle) {
            return res.status(404).json({ error: 'Véhicule non trouvé' });
        }

        // Get vehicle photos
        const photos = await db.executeQuery(
            'SELECT * FROM vehicle_photos WHERE vehicle_id = ?',
            [id]
        );

        // Get payments
        const payments = await db.executeQuery(
            'SELECT * FROM payments WHERE vehicle_id = ? ORDER BY payment_date DESC',
            [id]
        );

        vehicle.photos = photos;
        vehicle.payments = payments;

        res.json(vehicle);

    } catch (error) {
        console.error('Error fetching vehicle:', error);
        res.status(500).json({ error: 'Erreur lors de la récupération du véhicule' });
    }
});

// POST /api/vehicles - Create new vehicle
router.post('/', validateVehicle, async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }

        const {
            depot_number, license_plate, vehicle_type_id, brand, color, year, vin,
            depositor, entry_date, entry_time, location_found, reason_impounded, observations,
            storage_cost_per_day = 25.00, owner_info
        } = req.body;

        // Set default values for optional fields - ensure no undefined values
        const safeBrand = brand !== undefined ? brand : null;
        const safeColor = color !== undefined ? color : null;
        const safeYear = year !== undefined ? year : null;
        const safeVin = vin !== undefined ? vin : null;
        const safeEntryTime = entry_time !== undefined ? entry_time : null;
        const safeLocationFound = location_found !== undefined ? location_found : null;
        const safeReasonImpounded = reason_impounded !== undefined ? reason_impounded : null;
        const safeObservations = observations !== undefined ? observations : null;

        let owner_id = null;

        // Create owner if provided
        if (owner_info && owner_info.first_name && owner_info.last_name) {
            const ownerResult = await db.executeQuery(
                'INSERT INTO vehicle_owners (first_name, last_name, phone, email, address, id_number) VALUES (?, ?, ?, ?, ?, ?)',
                [owner_info.first_name, owner_info.last_name, owner_info.phone, owner_info.email, owner_info.address, owner_info.id_number]
            );
            owner_id = ownerResult.insertId;
        }

        const result = await db.executeQuery(
            `INSERT INTO vehicles (depot_number, license_plate, vehicle_type_id, brand, color, year, vin,
             depositor, entry_date, entry_time, location_found, reason_impounded, observations, storage_cost_per_day, owner_id)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [depot_number, license_plate, vehicle_type_id, safeBrand, safeColor, safeYear, safeVin,
             depositor, entry_date, safeEntryTime, safeLocationFound, safeReasonImpounded, safeObservations, storage_cost_per_day, owner_id]
        );

        res.status(201).json({
            message: 'Véhicule enregistré avec succès',
            vehicle_id: result.insertId
        });

    } catch (error) {
        console.error('Error creating vehicle:', error);
        if (error.code === 'ER_DUP_ENTRY') {
            res.status(400).json({ error: 'Numéro de dépôt déjà existant' });
        } else {
            res.status(500).json({ error: 'Erreur lors de l\'enregistrement du véhicule' });
        }
    }
});

// PUT /api/vehicles/:id - Update vehicle
router.put('/:id', validateVehicle, async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }

        const { id } = req.params;
        const {
            depot_number, license_plate, vehicle_type_id, brand, color, year, vin,
            depositor, entry_date, entry_time, location_found, reason_impounded, observations,
            storage_cost_per_day, status
        } = req.body;

        await db.executeQuery(
            `UPDATE vehicles SET depot_number = ?, license_plate = ?, vehicle_type_id = ?, brand = ?,
             color = ?, year = ?, vin = ?, depositor = ?, entry_date = ?, entry_time = ?, location_found = ?,
             reason_impounded = ?, observations = ?, storage_cost_per_day = ?, status = ?, updated_at = CURRENT_TIMESTAMP
             WHERE id = ?`,
            [depot_number, license_plate, vehicle_type_id, brand, color, year, vin,
             depositor, entry_date, entry_time, location_found, reason_impounded, observations,
             storage_cost_per_day, status, id]
        );

        res.json({ message: 'Véhicule mis à jour avec succès' });

    } catch (error) {
        console.error('Error updating vehicle:', error);
        res.status(500).json({ error: 'Erreur lors de la mise à jour du véhicule' });
    }
});

// DELETE /api/vehicles/:id - Delete vehicle
router.delete('/:id', async (req, res) => {
    try {
        const { id } = req.params;

        await db.executeQuery('DELETE FROM vehicles WHERE id = ?', [id]);

        res.json({ message: 'Véhicule supprimé avec succès' });

    } catch (error) {
        console.error('Error deleting vehicle:', error);
        res.status(500).json({ error: 'Erreur lors de la suppression du véhicule' });
    }
});

// GET /api/vehicles/types - Get vehicle types
router.get('/types/list', async (req, res) => {
    try {
        const types = await db.executeQuery('SELECT * FROM vehicle_types ORDER BY name');
        res.json(types);
    } catch (error) {
        console.error('Error fetching vehicle types:', error);
        res.status(500).json({ error: 'Erreur lors de la récupération des types de véhicules' });
    }
});

// PUT /api/vehicles/:id/status - Update vehicle status
router.put('/:id/status', async (req, res) => {
    try {
        const { id } = req.params;
        const { status } = req.body;

        const validStatuses = ['impounded', 'pending_release', 'released', 'overdue', 'destroyed'];
        if (!validStatuses.includes(status)) {
            return res.status(400).json({ error: 'Statut invalide' });
        }

        await db.executeQuery(
            'UPDATE vehicles SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [status, id]
        );

        res.json({ message: 'Statut mis à jour avec succès' });

    } catch (error) {
        console.error('Error updating vehicle status:', error);
        res.status(500).json({ error: 'Erreur lors de la mise à jour du statut' });
    }
});

// POST /api/vehicles/:id/release - Release vehicle (change status from pending_release to released)
router.post('/:id/release', async (req, res) => {
    try {
        const { id } = req.params;

        console.log('🚪 Processing vehicle release for ID:', id);

        // First, check if vehicle exists and has correct status
        const [vehicle] = await db.executeQuery(
            'SELECT * FROM vehicles WHERE id = ?',
            [id]
        );

        if (!vehicle) {
            return res.status(404).json({ error: 'Véhicule non trouvé' });
        }

        if (vehicle.status !== 'pending_release') {
            return res.status(400).json({
                error: 'Ce véhicule ne peut pas être sorti. Statut actuel: ' + vehicle.status,
                current_status: vehicle.status
            });
        }

        console.log('✅ Vehicle found and ready for release:', {
            id: vehicle.id,
            license_plate: vehicle.license_plate,
            current_status: vehicle.status
        });

        // Update vehicle status to released and set release timestamp
        await db.executeQuery(
            'UPDATE vehicles SET status = "released", updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [id]
        );

        // Update the vehicle_releases table with release timestamp (using existing release_date column)
        await db.executeQuery(
            'UPDATE vehicle_releases SET release_date = CURRENT_TIMESTAMP WHERE vehicle_id = ?',
            [id]
        );

        console.log('✅ Vehicle released successfully');

        res.json({
            message: 'Véhicule sorti avec succès de la fourrière',
            vehicle_id: parseInt(id),
            new_status: 'released',
            release_timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('Error releasing vehicle:', error);
        res.status(500).json({ error: 'Erreur lors de la sortie du véhicule' });
    }
});

module.exports = router;
