{"version": 3, "sources": ["../../../src/dialects/abstract/query-interface.js"], "sourcesContent": ["'use strict';\n\nconst _ = require('lodash');\n\nconst Utils = require('../../utils');\nconst DataTypes = require('../../data-types');\nconst Transaction = require('../../transaction');\nconst QueryTypes = require('../../query-types');\n\n/**\n * The interface that <PERSON><PERSON><PERSON> uses to talk to all databases\n */\nclass QueryInterface {\n  constructor(sequelize, queryGenerator) {\n    this.sequelize = sequelize;\n    this.queryGenerator = queryGenerator;\n  }\n\n  /**\n   * Create a database\n   *\n   * @param {string} database  Database name to create\n   * @param {object} [options] Query options\n   * @param {string} [options.charset] Database default character set, MYSQL only\n   * @param {string} [options.collate] Database default collation\n   * @param {string} [options.encoding] Database default character set, PostgreSQL only\n   * @param {string} [options.ctype] Database character classification, PostgreSQL only\n   * @param {string} [options.template] The name of the template from which to create the new database, PostgreSQL only\n   *\n   * @returns {Promise}\n   */\n  async createDatabase(database, options) {\n    options = options || {};\n    const sql = this.queryGenerator.createDatabaseQuery(database, options);\n    return await this.sequelize.query(sql, options);\n  }\n\n  /**\n   * Drop a database\n   *\n   * @param {string} database  Database name to drop\n   * @param {object} [options] Query options\n   *\n   * @returns {Promise}\n   */\n  async dropDatabase(database, options) {\n    options = options || {};\n    const sql = this.queryGenerator.dropDatabaseQuery(database);\n    return await this.sequelize.query(sql, options);\n  }\n\n  /**\n   * Create a schema\n   *\n   * @param {string} schema    Schema name to create\n   * @param {object} [options] Query options\n   *\n   * @returns {Promise}\n   */\n  async createSchema(schema, options) {\n    options = options || {};\n    const sql = this.queryGenerator.createSchema(schema);\n    return await this.sequelize.query(sql, options);\n  }\n\n  /**\n   * Drop a schema\n   *\n   * @param {string} schema    Schema name to drop\n   * @param {object} [options] Query options\n   *\n   * @returns {Promise}\n   */\n  async dropSchema(schema, options) {\n    options = options || {};\n    const sql = this.queryGenerator.dropSchema(schema);\n    return await this.sequelize.query(sql, options);\n  }\n\n  /**\n   * Drop all schemas\n   *\n   * @param {object} [options] Query options\n   *\n   * @returns {Promise}\n   */\n  async dropAllSchemas(options) {\n    options = options || {};\n\n    if (!this.queryGenerator._dialect.supports.schemas) {\n      return this.sequelize.drop(options);\n    }\n    const schemas = await this.showAllSchemas(options);\n    return Promise.all(schemas.map(schemaName => this.dropSchema(schemaName, options)));\n  }\n\n  /**\n   * Show all schemas\n   *\n   * @param {object} [options] Query options\n   *\n   * @returns {Promise<Array>}\n   */\n  async showAllSchemas(options) {\n    options = {\n      ...options,\n      raw: true,\n      type: this.sequelize.QueryTypes.SELECT\n    };\n\n    const showSchemasSql = this.queryGenerator.showSchemasQuery(options);\n\n    const schemaNames = await this.sequelize.query(showSchemasSql, options);\n\n    return _.flatten(schemaNames.map(value => value.schema_name ? value.schema_name : value));\n  }\n\n  /**\n   * Return database version\n   *\n   * @param {object}    [options]      Query options\n   * @param {QueryType} [options.type] Query type\n   *\n   * @returns {Promise}\n   * @private\n   */\n  async databaseVersion(options) {\n    return await this.sequelize.query(\n      this.queryGenerator.versionQuery(),\n      { ...options, type: QueryTypes.VERSION }\n    );\n  }\n\n  /**\n   * Create a table with given set of attributes\n   *\n   * ```js\n   * queryInterface.createTable(\n   *   'nameOfTheNewTable',\n   *   {\n   *     id: {\n   *       type: Sequelize.INTEGER,\n   *       primaryKey: true,\n   *       autoIncrement: true\n   *     },\n   *     createdAt: {\n   *       type: Sequelize.DATE\n   *     },\n   *     updatedAt: {\n   *       type: Sequelize.DATE\n   *     },\n   *     attr1: Sequelize.STRING,\n   *     attr2: Sequelize.INTEGER,\n   *     attr3: {\n   *       type: Sequelize.BOOLEAN,\n   *       defaultValue: false,\n   *       allowNull: false\n   *     },\n   *     //foreign key usage\n   *     attr4: {\n   *       type: Sequelize.INTEGER,\n   *       references: {\n   *         model: 'another_table_name',\n   *         key: 'id'\n   *       },\n   *       onUpdate: 'cascade',\n   *       onDelete: 'cascade'\n   *     }\n   *   },\n   *   {\n   *     engine: 'MYISAM',    // default: 'InnoDB'\n   *     charset: 'latin1',   // default: null\n   *     schema: 'public',    // default: public, PostgreSQL only.\n   *     comment: 'my table', // comment for table\n   *     collate: 'latin1_danish_ci' // collation, MYSQL only\n   *   }\n   * )\n   * ```\n   *\n   * @param {string} tableName  Name of table to create\n   * @param {object} attributes Object representing a list of table attributes to create\n   * @param {object} [options] create table and query options\n   * @param {Model}  [model] model class\n   *\n   * @returns {Promise}\n   */\n  async createTable(tableName, attributes, options, model) {\n    let sql = '';\n\n    options = { ...options };\n\n    if (options && options.uniqueKeys) {\n      _.forOwn(options.uniqueKeys, uniqueKey => {\n        if (uniqueKey.customIndex === undefined) {\n          uniqueKey.customIndex = true;\n        }\n      });\n    }\n\n    if (model) {\n      options.uniqueKeys = options.uniqueKeys || model.uniqueKeys;\n    }\n\n    attributes = _.mapValues(\n      attributes,\n      attribute => this.sequelize.normalizeAttribute(attribute)\n    );\n\n    // Postgres requires special SQL commands for ENUM/ENUM[]\n    await this.ensureEnums(tableName, attributes, options, model);\n\n    if (\n      !tableName.schema &&\n      (options.schema || !!model && model._schema)\n    ) {\n      tableName = this.queryGenerator.addSchema({\n        tableName,\n        _schema: !!model && model._schema || options.schema\n      });\n    }\n\n    attributes = this.queryGenerator.attributesToSQL(attributes, {\n      table: tableName,\n      context: 'createTable',\n      withoutForeignKeyConstraints: options.withoutForeignKeyConstraints\n    });\n    sql = this.queryGenerator.createTableQuery(tableName, attributes, options);\n\n    return await this.sequelize.query(sql, options);\n  }\n\n  /**\n   * Returns a promise that will resolve to true if the table exists in the database, false otherwise.\n   *\n   * @param {TableName} tableName - The name of the table\n   * @param {QueryOptions} options - Query options\n   * @returns {Promise<boolean>}\n   */\n  async tableExists(tableName, options) {\n    const sql = this.queryGenerator.tableExistsQuery(tableName);\n\n    const out = await this.sequelize.query(sql, {\n      ...options,\n      type: QueryTypes.SHOWTABLES\n    });\n\n    return out.length === 1;\n  }\n\n  /**\n   * Drop a table from database\n   *\n   * @param {string} tableName Table name to drop\n   * @param {object} options   Query options\n   *\n   * @returns {Promise}\n   */\n  async dropTable(tableName, options) {\n    // if we're forcing we should be cascading unless explicitly stated otherwise\n    options = { ...options };\n    options.cascade = options.cascade || options.force || false;\n\n    const sql = this.queryGenerator.dropTableQuery(tableName, options);\n\n    await this.sequelize.query(sql, options);\n  }\n\n  async _dropAllTables(tableNames, skip, options) {\n    for (const tableName of tableNames) {\n      // if tableName is not in the Array of tables names then don't drop it\n      if (!skip.includes(tableName.tableName || tableName)) {\n        await this.dropTable(tableName, { ...options, cascade: true } );\n      }\n    }\n  }\n\n  /**\n   * Drop all tables from database\n   *\n   * @param {object} [options] query options\n   * @param {Array}  [options.skip] List of table to skip\n   *\n   * @returns {Promise}\n   */\n  async dropAllTables(options) {\n    options = options || {};\n    const skip = options.skip || [];\n\n    const tableNames = await this.showAllTables(options);\n    const foreignKeys = await this.getForeignKeysForTables(tableNames, options);\n\n    for (const tableName of tableNames) {\n      let normalizedTableName = tableName;\n      if (_.isObject(tableName)) {\n        normalizedTableName = `${tableName.schema}.${tableName.tableName}`;\n      }\n\n      for (const foreignKey of foreignKeys[normalizedTableName]) {\n        await this.sequelize.query(this.queryGenerator.dropForeignKeyQuery(tableName, foreignKey));\n      }\n    }\n    await this._dropAllTables(tableNames, skip, options);\n  }\n\n  /**\n   * Rename a table\n   *\n   * @param {string} before    Current name of table\n   * @param {string} after     New name from table\n   * @param {object} [options] Query options\n   *\n   * @returns {Promise}\n   */\n  async renameTable(before, after, options) {\n    options = options || {};\n    const sql = this.queryGenerator.renameTableQuery(before, after);\n    return await this.sequelize.query(sql, options);\n  }\n\n  /**\n   * Get all tables in current database\n   *\n   * @param {object}    [options] Query options\n   * @param {boolean}   [options.raw=true] Run query in raw mode\n   * @param {QueryType} [options.type=QueryType.SHOWTABLE] query type\n   *\n   * @returns {Promise<Array>}\n   * @private\n   */\n  async showAllTables(options) {\n    options = {\n      ...options,\n      raw: true,\n      type: QueryTypes.SHOWTABLES\n    };\n\n    const showTablesSql = this.queryGenerator.showTablesQuery(this.sequelize.config.database);\n    const tableNames = await this.sequelize.query(showTablesSql, options);\n    return _.flatten(tableNames);\n  }\n\n  /**\n   * Describe a table structure\n   *\n   * This method returns an array of hashes containing information about all attributes in the table.\n   *\n   * ```js\n   * {\n   *    name: {\n   *      type:         'VARCHAR(255)', // this will be 'CHARACTER VARYING' for pg!\n   *      allowNull:    true,\n   *      defaultValue: null\n   *    },\n   *    isBetaMember: {\n   *      type:         'TINYINT(1)', // this will be 'BOOLEAN' for pg!\n   *      allowNull:    false,\n   *      defaultValue: false\n   *    }\n   * }\n   * ```\n   *\n   * @param {string} tableName table name\n   * @param {object} [options] Query options\n   *\n   * @returns {Promise<object>}\n   */\n  async describeTable(tableName, options) {\n    let schema = null;\n    let schemaDelimiter = null;\n\n    if (typeof options === 'string') {\n      schema = options;\n    } else if (typeof options === 'object' && options !== null) {\n      schema = options.schema || null;\n      schemaDelimiter = options.schemaDelimiter || null;\n    }\n\n    if (typeof tableName === 'object' && tableName !== null) {\n      schema = tableName.schema;\n      tableName = tableName.tableName;\n    }\n\n    const sql = this.queryGenerator.describeTableQuery(tableName, schema, schemaDelimiter);\n    options = { ...options, type: QueryTypes.DESCRIBE };\n\n    try {\n      const data = await this.sequelize.query(sql, options);\n      /*\n       * If no data is returned from the query, then the table name may be wrong.\n       * Query generators that use information_schema for retrieving table info will just return an empty result set,\n       * it will not throw an error like built-ins do (e.g. DESCRIBE on MySql).\n       */\n      if (_.isEmpty(data)) {\n        throw new Error(`No description found for \"${tableName}\" table. Check the table name and schema; remember, they _are_ case sensitive.`);\n      }\n\n      return data;\n    } catch (e) {\n      if (e.original && e.original.code === 'ER_NO_SUCH_TABLE') {\n        throw new Error(`No description found for \"${tableName}\" table. Check the table name and schema; remember, they _are_ case sensitive.`);\n      }\n\n      throw e;\n    }\n  }\n\n  /**\n   * Add a new column to a table\n   *\n   * ```js\n   * queryInterface.addColumn('tableA', 'columnC', Sequelize.STRING, {\n   *    after: 'columnB' // after option is only supported by MySQL\n   * });\n   * ```\n   *\n   * @param {string} table     Table to add column to\n   * @param {string} key       Column name\n   * @param {object} attribute Attribute definition\n   * @param {object} [options] Query options\n   *\n   * @returns {Promise}\n   */\n  async addColumn(table, key, attribute, options) {\n    if (!table || !key || !attribute) {\n      throw new Error('addColumn takes at least 3 arguments (table, attribute name, attribute definition)');\n    }\n\n    options = options || {};\n    attribute = this.sequelize.normalizeAttribute(attribute);\n    return await this.sequelize.query(this.queryGenerator.addColumnQuery(table, key, attribute), options);\n  }\n\n  /**\n   * Remove a column from a table\n   *\n   * @param {string} tableName      Table to remove column from\n   * @param {string} attributeName  Column name to remove\n   * @param {object} [options]      Query options\n   */\n  async removeColumn(tableName, attributeName, options) {\n    return this.sequelize.query(this.queryGenerator.removeColumnQuery(tableName, attributeName), options);\n  }\n\n  normalizeAttribute(dataTypeOrOptions) {\n    let attribute;\n    if (Object.values(DataTypes).includes(dataTypeOrOptions)) {\n      attribute = { type: dataTypeOrOptions, allowNull: true };\n    } else {\n      attribute = dataTypeOrOptions;\n    }\n\n    return this.sequelize.normalizeAttribute(attribute);\n  }\n\n  /**\n   * Split a list of identifiers by \".\" and quote each part\n   *\n   * @param {string} identifier\n   * @param {boolean} force\n   *\n   * @returns {string}\n   */\n  quoteIdentifier(identifier, force) {\n    return this.queryGenerator.quoteIdentifier(identifier, force);\n  }\n\n  /**\n   * Split a list of identifiers by \".\" and quote each part.\n   *\n   * @param {string} identifiers \n   * \n   * @returns {string}\n   */\n  quoteIdentifiers(identifiers) {\n    return this.queryGenerator.quoteIdentifiers(identifiers);\n  }\n\n  /**\n   * Change a column definition\n   *\n   * @param {string} tableName          Table name to change from\n   * @param {string} attributeName      Column name\n   * @param {object} dataTypeOrOptions  Attribute definition for new column\n   * @param {object} [options]          Query options\n   */\n  async changeColumn(tableName, attributeName, dataTypeOrOptions, options) {\n    options = options || {};\n\n    const query = this.queryGenerator.attributesToSQL({\n      [attributeName]: this.normalizeAttribute(dataTypeOrOptions)\n    }, {\n      context: 'changeColumn',\n      table: tableName\n    });\n    const sql = this.queryGenerator.changeColumnQuery(tableName, query);\n\n    return this.sequelize.query(sql, options);\n  }\n\n  /**\n   * Rejects if the table doesn't have the specified column, otherwise returns the column description.\n   *\n   * @param {string} tableName\n   * @param {string} columnName\n   * @param {object} options\n   * @private\n   */\n  async assertTableHasColumn(tableName, columnName, options) {\n    const description = await this.describeTable(tableName, options);\n    if (description[columnName]) {\n      return description;\n    }\n    throw new Error(`Table ${tableName} doesn't have the column ${columnName}`);\n  }\n\n  /**\n   * Rename a column\n   *\n   * @param {string} tableName        Table name whose column to rename\n   * @param {string} attrNameBefore   Current column name\n   * @param {string} attrNameAfter    New column name\n   * @param {object} [options]        Query option\n   *\n   * @returns {Promise}\n   */\n  async renameColumn(tableName, attrNameBefore, attrNameAfter, options) {\n    options = options || {};\n    const data = (await this.assertTableHasColumn(tableName, attrNameBefore, options))[attrNameBefore];\n\n    const _options = {};\n\n    _options[attrNameAfter] = {\n      attribute: attrNameAfter,\n      type: data.type,\n      allowNull: data.allowNull,\n      defaultValue: data.defaultValue\n    };\n\n    // fix: a not-null column cannot have null as default value\n    if (data.defaultValue === null && !data.allowNull) {\n      delete _options[attrNameAfter].defaultValue;\n    }\n\n    const sql = this.queryGenerator.renameColumnQuery(\n      tableName,\n      attrNameBefore,\n      this.queryGenerator.attributesToSQL(_options)\n    );\n    return await this.sequelize.query(sql, options);\n  }\n\n  /**\n   * Add an index to a column\n   *\n   * @param {string|object}  tableName Table name to add index on, can be a object with schema\n   * @param {Array}   [attributes]     Use options.fields instead, List of attributes to add index on\n   * @param {object}  options          indexes options\n   * @param {Array}   options.fields   List of attributes to add index on\n   * @param {boolean} [options.concurrently] Pass CONCURRENT so other operations run while the index is created\n   * @param {boolean} [options.unique] Create a unique index\n   * @param {string}  [options.using]  Useful for GIN indexes\n   * @param {string}  [options.operator] Index operator\n   * @param {string}  [options.type]   Type of index, available options are UNIQUE|FULLTEXT|SPATIAL\n   * @param {string}  [options.name]   Name of the index. Default is <table>_<attr1>_<attr2>\n   * @param {object}  [options.where]  Where condition on index, for partial indexes\n   * @param {string}  [rawTablename]   table name, this is just for backward compatibiity\n   *\n   * @returns {Promise}\n   */\n  async addIndex(tableName, attributes, options, rawTablename) {\n    // Support for passing tableName, attributes, options or tableName, options (with a fields param which is the attributes)\n    if (!Array.isArray(attributes)) {\n      rawTablename = options;\n      options = attributes;\n      attributes = options.fields;\n    }\n\n    if (!rawTablename) {\n      // Map for backwards compat\n      rawTablename = tableName;\n    }\n\n    options = Utils.cloneDeep(options);\n    options.fields = attributes;\n    const sql = this.queryGenerator.addIndexQuery(tableName, options, rawTablename);\n    return await this.sequelize.query(sql, { ...options, supportsSearchPath: false });\n  }\n\n  /**\n   * Show indexes on a table\n   *\n   * @param {string} tableName table name\n   * @param {object} [options]   Query options\n   *\n   * @returns {Promise<Array>}\n   * @private\n   */\n  async showIndex(tableName, options) {\n    const sql = this.queryGenerator.showIndexesQuery(tableName, options);\n    return await this.sequelize.query(sql, { ...options, type: QueryTypes.SHOWINDEXES });\n  }\n\n\n  /**\n   * Returns all foreign key constraints of requested tables\n   *\n   * @param {string[]} tableNames table names\n   * @param {object} [options] Query options\n   *\n   * @returns {Promise}\n   */\n  async getForeignKeysForTables(tableNames, options) {\n    if (tableNames.length === 0) {\n      return {};\n    }\n\n    options = { ...options, type: QueryTypes.FOREIGNKEYS };\n\n    const results = await Promise.all(tableNames.map(tableName =>\n      this.sequelize.query(this.queryGenerator.getForeignKeysQuery(tableName, this.sequelize.config.database), options)));\n\n    const result = {};\n\n    tableNames.forEach((tableName, i) => {\n      if (_.isObject(tableName)) {\n        tableName = `${tableName.schema}.${tableName.tableName}`;\n      }\n\n      result[tableName] = Array.isArray(results[i])\n        ? results[i].map(r => r.constraint_name)\n        : [results[i] && results[i].constraint_name];\n\n      result[tableName] = result[tableName].filter(_.identity);\n    });\n\n    return result;\n  }\n\n  /**\n   * Get foreign key references details for the table\n   *\n   * Those details contains constraintSchema, constraintName, constraintCatalog\n   * tableCatalog, tableSchema, tableName, columnName,\n   * referencedTableCatalog, referencedTableCatalog, referencedTableSchema, referencedTableName, referencedColumnName.\n   * Remind: constraint informations won't return if it's sqlite.\n   *\n   * @param {string} tableName table name\n   * @param {object} [options]  Query options\n   */\n  async getForeignKeyReferencesForTable(tableName, options) {\n    const queryOptions = {\n      ...options,\n      type: QueryTypes.FOREIGNKEYS\n    };\n    const query = this.queryGenerator.getForeignKeysQuery(tableName, this.sequelize.config.database);\n    return this.sequelize.query(query, queryOptions);\n  }\n\n  /**\n   * Remove an already existing index from a table\n   *\n   * @param {string} tableName                    Table name to drop index from\n   * @param {string|string[]} indexNameOrAttributes  Index name or list of attributes that in the index\n   * @param {object} [options]                    Query options\n   * @param {boolean} [options.concurrently]      Pass CONCURRENTLY so other operations run while the index is created\n   *\n   * @returns {Promise}\n   */\n  async removeIndex(tableName, indexNameOrAttributes, options) {\n    options = options || {};\n    const sql = this.queryGenerator.removeIndexQuery(tableName, indexNameOrAttributes, options);\n    return await this.sequelize.query(sql, options);\n  }\n\n  /**\n   * Add a constraint to a table\n   *\n   * Available constraints:\n   * - UNIQUE\n   * - DEFAULT (MSSQL only)\n   * - CHECK (MySQL - Ignored by the database engine )\n   * - FOREIGN KEY\n   * - PRIMARY KEY\n   *\n   * @example <caption>UNIQUE</caption>\n   * queryInterface.addConstraint('Users', {\n   *   fields: ['email'],\n   *   type: 'unique',\n   *   name: 'custom_unique_constraint_name'\n   * });\n   *\n   * @example <caption>CHECK</caption>\n   * queryInterface.addConstraint('Users', {\n   *   fields: ['roles'],\n   *   type: 'check',\n   *   where: {\n   *      roles: ['user', 'admin', 'moderator', 'guest']\n   *   }\n   * });\n   *\n   * @example <caption>Default - MSSQL only</caption>\n   * queryInterface.addConstraint('Users', {\n   *    fields: ['roles'],\n   *    type: 'default',\n   *    defaultValue: 'guest'\n   * });\n   *\n   * @example <caption>Primary Key</caption>\n   * queryInterface.addConstraint('Users', {\n   *    fields: ['username'],\n   *    type: 'primary key',\n   *    name: 'custom_primary_constraint_name'\n   * });\n   *\n   * @example <caption>Foreign Key</caption>\n   * queryInterface.addConstraint('Posts', {\n   *   fields: ['username'],\n   *   type: 'foreign key',\n   *   name: 'custom_fkey_constraint_name',\n   *   references: { //Required field\n   *     table: 'target_table_name',\n   *     field: 'target_column_name'\n   *   },\n   *   onDelete: 'cascade',\n   *   onUpdate: 'cascade'\n   * });\n   *\n   * @example <caption>Composite Foreign Key</caption>\n   * queryInterface.addConstraint('TableName', {\n   *   fields: ['source_column_name', 'other_source_column_name'],\n   *   type: 'foreign key',\n   *   name: 'custom_fkey_constraint_name',\n   *   references: { //Required field\n   *     table: 'target_table_name',\n   *     fields: ['target_column_name', 'other_target_column_name']\n   *   },\n   *   onDelete: 'cascade',\n   *   onUpdate: 'cascade'\n   * });\n   *\n   * @param {string} tableName                   Table name where you want to add a constraint\n   * @param {object} options                     An object to define the constraint name, type etc\n   * @param {string} options.type                Type of constraint. One of the values in available constraints(case insensitive)\n   * @param {Array}  options.fields              Array of column names to apply the constraint over\n   * @param {string} [options.name]              Name of the constraint. If not specified, sequelize automatically creates a named constraint based on constraint type, table & column names\n   * @param {string} [options.defaultValue]      The value for the default constraint\n   * @param {object} [options.where]             Where clause/expression for the CHECK constraint\n   * @param {object} [options.references]        Object specifying target table, column name to create foreign key constraint\n   * @param {string} [options.references.table]  Target table name\n   * @param {string} [options.references.field]  Target column name\n   * @param {string} [options.references.fields] Target column names for a composite primary key. Must match the order of fields in options.fields.\n   * @param {string} [options.deferrable]        Sets the constraint to be deferred or immediately checked. See Sequelize.Deferrable. PostgreSQL Only\n   *\n   * @returns {Promise}\n   */\n  async addConstraint(tableName, options) {\n    if (!options.fields) {\n      throw new Error('Fields must be specified through options.fields');\n    }\n\n    if (!options.type) {\n      throw new Error('Constraint type must be specified through options.type');\n    }\n\n    options = Utils.cloneDeep(options);\n\n    const sql = this.queryGenerator.addConstraintQuery(tableName, options);\n    return await this.sequelize.query(sql, options);\n  }\n\n  async showConstraint(tableName, constraintName, options) {\n    const sql = this.queryGenerator.showConstraintsQuery(tableName, constraintName);\n    return await this.sequelize.query(sql, { ...options, type: QueryTypes.SHOWCONSTRAINTS });\n  }\n\n  /**\n   * Remove a constraint from a table\n   *\n   * @param {string} tableName       Table name to drop constraint from\n   * @param {string} constraintName  Constraint name\n   * @param {object} options         Query options\n   */\n  async removeConstraint(tableName, constraintName, options) {\n    return this.sequelize.query(this.queryGenerator.removeConstraintQuery(tableName, constraintName), options);\n  }\n\n  async insert(instance, tableName, values, options) {\n    options = Utils.cloneDeep(options);\n    options.hasTrigger = instance && instance.constructor.options.hasTrigger;\n    const sql = this.queryGenerator.insertQuery(tableName, values, instance && instance.constructor.rawAttributes, options);\n\n    options.type = QueryTypes.INSERT;\n    options.instance = instance;\n\n    const results = await this.sequelize.query(sql, options);\n    if (instance) results[0].isNewRecord = false;\n\n    return results;\n  }\n\n  /**\n   * Upsert\n   *\n   * @param {string} tableName    table to upsert on\n   * @param {object} insertValues values to be inserted, mapped to field name\n   * @param {object} updateValues values to be updated, mapped to field name\n   * @param {object} where        where conditions, which can be used for UPDATE part when INSERT fails\n   * @param {object} options      query options\n   *\n   * @returns {Promise<boolean,?number>} Resolves an array with <created, primaryKey>\n   */\n  async upsert(tableName, insertValues, updateValues, where, options) {\n    options = { ...options };\n\n    const model = options.model;\n\n    options.type = QueryTypes.UPSERT;\n    options.updateOnDuplicate = Object.keys(updateValues);\n    options.upsertKeys = options.conflictFields || [];\n\n    if (options.upsertKeys.length === 0) {\n      const primaryKeys = Object.values(model.primaryKeys).map(item => item.field);\n      const uniqueKeys = Object.values(model.uniqueKeys).filter(c => c.fields.length > 0).map(c => c.fields);\n      const indexKeys = Object.values(model._indexes).filter(c => c.unique && c.fields.length > 0).map(c => c.fields);\n      // For fields in updateValues, try to find a constraint or unique index\n      // that includes given field. Only first matching upsert key is used.\n      for (const field of options.updateOnDuplicate) {\n        const uniqueKey = uniqueKeys.find(fields => fields.includes(field));\n        if (uniqueKey) {\n          options.upsertKeys = uniqueKey;\n          break;\n        }\n\n        const indexKey = indexKeys.find(fields => fields.includes(field));\n        if (indexKey) {\n          options.upsertKeys = indexKey;\n          break;\n        }\n      }\n\n      // Always use PK, if no constraint available OR update data contains PK\n      if (\n        options.upsertKeys.length === 0\n        || _.intersection(options.updateOnDuplicate, primaryKeys).length\n      ) {\n        options.upsertKeys = primaryKeys;\n      }\n\n      options.upsertKeys = _.uniq(options.upsertKeys);\n    }\n\n    const sql = this.queryGenerator.insertQuery(tableName, insertValues, model.rawAttributes, options);\n    return await this.sequelize.query(sql, options);\n  }\n\n  /**\n   * Insert multiple records into a table\n   *\n   * @example\n   * queryInterface.bulkInsert('roles', [{\n   *    label: 'user',\n   *    createdAt: new Date(),\n   *    updatedAt: new Date()\n   *  }, {\n   *    label: 'admin',\n   *    createdAt: new Date(),\n   *    updatedAt: new Date()\n   *  }]);\n   *\n   * @param {string} tableName   Table name to insert record to\n   * @param {Array}  records     List of records to insert\n   * @param {object} options     Various options, please see Model.bulkCreate options\n   * @param {object} attributes  Various attributes mapped by field name\n   *\n   * @returns {Promise}\n   */\n  async bulkInsert(tableName, records, options, attributes) {\n    options = { ...options };\n    options.type = QueryTypes.INSERT;\n\n    const results = await this.sequelize.query(\n      this.queryGenerator.bulkInsertQuery(tableName, records, options, attributes),\n      options\n    );\n\n    return results[0];\n  }\n\n  async update(instance, tableName, values, identifier, options) {\n    options = { ...options };\n    options.hasTrigger = instance && instance.constructor.options.hasTrigger;\n\n    const sql = this.queryGenerator.updateQuery(tableName, values, identifier, options, instance.constructor.rawAttributes);\n\n    options.type = QueryTypes.UPDATE;\n\n    options.instance = instance;\n    return await this.sequelize.query(sql, options);\n  }\n\n  /**\n   * Update multiple records of a table\n   *\n   * @example\n   * queryInterface.bulkUpdate('roles', {\n   *     label: 'admin',\n   *   }, {\n   *     userType: 3,\n   *   },\n   * );\n   *\n   * @param {string} tableName     Table name to update\n   * @param {object} values        Values to be inserted, mapped to field name\n   * @param {object} identifier    A hash with conditions OR an ID as integer OR a string with conditions\n   * @param {object} [options]     Various options, please see Model.bulkCreate options\n   * @param {object} [attributes]  Attributes on return objects if supported by SQL dialect\n   *\n   * @returns {Promise}\n   */\n  async bulkUpdate(tableName, values, identifier, options, attributes) {\n    options = Utils.cloneDeep(options);\n    if (typeof identifier === 'object') identifier = Utils.cloneDeep(identifier);\n\n    const sql = this.queryGenerator.updateQuery(tableName, values, identifier, options, attributes);\n    const table = _.isObject(tableName) ? tableName : { tableName };\n    const model = options.model ? options.model : _.find(this.sequelize.modelManager.models, { tableName: table.tableName });\n\n    options.type = QueryTypes.BULKUPDATE;\n    options.model = model;\n    return await this.sequelize.query(sql, options);\n  }\n\n  async delete(instance, tableName, identifier, options) {\n    const cascades = [];\n    const sql = this.queryGenerator.deleteQuery(tableName, identifier, {}, instance.constructor);\n\n    options = { ...options };\n\n    // Check for a restrict field\n    if (!!instance.constructor && !!instance.constructor.associations) {\n      const keys = Object.keys(instance.constructor.associations);\n      const length = keys.length;\n      let association;\n\n      for (let i = 0; i < length; i++) {\n        association = instance.constructor.associations[keys[i]];\n        if (association.options && association.options.onDelete &&\n          association.options.onDelete.toLowerCase() === 'cascade' &&\n          association.options.useHooks === true) {\n          cascades.push(association.accessors.get);\n        }\n      }\n    }\n\n    for (const cascade of cascades) {\n      let instances = await instance[cascade](options);\n      // Check for hasOne relationship with non-existing associate (\"has zero\")\n      if (!instances) continue;\n      if (!Array.isArray(instances)) instances = [instances];\n      for (const _instance of instances) await _instance.destroy(options);\n    }\n    options.instance = instance;\n    return await this.sequelize.query(sql, options);\n  }\n\n  /**\n   * Delete multiple records from a table\n   *\n   * @param {string}  tableName            table name from where to delete records\n   * @param {object}  where                where conditions to find records to delete\n   * @param {object}  [options]            options\n   * @param {boolean} [options.truncate]   Use truncate table command\n   * @param {boolean} [options.cascade=false]         Only used in conjunction with TRUNCATE. Truncates  all tables that have foreign-key references to the named table, or to any tables added to the group due to CASCADE.\n   * @param {boolean} [options.restartIdentity=false] Only used in conjunction with TRUNCATE. Automatically restart sequences owned by columns of the truncated table.\n   * @param {Model}   [model]              Model\n   *\n   * @returns {Promise}\n   */\n  async bulkDelete(tableName, where, options, model) {\n    options = Utils.cloneDeep(options);\n    options = _.defaults(options, { limit: null });\n\n    if (options.truncate === true) {\n      return this.sequelize.query(\n        this.queryGenerator.truncateTableQuery(tableName, options),\n        options\n      );\n    }\n\n    if (typeof identifier === 'object') where = Utils.cloneDeep(where);\n\n    return await this.sequelize.query(\n      this.queryGenerator.deleteQuery(tableName, where, options, model),\n      options\n    );\n  }\n\n  async select(model, tableName, optionsArg) {\n    const options = { ...optionsArg, type: QueryTypes.SELECT, model };\n\n    return await this.sequelize.query(\n      this.queryGenerator.selectQuery(tableName, options, model),\n      options\n    );\n  }\n\n  async increment(model, tableName, where, incrementAmountsByField, extraAttributesToBeUpdated, options) {\n    options = Utils.cloneDeep(options);\n\n    const sql = this.queryGenerator.arithmeticQuery('+', tableName, where, incrementAmountsByField, extraAttributesToBeUpdated, options);\n\n    options.type = QueryTypes.UPDATE;\n    options.model = model;\n\n    return await this.sequelize.query(sql, options);\n  }\n\n  async decrement(model, tableName, where, incrementAmountsByField, extraAttributesToBeUpdated, options) {\n    options = Utils.cloneDeep(options);\n\n    const sql = this.queryGenerator.arithmeticQuery('-', tableName, where, incrementAmountsByField, extraAttributesToBeUpdated, options);\n\n    options.type = QueryTypes.UPDATE;\n    options.model = model;\n\n    return await this.sequelize.query(sql, options);\n  }\n\n  async rawSelect(tableName, options, attributeSelector, Model) {\n    options = Utils.cloneDeep(options);\n    options = _.defaults(options, {\n      raw: true,\n      plain: true,\n      type: QueryTypes.SELECT\n    });\n\n    const sql = this.queryGenerator.selectQuery(tableName, options, Model);\n\n    if (attributeSelector === undefined) {\n      throw new Error('Please pass an attribute selector!');\n    }\n\n    const data = await this.sequelize.query(sql, options);\n    if (!options.plain) {\n      return data;\n    }\n\n    const result = data ? data[attributeSelector] : null;\n\n    if (!options || !options.dataType) {\n      return result;\n    }\n\n    const dataType = options.dataType;\n\n    if (dataType instanceof DataTypes.DECIMAL || dataType instanceof DataTypes.FLOAT) {\n      if (result !== null) {\n        return parseFloat(result);\n      }\n    }\n    if (dataType instanceof DataTypes.INTEGER || dataType instanceof DataTypes.BIGINT) {\n      if (result !== null) {\n        return parseInt(result, 10);\n      }\n    }\n    if (dataType instanceof DataTypes.DATE) {\n      if (result !== null && !(result instanceof Date)) {\n        return new Date(result);\n      }\n    }\n    return result;\n  }\n\n  async createTrigger(\n    tableName,\n    triggerName,\n    timingType,\n    fireOnArray,\n    functionName,\n    functionParams,\n    optionsArray,\n    options\n  ) {\n    const sql = this.queryGenerator.createTrigger(tableName, triggerName, timingType, fireOnArray, functionName, functionParams, optionsArray);\n    options = options || {};\n    if (sql) {\n      return await this.sequelize.query(sql, options);\n    }\n  }\n\n  async dropTrigger(tableName, triggerName, options) {\n    const sql = this.queryGenerator.dropTrigger(tableName, triggerName);\n    options = options || {};\n\n    if (sql) {\n      return await this.sequelize.query(sql, options);\n    }\n  }\n\n  async renameTrigger(tableName, oldTriggerName, newTriggerName, options) {\n    const sql = this.queryGenerator.renameTrigger(tableName, oldTriggerName, newTriggerName);\n    options = options || {};\n\n    if (sql) {\n      return await this.sequelize.query(sql, options);\n    }\n  }\n\n  /**\n   * Create an SQL function\n   *\n   * @example\n   * queryInterface.createFunction(\n   *   'someFunction',\n   *   [\n   *     {type: 'integer', name: 'param', direction: 'IN'}\n   *   ],\n   *   'integer',\n   *   'plpgsql',\n   *   'RETURN param + 1;',\n   *   [\n   *     'IMMUTABLE',\n   *     'LEAKPROOF'\n   *   ],\n   *   {\n   *    variables:\n   *      [\n   *        {type: 'integer', name: 'myVar', default: 100}\n   *      ],\n   *      force: true\n   *   };\n   * );\n   *\n   * @param {string}  functionName  Name of SQL function to create\n   * @param {Array}   params        List of parameters declared for SQL function\n   * @param {string}  returnType    SQL type of function returned value\n   * @param {string}  language      The name of the language that the function is implemented in\n   * @param {string}  body          Source code of function\n   * @param {Array}   optionsArray  Extra-options for creation\n   * @param {object}  [options]     query options\n   * @param {boolean} options.force If force is true, any existing functions with the same parameters will be replaced. For postgres, this means using `CREATE OR REPLACE FUNCTION` instead of `CREATE FUNCTION`. Default is false\n   * @param {Array<object>}   options.variables List of declared variables. Each variable should be an object with string fields `type` and `name`, and optionally having a `default` field as well.\n   *\n   * @returns {Promise}\n   */\n  async createFunction(functionName, params, returnType, language, body, optionsArray, options) {\n    const sql = this.queryGenerator.createFunction(functionName, params, returnType, language, body, optionsArray, options);\n    options = options || {};\n\n    if (sql) {\n      return await this.sequelize.query(sql, options);\n    }\n  }\n\n  /**\n   * Drop an SQL function\n   *\n   * @example\n   * queryInterface.dropFunction(\n   *   'someFunction',\n   *   [\n   *     {type: 'varchar', name: 'param1', direction: 'IN'},\n   *     {type: 'integer', name: 'param2', direction: 'INOUT'}\n   *   ]\n   * );\n   *\n   * @param {string} functionName Name of SQL function to drop\n   * @param {Array}  params       List of parameters declared for SQL function\n   * @param {object} [options]    query options\n   *\n   * @returns {Promise}\n   */\n  async dropFunction(functionName, params, options) {\n    const sql = this.queryGenerator.dropFunction(functionName, params);\n    options = options || {};\n\n    if (sql) {\n      return await this.sequelize.query(sql, options);\n    }\n  }\n\n  /**\n   * Rename an SQL function\n   *\n   * @example\n   * queryInterface.renameFunction(\n   *   'fooFunction',\n   *   [\n   *     {type: 'varchar', name: 'param1', direction: 'IN'},\n   *     {type: 'integer', name: 'param2', direction: 'INOUT'}\n   *   ],\n   *   'barFunction'\n   * );\n   *\n   * @param {string} oldFunctionName  Current name of function\n   * @param {Array}  params           List of parameters declared for SQL function\n   * @param {string} newFunctionName  New name of function\n   * @param {object} [options]        query options\n   *\n   * @returns {Promise}\n   */\n  async renameFunction(oldFunctionName, params, newFunctionName, options) {\n    const sql = this.queryGenerator.renameFunction(oldFunctionName, params, newFunctionName);\n    options = options || {};\n\n    if (sql) {\n      return await this.sequelize.query(sql, options);\n    }\n  }\n\n  // Helper methods useful for querying\n\n  /**\n   * @private\n   */\n  ensureEnums() {\n    // noop by default\n  }\n\n  async setIsolationLevel(transaction, value, options) {\n    if (!transaction || !(transaction instanceof Transaction)) {\n      throw new Error('Unable to set isolation level for a transaction without transaction object!');\n    }\n\n    if (transaction.parent || !value) {\n      // Not possible to set a separate isolation level for savepoints\n      return;\n    }\n\n    options = { ...options, transaction: transaction.parent || transaction };\n\n    const sql = this.queryGenerator.setIsolationLevelQuery(value, {\n      parent: transaction.parent\n    });\n\n    if (!sql) return;\n\n    return await this.sequelize.query(sql, options);\n  }\n\n  async startTransaction(transaction, options) {\n    if (!transaction || !(transaction instanceof Transaction)) {\n      throw new Error('Unable to start a transaction without transaction object!');\n    }\n\n    options = { ...options, transaction: transaction.parent || transaction };\n    options.transaction.name = transaction.parent ? transaction.name : undefined;\n    const sql = this.queryGenerator.startTransactionQuery(transaction);\n\n    return await this.sequelize.query(sql, options);\n  }\n\n  async deferConstraints(transaction, options) {\n    options = { ...options, transaction: transaction.parent || transaction };\n\n    const sql = this.queryGenerator.deferConstraintsQuery(options);\n\n    if (sql) {\n      return await this.sequelize.query(sql, options);\n    }\n  }\n\n  async commitTransaction(transaction, options) {\n    if (!transaction || !(transaction instanceof Transaction)) {\n      throw new Error('Unable to commit a transaction without transaction object!');\n    }\n    if (transaction.parent) {\n      // Savepoints cannot be committed\n      return;\n    }\n\n    options = {\n      ...options,\n      transaction: transaction.parent || transaction,\n      supportsSearchPath: false,\n      completesTransaction: true\n    };\n\n    const sql = this.queryGenerator.commitTransactionQuery(transaction);\n    const promise = this.sequelize.query(sql, options);\n\n    transaction.finished = 'commit';\n\n    return await promise;\n  }\n\n  async rollbackTransaction(transaction, options) {\n    if (!transaction || !(transaction instanceof Transaction)) {\n      throw new Error('Unable to rollback a transaction without transaction object!');\n    }\n\n    options = {\n      ...options,\n      transaction: transaction.parent || transaction,\n      supportsSearchPath: false,\n      completesTransaction: true\n    };\n    options.transaction.name = transaction.parent ? transaction.name : undefined;\n    const sql = this.queryGenerator.rollbackTransactionQuery(transaction);\n    const promise = this.sequelize.query(sql, options);\n\n    transaction.finished = 'rollback';\n\n    return await promise;\n  }\n}\n\nexports.QueryInterface = QueryInterface;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAEA,MAAM,IAAI,QAAQ;AAElB,MAAM,QAAQ,QAAQ;AACtB,MAAM,YAAY,QAAQ;AAC1B,MAAM,cAAc,QAAQ;AAC5B,MAAM,aAAa,QAAQ;AAK3B,qBAAqB;AAAA,EACnB,YAAY,WAAW,gBAAgB;AACrC,SAAK,YAAY;AACjB,SAAK,iBAAiB;AAAA;AAAA,QAgBlB,eAAe,UAAU,SAAS;AACtC,cAAU,WAAW;AACrB,UAAM,MAAM,KAAK,eAAe,oBAAoB,UAAU;AAC9D,WAAO,MAAM,KAAK,UAAU,MAAM,KAAK;AAAA;AAAA,QAWnC,aAAa,UAAU,SAAS;AACpC,cAAU,WAAW;AACrB,UAAM,MAAM,KAAK,eAAe,kBAAkB;AAClD,WAAO,MAAM,KAAK,UAAU,MAAM,KAAK;AAAA;AAAA,QAWnC,aAAa,QAAQ,SAAS;AAClC,cAAU,WAAW;AACrB,UAAM,MAAM,KAAK,eAAe,aAAa;AAC7C,WAAO,MAAM,KAAK,UAAU,MAAM,KAAK;AAAA;AAAA,QAWnC,WAAW,QAAQ,SAAS;AAChC,cAAU,WAAW;AACrB,UAAM,MAAM,KAAK,eAAe,WAAW;AAC3C,WAAO,MAAM,KAAK,UAAU,MAAM,KAAK;AAAA;AAAA,QAUnC,eAAe,SAAS;AAC5B,cAAU,WAAW;AAErB,QAAI,CAAC,KAAK,eAAe,SAAS,SAAS,SAAS;AAClD,aAAO,KAAK,UAAU,KAAK;AAAA;AAE7B,UAAM,UAAU,MAAM,KAAK,eAAe;AAC1C,WAAO,QAAQ,IAAI,QAAQ,IAAI,gBAAc,KAAK,WAAW,YAAY;AAAA;AAAA,QAUrE,eAAe,SAAS;AAC5B,cAAU,iCACL,UADK;AAAA,MAER,KAAK;AAAA,MACL,MAAM,KAAK,UAAU,WAAW;AAAA;AAGlC,UAAM,iBAAiB,KAAK,eAAe,iBAAiB;AAE5D,UAAM,cAAc,MAAM,KAAK,UAAU,MAAM,gBAAgB;AAE/D,WAAO,EAAE,QAAQ,YAAY,IAAI,WAAS,MAAM,cAAc,MAAM,cAAc;AAAA;AAAA,QAY9E,gBAAgB,SAAS;AAC7B,WAAO,MAAM,KAAK,UAAU,MAC1B,KAAK,eAAe,gBACpB,iCAAK,UAAL,EAAc,MAAM,WAAW;AAAA;AAAA,QAyD7B,YAAY,WAAW,YAAY,SAAS,OAAO;AACvD,QAAI,MAAM;AAEV,cAAU,mBAAK;AAEf,QAAI,WAAW,QAAQ,YAAY;AACjC,QAAE,OAAO,QAAQ,YAAY,eAAa;AACxC,YAAI,UAAU,gBAAgB,QAAW;AACvC,oBAAU,cAAc;AAAA;AAAA;AAAA;AAK9B,QAAI,OAAO;AACT,cAAQ,aAAa,QAAQ,cAAc,MAAM;AAAA;AAGnD,iBAAa,EAAE,UACb,YACA,eAAa,KAAK,UAAU,mBAAmB;AAIjD,UAAM,KAAK,YAAY,WAAW,YAAY,SAAS;AAEvD,QACE,CAAC,UAAU,UACV,SAAQ,UAAU,CAAC,CAAC,SAAS,MAAM,UACpC;AACA,kBAAY,KAAK,eAAe,UAAU;AAAA,QACxC;AAAA,QACA,SAAS,CAAC,CAAC,SAAS,MAAM,WAAW,QAAQ;AAAA;AAAA;AAIjD,iBAAa,KAAK,eAAe,gBAAgB,YAAY;AAAA,MAC3D,OAAO;AAAA,MACP,SAAS;AAAA,MACT,8BAA8B,QAAQ;AAAA;AAExC,UAAM,KAAK,eAAe,iBAAiB,WAAW,YAAY;AAElE,WAAO,MAAM,KAAK,UAAU,MAAM,KAAK;AAAA;AAAA,QAUnC,YAAY,WAAW,SAAS;AACpC,UAAM,MAAM,KAAK,eAAe,iBAAiB;AAEjD,UAAM,MAAM,MAAM,KAAK,UAAU,MAAM,KAAK,iCACvC,UADuC;AAAA,MAE1C,MAAM,WAAW;AAAA;AAGnB,WAAO,IAAI,WAAW;AAAA;AAAA,QAWlB,UAAU,WAAW,SAAS;AAElC,cAAU,mBAAK;AACf,YAAQ,UAAU,QAAQ,WAAW,QAAQ,SAAS;AAEtD,UAAM,MAAM,KAAK,eAAe,eAAe,WAAW;AAE1D,UAAM,KAAK,UAAU,MAAM,KAAK;AAAA;AAAA,QAG5B,eAAe,YAAY,MAAM,SAAS;AAC9C,eAAW,aAAa,YAAY;AAElC,UAAI,CAAC,KAAK,SAAS,UAAU,aAAa,YAAY;AACpD,cAAM,KAAK,UAAU,WAAW,iCAAK,UAAL,EAAc,SAAS;AAAA;AAAA;AAAA;AAAA,QAavD,cAAc,SAAS;AAC3B,cAAU,WAAW;AACrB,UAAM,OAAO,QAAQ,QAAQ;AAE7B,UAAM,aAAa,MAAM,KAAK,cAAc;AAC5C,UAAM,cAAc,MAAM,KAAK,wBAAwB,YAAY;AAEnE,eAAW,aAAa,YAAY;AAClC,UAAI,sBAAsB;AAC1B,UAAI,EAAE,SAAS,YAAY;AACzB,8BAAsB,GAAG,UAAU,UAAU,UAAU;AAAA;AAGzD,iBAAW,cAAc,YAAY,sBAAsB;AACzD,cAAM,KAAK,UAAU,MAAM,KAAK,eAAe,oBAAoB,WAAW;AAAA;AAAA;AAGlF,UAAM,KAAK,eAAe,YAAY,MAAM;AAAA;AAAA,QAYxC,YAAY,QAAQ,OAAO,SAAS;AACxC,cAAU,WAAW;AACrB,UAAM,MAAM,KAAK,eAAe,iBAAiB,QAAQ;AACzD,WAAO,MAAM,KAAK,UAAU,MAAM,KAAK;AAAA;AAAA,QAanC,cAAc,SAAS;AAC3B,cAAU,iCACL,UADK;AAAA,MAER,KAAK;AAAA,MACL,MAAM,WAAW;AAAA;AAGnB,UAAM,gBAAgB,KAAK,eAAe,gBAAgB,KAAK,UAAU,OAAO;AAChF,UAAM,aAAa,MAAM,KAAK,UAAU,MAAM,eAAe;AAC7D,WAAO,EAAE,QAAQ;AAAA;AAAA,QA4Bb,cAAc,WAAW,SAAS;AACtC,QAAI,SAAS;AACb,QAAI,kBAAkB;AAEtB,QAAI,OAAO,YAAY,UAAU;AAC/B,eAAS;AAAA,eACA,OAAO,YAAY,YAAY,YAAY,MAAM;AAC1D,eAAS,QAAQ,UAAU;AAC3B,wBAAkB,QAAQ,mBAAmB;AAAA;AAG/C,QAAI,OAAO,cAAc,YAAY,cAAc,MAAM;AACvD,eAAS,UAAU;AACnB,kBAAY,UAAU;AAAA;AAGxB,UAAM,MAAM,KAAK,eAAe,mBAAmB,WAAW,QAAQ;AACtE,cAAU,iCAAK,UAAL,EAAc,MAAM,WAAW;AAEzC,QAAI;AACF,YAAM,OAAO,MAAM,KAAK,UAAU,MAAM,KAAK;AAM7C,UAAI,EAAE,QAAQ,OAAO;AACnB,cAAM,IAAI,MAAM,6BAA6B;AAAA;AAG/C,aAAO;AAAA,aACA,GAAP;AACA,UAAI,EAAE,YAAY,EAAE,SAAS,SAAS,oBAAoB;AACxD,cAAM,IAAI,MAAM,6BAA6B;AAAA;AAG/C,YAAM;AAAA;AAAA;AAAA,QAoBJ,UAAU,OAAO,KAAK,WAAW,SAAS;AAC9C,QAAI,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW;AAChC,YAAM,IAAI,MAAM;AAAA;AAGlB,cAAU,WAAW;AACrB,gBAAY,KAAK,UAAU,mBAAmB;AAC9C,WAAO,MAAM,KAAK,UAAU,MAAM,KAAK,eAAe,eAAe,OAAO,KAAK,YAAY;AAAA;AAAA,QAUzF,aAAa,WAAW,eAAe,SAAS;AACpD,WAAO,KAAK,UAAU,MAAM,KAAK,eAAe,kBAAkB,WAAW,gBAAgB;AAAA;AAAA,EAG/F,mBAAmB,mBAAmB;AACpC,QAAI;AACJ,QAAI,OAAO,OAAO,WAAW,SAAS,oBAAoB;AACxD,kBAAY,EAAE,MAAM,mBAAmB,WAAW;AAAA,WAC7C;AACL,kBAAY;AAAA;AAGd,WAAO,KAAK,UAAU,mBAAmB;AAAA;AAAA,EAW3C,gBAAgB,aAAY,OAAO;AACjC,WAAO,KAAK,eAAe,gBAAgB,aAAY;AAAA;AAAA,EAUzD,iBAAiB,aAAa;AAC5B,WAAO,KAAK,eAAe,iBAAiB;AAAA;AAAA,QAWxC,aAAa,WAAW,eAAe,mBAAmB,SAAS;AACvE,cAAU,WAAW;AAErB,UAAM,QAAQ,KAAK,eAAe,gBAAgB;AAAA,OAC/C,gBAAgB,KAAK,mBAAmB;AAAA,OACxC;AAAA,MACD,SAAS;AAAA,MACT,OAAO;AAAA;AAET,UAAM,MAAM,KAAK,eAAe,kBAAkB,WAAW;AAE7D,WAAO,KAAK,UAAU,MAAM,KAAK;AAAA;AAAA,QAW7B,qBAAqB,WAAW,YAAY,SAAS;AACzD,UAAM,cAAc,MAAM,KAAK,cAAc,WAAW;AACxD,QAAI,YAAY,aAAa;AAC3B,aAAO;AAAA;AAET,UAAM,IAAI,MAAM,SAAS,qCAAqC;AAAA;AAAA,QAa1D,aAAa,WAAW,gBAAgB,eAAe,SAAS;AACpE,cAAU,WAAW;AACrB,UAAM,OAAQ,OAAM,KAAK,qBAAqB,WAAW,gBAAgB,UAAU;AAEnF,UAAM,WAAW;AAEjB,aAAS,iBAAiB;AAAA,MACxB,WAAW;AAAA,MACX,MAAM,KAAK;AAAA,MACX,WAAW,KAAK;AAAA,MAChB,cAAc,KAAK;AAAA;AAIrB,QAAI,KAAK,iBAAiB,QAAQ,CAAC,KAAK,WAAW;AACjD,aAAO,SAAS,eAAe;AAAA;AAGjC,UAAM,MAAM,KAAK,eAAe,kBAC9B,WACA,gBACA,KAAK,eAAe,gBAAgB;AAEtC,WAAO,MAAM,KAAK,UAAU,MAAM,KAAK;AAAA;AAAA,QAqBnC,SAAS,WAAW,YAAY,SAAS,cAAc;AAE3D,QAAI,CAAC,MAAM,QAAQ,aAAa;AAC9B,qBAAe;AACf,gBAAU;AACV,mBAAa,QAAQ;AAAA;AAGvB,QAAI,CAAC,cAAc;AAEjB,qBAAe;AAAA;AAGjB,cAAU,MAAM,UAAU;AAC1B,YAAQ,SAAS;AACjB,UAAM,MAAM,KAAK,eAAe,cAAc,WAAW,SAAS;AAClE,WAAO,MAAM,KAAK,UAAU,MAAM,KAAK,iCAAK,UAAL,EAAc,oBAAoB;AAAA;AAAA,QAYrE,UAAU,WAAW,SAAS;AAClC,UAAM,MAAM,KAAK,eAAe,iBAAiB,WAAW;AAC5D,WAAO,MAAM,KAAK,UAAU,MAAM,KAAK,iCAAK,UAAL,EAAc,MAAM,WAAW;AAAA;AAAA,QAYlE,wBAAwB,YAAY,SAAS;AACjD,QAAI,WAAW,WAAW,GAAG;AAC3B,aAAO;AAAA;AAGT,cAAU,iCAAK,UAAL,EAAc,MAAM,WAAW;AAEzC,UAAM,UAAU,MAAM,QAAQ,IAAI,WAAW,IAAI,eAC/C,KAAK,UAAU,MAAM,KAAK,eAAe,oBAAoB,WAAW,KAAK,UAAU,OAAO,WAAW;AAE3G,UAAM,SAAS;AAEf,eAAW,QAAQ,CAAC,WAAW,MAAM;AACnC,UAAI,EAAE,SAAS,YAAY;AACzB,oBAAY,GAAG,UAAU,UAAU,UAAU;AAAA;AAG/C,aAAO,aAAa,MAAM,QAAQ,QAAQ,MACtC,QAAQ,GAAG,IAAI,OAAK,EAAE,mBACtB,CAAC,QAAQ,MAAM,QAAQ,GAAG;AAE9B,aAAO,aAAa,OAAO,WAAW,OAAO,EAAE;AAAA;AAGjD,WAAO;AAAA;AAAA,QAcH,gCAAgC,WAAW,SAAS;AACxD,UAAM,eAAe,iCAChB,UADgB;AAAA,MAEnB,MAAM,WAAW;AAAA;AAEnB,UAAM,QAAQ,KAAK,eAAe,oBAAoB,WAAW,KAAK,UAAU,OAAO;AACvF,WAAO,KAAK,UAAU,MAAM,OAAO;AAAA;AAAA,QAa/B,YAAY,WAAW,uBAAuB,SAAS;AAC3D,cAAU,WAAW;AACrB,UAAM,MAAM,KAAK,eAAe,iBAAiB,WAAW,uBAAuB;AACnF,WAAO,MAAM,KAAK,UAAU,MAAM,KAAK;AAAA;AAAA,QAoFnC,cAAc,WAAW,SAAS;AACtC,QAAI,CAAC,QAAQ,QAAQ;AACnB,YAAM,IAAI,MAAM;AAAA;AAGlB,QAAI,CAAC,QAAQ,MAAM;AACjB,YAAM,IAAI,MAAM;AAAA;AAGlB,cAAU,MAAM,UAAU;AAE1B,UAAM,MAAM,KAAK,eAAe,mBAAmB,WAAW;AAC9D,WAAO,MAAM,KAAK,UAAU,MAAM,KAAK;AAAA;AAAA,QAGnC,eAAe,WAAW,gBAAgB,SAAS;AACvD,UAAM,MAAM,KAAK,eAAe,qBAAqB,WAAW;AAChE,WAAO,MAAM,KAAK,UAAU,MAAM,KAAK,iCAAK,UAAL,EAAc,MAAM,WAAW;AAAA;AAAA,QAUlE,iBAAiB,WAAW,gBAAgB,SAAS;AACzD,WAAO,KAAK,UAAU,MAAM,KAAK,eAAe,sBAAsB,WAAW,iBAAiB;AAAA;AAAA,QAG9F,OAAO,UAAU,WAAW,QAAQ,SAAS;AACjD,cAAU,MAAM,UAAU;AAC1B,YAAQ,aAAa,YAAY,SAAS,YAAY,QAAQ;AAC9D,UAAM,MAAM,KAAK,eAAe,YAAY,WAAW,QAAQ,YAAY,SAAS,YAAY,eAAe;AAE/G,YAAQ,OAAO,WAAW;AAC1B,YAAQ,WAAW;AAEnB,UAAM,UAAU,MAAM,KAAK,UAAU,MAAM,KAAK;AAChD,QAAI;AAAU,cAAQ,GAAG,cAAc;AAEvC,WAAO;AAAA;AAAA,QAcH,OAAO,WAAW,cAAc,cAAc,OAAO,SAAS;AAClE,cAAU,mBAAK;AAEf,UAAM,QAAQ,QAAQ;AAEtB,YAAQ,OAAO,WAAW;AAC1B,YAAQ,oBAAoB,OAAO,KAAK;AACxC,YAAQ,aAAa,QAAQ,kBAAkB;AAE/C,QAAI,QAAQ,WAAW,WAAW,GAAG;AACnC,YAAM,cAAc,OAAO,OAAO,MAAM,aAAa,IAAI,UAAQ,KAAK;AACtE,YAAM,aAAa,OAAO,OAAO,MAAM,YAAY,OAAO,OAAK,EAAE,OAAO,SAAS,GAAG,IAAI,OAAK,EAAE;AAC/F,YAAM,YAAY,OAAO,OAAO,MAAM,UAAU,OAAO,OAAK,EAAE,UAAU,EAAE,OAAO,SAAS,GAAG,IAAI,OAAK,EAAE;AAGxG,iBAAW,SAAS,QAAQ,mBAAmB;AAC7C,cAAM,YAAY,WAAW,KAAK,YAAU,OAAO,SAAS;AAC5D,YAAI,WAAW;AACb,kBAAQ,aAAa;AACrB;AAAA;AAGF,cAAM,WAAW,UAAU,KAAK,YAAU,OAAO,SAAS;AAC1D,YAAI,UAAU;AACZ,kBAAQ,aAAa;AACrB;AAAA;AAAA;AAKJ,UACE,QAAQ,WAAW,WAAW,KAC3B,EAAE,aAAa,QAAQ,mBAAmB,aAAa,QAC1D;AACA,gBAAQ,aAAa;AAAA;AAGvB,cAAQ,aAAa,EAAE,KAAK,QAAQ;AAAA;AAGtC,UAAM,MAAM,KAAK,eAAe,YAAY,WAAW,cAAc,MAAM,eAAe;AAC1F,WAAO,MAAM,KAAK,UAAU,MAAM,KAAK;AAAA;AAAA,QAwBnC,WAAW,WAAW,SAAS,SAAS,YAAY;AACxD,cAAU,mBAAK;AACf,YAAQ,OAAO,WAAW;AAE1B,UAAM,UAAU,MAAM,KAAK,UAAU,MACnC,KAAK,eAAe,gBAAgB,WAAW,SAAS,SAAS,aACjE;AAGF,WAAO,QAAQ;AAAA;AAAA,QAGX,OAAO,UAAU,WAAW,QAAQ,aAAY,SAAS;AAC7D,cAAU,mBAAK;AACf,YAAQ,aAAa,YAAY,SAAS,YAAY,QAAQ;AAE9D,UAAM,MAAM,KAAK,eAAe,YAAY,WAAW,QAAQ,aAAY,SAAS,SAAS,YAAY;AAEzG,YAAQ,OAAO,WAAW;AAE1B,YAAQ,WAAW;AACnB,WAAO,MAAM,KAAK,UAAU,MAAM,KAAK;AAAA;AAAA,QAsBnC,WAAW,WAAW,QAAQ,aAAY,SAAS,YAAY;AACnE,cAAU,MAAM,UAAU;AAC1B,QAAI,OAAO,gBAAe;AAAU,oBAAa,MAAM,UAAU;AAEjE,UAAM,MAAM,KAAK,eAAe,YAAY,WAAW,QAAQ,aAAY,SAAS;AACpF,UAAM,QAAQ,EAAE,SAAS,aAAa,YAAY,EAAE;AACpD,UAAM,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,EAAE,KAAK,KAAK,UAAU,aAAa,QAAQ,EAAE,WAAW,MAAM;AAE5G,YAAQ,OAAO,WAAW;AAC1B,YAAQ,QAAQ;AAChB,WAAO,MAAM,KAAK,UAAU,MAAM,KAAK;AAAA;AAAA,QAGnC,OAAO,UAAU,WAAW,aAAY,SAAS;AACrD,UAAM,WAAW;AACjB,UAAM,MAAM,KAAK,eAAe,YAAY,WAAW,aAAY,IAAI,SAAS;AAEhF,cAAU,mBAAK;AAGf,QAAI,CAAC,CAAC,SAAS,eAAe,CAAC,CAAC,SAAS,YAAY,cAAc;AACjE,YAAM,OAAO,OAAO,KAAK,SAAS,YAAY;AAC9C,YAAM,SAAS,KAAK;AACpB,UAAI;AAEJ,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,sBAAc,SAAS,YAAY,aAAa,KAAK;AACrD,YAAI,YAAY,WAAW,YAAY,QAAQ,YAC7C,YAAY,QAAQ,SAAS,kBAAkB,aAC/C,YAAY,QAAQ,aAAa,MAAM;AACvC,mBAAS,KAAK,YAAY,UAAU;AAAA;AAAA;AAAA;AAK1C,eAAW,WAAW,UAAU;AAC9B,UAAI,YAAY,MAAM,SAAS,SAAS;AAExC,UAAI,CAAC;AAAW;AAChB,UAAI,CAAC,MAAM,QAAQ;AAAY,oBAAY,CAAC;AAC5C,iBAAW,aAAa;AAAW,cAAM,UAAU,QAAQ;AAAA;AAE7D,YAAQ,WAAW;AACnB,WAAO,MAAM,KAAK,UAAU,MAAM,KAAK;AAAA;AAAA,QAgBnC,WAAW,WAAW,OAAO,SAAS,OAAO;AACjD,cAAU,MAAM,UAAU;AAC1B,cAAU,EAAE,SAAS,SAAS,EAAE,OAAO;AAEvC,QAAI,QAAQ,aAAa,MAAM;AAC7B,aAAO,KAAK,UAAU,MACpB,KAAK,eAAe,mBAAmB,WAAW,UAClD;AAAA;AAIJ,QAAI,OAAO,eAAe;AAAU,cAAQ,MAAM,UAAU;AAE5D,WAAO,MAAM,KAAK,UAAU,MAC1B,KAAK,eAAe,YAAY,WAAW,OAAO,SAAS,QAC3D;AAAA;AAAA,QAIE,OAAO,OAAO,WAAW,YAAY;AACzC,UAAM,UAAU,iCAAK,aAAL,EAAiB,MAAM,WAAW,QAAQ;AAE1D,WAAO,MAAM,KAAK,UAAU,MAC1B,KAAK,eAAe,YAAY,WAAW,SAAS,QACpD;AAAA;AAAA,QAIE,UAAU,OAAO,WAAW,OAAO,yBAAyB,4BAA4B,SAAS;AACrG,cAAU,MAAM,UAAU;AAE1B,UAAM,MAAM,KAAK,eAAe,gBAAgB,KAAK,WAAW,OAAO,yBAAyB,4BAA4B;AAE5H,YAAQ,OAAO,WAAW;AAC1B,YAAQ,QAAQ;AAEhB,WAAO,MAAM,KAAK,UAAU,MAAM,KAAK;AAAA;AAAA,QAGnC,UAAU,OAAO,WAAW,OAAO,yBAAyB,4BAA4B,SAAS;AACrG,cAAU,MAAM,UAAU;AAE1B,UAAM,MAAM,KAAK,eAAe,gBAAgB,KAAK,WAAW,OAAO,yBAAyB,4BAA4B;AAE5H,YAAQ,OAAO,WAAW;AAC1B,YAAQ,QAAQ;AAEhB,WAAO,MAAM,KAAK,UAAU,MAAM,KAAK;AAAA;AAAA,QAGnC,UAAU,WAAW,SAAS,mBAAmB,OAAO;AAC5D,cAAU,MAAM,UAAU;AAC1B,cAAU,EAAE,SAAS,SAAS;AAAA,MAC5B,KAAK;AAAA,MACL,OAAO;AAAA,MACP,MAAM,WAAW;AAAA;AAGnB,UAAM,MAAM,KAAK,eAAe,YAAY,WAAW,SAAS;AAEhE,QAAI,sBAAsB,QAAW;AACnC,YAAM,IAAI,MAAM;AAAA;AAGlB,UAAM,OAAO,MAAM,KAAK,UAAU,MAAM,KAAK;AAC7C,QAAI,CAAC,QAAQ,OAAO;AAClB,aAAO;AAAA;AAGT,UAAM,SAAS,OAAO,KAAK,qBAAqB;AAEhD,QAAI,CAAC,WAAW,CAAC,QAAQ,UAAU;AACjC,aAAO;AAAA;AAGT,UAAM,WAAW,QAAQ;AAEzB,QAAI,oBAAoB,UAAU,WAAW,oBAAoB,UAAU,OAAO;AAChF,UAAI,WAAW,MAAM;AACnB,eAAO,WAAW;AAAA;AAAA;AAGtB,QAAI,oBAAoB,UAAU,WAAW,oBAAoB,UAAU,QAAQ;AACjF,UAAI,WAAW,MAAM;AACnB,eAAO,SAAS,QAAQ;AAAA;AAAA;AAG5B,QAAI,oBAAoB,UAAU,MAAM;AACtC,UAAI,WAAW,QAAQ,CAAE,mBAAkB,OAAO;AAChD,eAAO,IAAI,KAAK;AAAA;AAAA;AAGpB,WAAO;AAAA;AAAA,QAGH,cACJ,WACA,aACA,YACA,aACA,cACA,gBACA,cACA,SACA;AACA,UAAM,MAAM,KAAK,eAAe,cAAc,WAAW,aAAa,YAAY,aAAa,cAAc,gBAAgB;AAC7H,cAAU,WAAW;AACrB,QAAI,KAAK;AACP,aAAO,MAAM,KAAK,UAAU,MAAM,KAAK;AAAA;AAAA;AAAA,QAIrC,YAAY,WAAW,aAAa,SAAS;AACjD,UAAM,MAAM,KAAK,eAAe,YAAY,WAAW;AACvD,cAAU,WAAW;AAErB,QAAI,KAAK;AACP,aAAO,MAAM,KAAK,UAAU,MAAM,KAAK;AAAA;AAAA;AAAA,QAIrC,cAAc,WAAW,gBAAgB,gBAAgB,SAAS;AACtE,UAAM,MAAM,KAAK,eAAe,cAAc,WAAW,gBAAgB;AACzE,cAAU,WAAW;AAErB,QAAI,KAAK;AACP,aAAO,MAAM,KAAK,UAAU,MAAM,KAAK;AAAA;AAAA;AAAA,QAyCrC,eAAe,cAAc,QAAQ,YAAY,UAAU,MAAM,cAAc,SAAS;AAC5F,UAAM,MAAM,KAAK,eAAe,eAAe,cAAc,QAAQ,YAAY,UAAU,MAAM,cAAc;AAC/G,cAAU,WAAW;AAErB,QAAI,KAAK;AACP,aAAO,MAAM,KAAK,UAAU,MAAM,KAAK;AAAA;AAAA;AAAA,QAsBrC,aAAa,cAAc,QAAQ,SAAS;AAChD,UAAM,MAAM,KAAK,eAAe,aAAa,cAAc;AAC3D,cAAU,WAAW;AAErB,QAAI,KAAK;AACP,aAAO,MAAM,KAAK,UAAU,MAAM,KAAK;AAAA;AAAA;AAAA,QAwBrC,eAAe,iBAAiB,QAAQ,iBAAiB,SAAS;AACtE,UAAM,MAAM,KAAK,eAAe,eAAe,iBAAiB,QAAQ;AACxE,cAAU,WAAW;AAErB,QAAI,KAAK;AACP,aAAO,MAAM,KAAK,UAAU,MAAM,KAAK;AAAA;AAAA;AAAA,EAS3C,cAAc;AAAA;AAAA,QAIR,kBAAkB,aAAa,OAAO,SAAS;AACnD,QAAI,CAAC,eAAe,CAAE,wBAAuB,cAAc;AACzD,YAAM,IAAI,MAAM;AAAA;AAGlB,QAAI,YAAY,UAAU,CAAC,OAAO;AAEhC;AAAA;AAGF,cAAU,iCAAK,UAAL,EAAc,aAAa,YAAY,UAAU;AAE3D,UAAM,MAAM,KAAK,eAAe,uBAAuB,OAAO;AAAA,MAC5D,QAAQ,YAAY;AAAA;AAGtB,QAAI,CAAC;AAAK;AAEV,WAAO,MAAM,KAAK,UAAU,MAAM,KAAK;AAAA;AAAA,QAGnC,iBAAiB,aAAa,SAAS;AAC3C,QAAI,CAAC,eAAe,CAAE,wBAAuB,cAAc;AACzD,YAAM,IAAI,MAAM;AAAA;AAGlB,cAAU,iCAAK,UAAL,EAAc,aAAa,YAAY,UAAU;AAC3D,YAAQ,YAAY,OAAO,YAAY,SAAS,YAAY,OAAO;AACnE,UAAM,MAAM,KAAK,eAAe,sBAAsB;AAEtD,WAAO,MAAM,KAAK,UAAU,MAAM,KAAK;AAAA;AAAA,QAGnC,iBAAiB,aAAa,SAAS;AAC3C,cAAU,iCAAK,UAAL,EAAc,aAAa,YAAY,UAAU;AAE3D,UAAM,MAAM,KAAK,eAAe,sBAAsB;AAEtD,QAAI,KAAK;AACP,aAAO,MAAM,KAAK,UAAU,MAAM,KAAK;AAAA;AAAA;AAAA,QAIrC,kBAAkB,aAAa,SAAS;AAC5C,QAAI,CAAC,eAAe,CAAE,wBAAuB,cAAc;AACzD,YAAM,IAAI,MAAM;AAAA;AAElB,QAAI,YAAY,QAAQ;AAEtB;AAAA;AAGF,cAAU,iCACL,UADK;AAAA,MAER,aAAa,YAAY,UAAU;AAAA,MACnC,oBAAoB;AAAA,MACpB,sBAAsB;AAAA;AAGxB,UAAM,MAAM,KAAK,eAAe,uBAAuB;AACvD,UAAM,UAAU,KAAK,UAAU,MAAM,KAAK;AAE1C,gBAAY,WAAW;AAEvB,WAAO,MAAM;AAAA;AAAA,QAGT,oBAAoB,aAAa,SAAS;AAC9C,QAAI,CAAC,eAAe,CAAE,wBAAuB,cAAc;AACzD,YAAM,IAAI,MAAM;AAAA;AAGlB,cAAU,iCACL,UADK;AAAA,MAER,aAAa,YAAY,UAAU;AAAA,MACnC,oBAAoB;AAAA,MACpB,sBAAsB;AAAA;AAExB,YAAQ,YAAY,OAAO,YAAY,SAAS,YAAY,OAAO;AACnE,UAAM,MAAM,KAAK,eAAe,yBAAyB;AACzD,UAAM,UAAU,KAAK,UAAU,MAAM,KAAK;AAE1C,gBAAY,WAAW;AAEvB,WAAO,MAAM;AAAA;AAAA;AAIjB,QAAQ,iBAAiB;", "names": []}