// Force modal test - Direct modal manipulation
console.log('🚀 Force Modal Test Loading...\n');

function forceModalTest() {
    console.log('=== FORCE MODAL TEST ===\n');
    
    // Test 1: Direct modal manipulation
    console.log('1. Testing direct modal manipulation...');
    const modal = document.getElementById('paymentModal');
    
    if (!modal) {
        console.error('❌ CRITICAL: Payment modal not found!');
        return;
    }
    
    console.log('✅ Modal found');
    console.log(`   Current classes: ${modal.className}`);
    console.log(`   Is hidden: ${modal.classList.contains('hidden')}`);
    
    // Test 2: Force open modal
    console.log('\n2. Force opening modal...');
    modal.classList.remove('hidden');
    
    setTimeout(() => {
        const isVisible = !modal.classList.contains('hidden');
        console.log(`   Modal is now visible: ${isVisible ? '✅ YES' : '❌ NO'}`);
        
        if (isVisible) {
            console.log('🎉 SUCCESS: Modal can be opened directly!');
            
            // Test 3: Populate with test data
            console.log('\n3. Populating with test data...');
            
            const vehicleInfo = document.getElementById('payment-vehicle-info');
            if (vehicleInfo) {
                vehicleInfo.innerHTML = `
                    <div class="bg-blue-50 p-4 rounded">
                        <h4 class="font-bold text-blue-800">🧪 TEST VEHICLE</h4>
                        <p><strong>N° dépôt:</strong> TEST-001</p>
                        <p><strong>Immatriculation:</strong> TEST-123</p>
                        <p><strong>Type:</strong> Voiture</p>
                        <p><strong>Jours:</strong> 5 jours</p>
                        <p><strong>Total:</strong> 125.00 DH</p>
                    </div>
                `;
                console.log('✅ Test data populated');
            }
            
            const vehicleIdField = document.getElementById('payment-vehicle-id');
            if (vehicleIdField) {
                vehicleIdField.value = '999';
                console.log('✅ Vehicle ID set to 999');
            }
            
            const amountField = document.getElementById('payment-amount');
            if (amountField) {
                amountField.value = '125.00';
                console.log('✅ Amount set to 125.00');
            }
            
            console.log('\n🎉 Modal is working! The problem is likely in the processPayment method or API call.');
            
            // Close modal after 5 seconds
            setTimeout(() => {
                modal.classList.add('hidden');
                console.log('Test modal closed');
            }, 5000);
            
        } else {
            console.error('❌ FAIL: Modal cannot be opened even directly!');
            console.log('   This suggests a CSS or DOM issue.');
        }
    }, 100);
}

function createForceTestButton() {
    const testBtn = document.createElement('button');
    testBtn.innerHTML = '🚀 Force Modal Test';
    testBtn.className = 'bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded m-2';
    testBtn.onclick = forceModalTest;
    
    // Add to page
    const header = document.querySelector('h1, h2, .text-2xl');
    if (header && header.parentNode) {
        header.parentNode.insertBefore(testBtn, header.nextSibling);
        console.log('🚀 Force test button added');
    }
}

function testModalElements() {
    console.log('\n=== MODAL ELEMENTS TEST ===');
    
    const requiredElements = [
        'paymentModal',
        'payment-vehicle-id',
        'payment-vehicle-info',
        'payment-days',
        'payment-daily-cost',
        'payment-amount',
        'paymentForm'
    ];
    
    requiredElements.forEach(id => {
        const element = document.getElementById(id);
        console.log(`${id}: ${element ? '✅ EXISTS' : '❌ MISSING'}`);
        
        if (element && id === 'paymentModal') {
            console.log(`   Classes: ${element.className}`);
            console.log(`   Style display: ${element.style.display}`);
            console.log(`   Computed display: ${window.getComputedStyle(element).display}`);
        }
    });
}

function testAppMethods() {
    console.log('\n=== APP METHODS TEST ===');
    
    if (!window.app) {
        console.error('❌ window.app not available');
        return;
    }
    
    const methods = [
        'processPayment',
        'populatePaymentForm',
        'handlePayment',
        'apiCall',
        'showError',
        'showSuccess'
    ];
    
    methods.forEach(method => {
        const exists = typeof window.app[method] === 'function';
        console.log(`app.${method}: ${exists ? '✅ EXISTS' : '❌ MISSING'}`);
    });
}

// Auto-run tests
setTimeout(() => {
    console.log('🚀 Starting force modal tests...');
    createForceTestButton();
    testModalElements();
    testAppMethods();
    
    console.log('\n📋 Instructions:');
    console.log('1. Click the "🚀 Force Modal Test" button to test direct modal opening');
    console.log('2. If the modal opens, the problem is in processPayment or API');
    console.log('3. If the modal doesn\'t open, there\'s a CSS or DOM issue');
    console.log('4. Check the console for detailed results');
    
}, 7000); // Wait 7 seconds
