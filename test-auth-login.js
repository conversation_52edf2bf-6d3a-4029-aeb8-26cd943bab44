// Test authentication and login
console.log('🔐 Testing Authentication and Login...\n');

async function testAuthAndLogin() {
    try {
        // Test 1: Check current auth status
        console.log('1. Checking current authentication status...');
        try {
            const response = await fetch('/api/auth/status');
            console.log(`   📋 Auth status: ${response.status} ${response.statusText}`);
            
            if (response.ok) {
                const data = await response.json();
                console.log(`   ✅ Already logged in as: ${data.user.username} (${data.user.role})`);
                return true; // Already authenticated
            } else {
                console.log('   📋 Not authenticated');
            }
        } catch (error) {
            console.log(`   ❌ Auth status error: ${error.message}`);
        }

        // Test 2: Try to create a test user (if not exists)
        console.log('\n2. Attempting to create test user...');
        try {
            const testUser = {
                username: 'admin',
                password: 'admin123',
                email: '<EMAIL>',
                role: 'admin'
            };

            const response = await fetch('/api/users', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(testUser)
            });

            console.log(`   📋 User creation: ${response.status} ${response.statusText}`);
            
            if (response.ok) {
                const data = await response.json();
                console.log(`   ✅ Test user created successfully`);
            } else if (response.status === 409) {
                console.log(`   📋 Test user already exists`);
            } else {
                const errorText = await response.text();
                console.log(`   ❌ Error: ${errorText}`);
            }
        } catch (error) {
            console.log(`   ❌ User creation error: ${error.message}`);
        }

        // Test 3: Try to login with test credentials
        console.log('\n3. Attempting to login...');
        try {
            const loginData = {
                username: 'admin',
                password: 'admin123'
            };

            const response = await fetch('/api/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(loginData)
            });

            console.log(`   📋 Login attempt: ${response.status} ${response.statusText}`);
            
            if (response.ok) {
                const data = await response.json();
                console.log(`   ✅ Login successful: ${data.user.username} (${data.user.role})`);
                return true;
            } else {
                const errorText = await response.text();
                console.log(`   ❌ Login failed: ${errorText}`);
                
                // Try with different credentials
                console.log('\n   📋 Trying alternative credentials...');
                const altResponse = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'password'
                    })
                });
                
                if (altResponse.ok) {
                    const altData = await altResponse.json();
                    console.log(`   ✅ Alternative login successful: ${altData.user.username}`);
                    return true;
                } else {
                    console.log(`   ❌ Alternative login also failed`);
                }
            }
        } catch (error) {
            console.log(`   ❌ Login error: ${error.message}`);
        }

        // Test 4: Check if we can access protected endpoints now
        console.log('\n4. Testing protected endpoints after login...');
        try {
            const response = await fetch('/api/vehicles/types/list');
            console.log(`   📋 Vehicle types: ${response.status} ${response.statusText}`);
            
            if (response.ok) {
                const data = await response.json();
                console.log(`   ✅ Successfully accessed protected endpoint`);
                console.log(`   📋 Found ${data.length} vehicle types`);
                return true;
            } else {
                console.log(`   ❌ Still cannot access protected endpoints`);
            }
        } catch (error) {
            console.log(`   ❌ Protected endpoint test error: ${error.message}`);
        }

        return false;

    } catch (error) {
        console.error('❌ Auth test failed:', error);
        return false;
    }
}

async function runAuthTest() {
    const isAuthenticated = await testAuthAndLogin();
    
    console.log('\n🎉 Authentication test completed!');
    
    if (isAuthenticated) {
        console.log('\n✅ Authentication successful! You can now:');
        console.log('1. Try adding a vehicle through the form');
        console.log('2. Access all protected features');
        console.log('3. Use the application normally');
    } else {
        console.log('\n❌ Authentication failed. Please:');
        console.log('1. Check if the database is properly set up');
        console.log('2. Verify user table exists and has data');
        console.log('3. Try logging in manually through the login page');
        console.log('4. Check server logs for detailed errors');
    }
}

// Run the test
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(runAuthTest, 2000);
    });
} else {
    setTimeout(runAuthTest, 2000);
}
