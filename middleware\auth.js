const bcrypt = require('bcryptjs');
const db = require('../config/database');

// Middleware to check if user is authenticated
const requireAuth = async (req, res, next) => {
    try {
        // Check if session exists
        if (!req.session || !req.session.userId) {
            return res.status(401).json({ error: 'Authentication required' });
        }

        // Get user from database
        const [user] = await db.executeQuery(
            `SELECT u.*, r.name as role_name, r.permissions
             FROM users u
             JOIN roles r ON u.role_id = r.id
             WHERE u.id = ? AND u.is_active = TRUE`,
            [req.session.userId]
        );

        if (!user) {
            req.session.destroy();
            return res.status(401).json({ error: 'User not found or inactive' });
        }

        // Add user to request object
        req.user = user;
        next();

    } catch (error) {
        console.error('Auth middleware error:', error);
        res.status(500).json({ error: 'Authentication error' });
    }
};

// Middleware to check if user has specific permission
const requirePermission = (resource, action) => {
    return async (req, res, next) => {
        try {
            if (!req.user) {
                return res.status(401).json({ error: 'Authentication required' });
            }

            // Admin has all permissions
            if (req.user.role_name === 'admin') {
                return next();
            }

            // Parse permissions string (format: "vehicles:crud,payments:cru")
            const permissions = req.user.permissions || '';
            const resourcePermissions = permissions.split(',')
                .find(perm => perm.startsWith(resource + ':'));

            if (!resourcePermissions) {
                return res.status(403).json({ error: 'Access denied' });
            }

            const actions = resourcePermissions.split(':')[1] || '';

            // Check if user has the required action
            const hasPermission = actions.includes(action) || actions.includes('crud');

            if (!hasPermission) {
                return res.status(403).json({ error: 'Insufficient permissions' });
            }

            next();

        } catch (error) {
            console.error('Permission middleware error:', error);
            res.status(500).json({ error: 'Permission check error' });
        }
    };
};

// Middleware to check if user is admin
const requireAdmin = async (req, res, next) => {
    try {
        if (!req.user) {
            return res.status(401).json({ error: 'Authentication required' });
        }

        if (req.user.role_name !== 'admin') {
            return res.status(403).json({ error: 'Admin access required' });
        }

        next();

    } catch (error) {
        console.error('Admin middleware error:', error);
        res.status(500).json({ error: 'Admin check error' });
    }
};

// Function to hash password
const hashPassword = async (password) => {
    const saltRounds = 10;
    return await bcrypt.hash(password, saltRounds);
};

// Function to verify password
const verifyPassword = async (password, hash) => {
    return await bcrypt.compare(password, hash);
};

// Function to log user action
const logUserAction = async (userId, action, resourceType = null, resourceId = null, details = null, req = null) => {
    try {
        // Temporarily disable audit logging to avoid database constraint issues
        console.log(`User action logged: User ${userId} performed ${action} on ${resourceType}:${resourceId}`);
        return; // Skip database logging for now

        const ipAddress = req ? (req.ip || req.connection.remoteAddress) : null;
        const userAgent = req ? req.get('User-Agent') : null;

        await db.executeQuery(
            'INSERT INTO user_audit_log (user_id, action, resource_type, resource_id, details, ip_address, user_agent) VALUES (?, ?, ?, ?, ?, ?, ?)',
            [userId, action, resourceType, resourceId, details, ipAddress, userAgent]
        );
    } catch (error) {
        console.error('Error logging user action:', error);
        // Don't throw error to avoid breaking the main flow
    }
};

module.exports = {
    requireAuth,
    requirePermission,
    requireAdmin,
    hashPassword,
    verifyPassword,
    logUserAction
};
