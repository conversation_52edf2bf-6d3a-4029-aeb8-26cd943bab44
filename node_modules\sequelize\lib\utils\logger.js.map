{"version": 3, "sources": ["../../src/utils/logger.ts"], "sourcesContent": ["/**\n * @file Sequelize module for debug and deprecation messages.\n * It require a `context` for which messages will be printed.\n *\n * @module logging\n * @access package\n */\nimport nodeDebug from 'debug';\nimport util from 'util';\n\n/**\n * The configuration for sequelize's logging interface.\n *\n * @access package\n */\nexport interface LoggerConfig {\n  /**\n   * The context which the logger should log in.\n   *\n   * @default 'sequelize'\n   */\n  context?: string;\n}\n\nexport class Logger {\n  protected config: LoggerConfig;\n\n  constructor({ context = 'sequelize', ...rest }: Partial<LoggerConfig> = {}) {\n    this.config = {\n      context,\n      ...rest\n    };\n  }\n\n  /**\n   * Logs a warning in the logger's context.\n   *\n   * @param message The message of the warning.\n   */\n  warn(message: string): void {\n    console.warn(`(${this.config.context}) Warning: ${message}`);\n  }\n\n  /**\n   * Uses node's util.inspect to stringify a value.\n   *\n   * @param value The value which should be inspected.\n   * @returns The string of the inspected value.\n   */\n  inspect(value: unknown): string {\n    return util.inspect(value, {\n      showHidden: false,\n      depth: 1\n    });\n  }\n\n  /**\n   * Gets a debugger for a context.\n   *\n   * @param name The name of the context.\n   * @returns A debugger interace which can be used to debug.\n   */\n  debugContext(name: string): nodeDebug.Debugger {\n    return nodeDebug(`${this.config.context}:${name}`);\n  }\n}\n\nexport const logger = new Logger();\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAOA,mBAAsB;AACtB,kBAAiB;AAgBV,aAAa;AAAA,EAGlB,YAAY,KAA4D,IAAI;AAFlE;AAEE,iBAAE,YAAU,gBAAZ,IAA4B,iBAA5B,IAA4B,CAA1B;AACZ,SAAK,SAAS;AAAA,MACZ;AAAA,OACG;AAAA;AAAA,EASP,KAAK,SAAuB;AAC1B,YAAQ,KAAK,IAAI,KAAK,OAAO,qBAAqB;AAAA;AAAA,EASpD,QAAQ,OAAwB;AAC9B,WAAO,oBAAK,QAAQ,OAAO;AAAA,MACzB,YAAY;AAAA,MACZ,OAAO;AAAA;AAAA;AAAA,EAUX,aAAa,MAAkC;AAC7C,WAAO,0BAAU,GAAG,KAAK,OAAO,WAAW;AAAA;AAAA;AAIxC,MAAM,SAAS,IAAI;", "names": []}