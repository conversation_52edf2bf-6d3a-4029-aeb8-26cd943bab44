-- Add actual_release_date column to vehicle_releases table
-- This column will store the actual timestamp when the vehicle was released from the pound

ALTER TABLE vehicle_releases 
ADD COLUMN actual_release_date TIMESTAMP NULL 
AFTER release_date;

-- Add an index for better performance on queries
CREATE INDEX idx_vehicle_releases_actual_release_date 
ON vehicle_releases(actual_release_date);

-- Update existing records to set actual_release_date same as release_date for already released vehicles
UPDATE vehicle_releases vr
JOIN vehicles v ON vr.vehicle_id = v.id
SET vr.actual_release_date = vr.release_date
WHERE v.status = 'released' AND vr.actual_release_date IS NULL;
