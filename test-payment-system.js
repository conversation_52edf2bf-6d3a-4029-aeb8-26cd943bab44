// Test payment system functionality
console.log('💳 Testing Payment System...\n');

async function testPaymentSystem() {
    try {
        // Test 1: Check if payment modal exists
        console.log('1. Checking payment modal structure...');
        const paymentModal = document.getElementById('paymentModal');
        console.log(`   📋 Payment modal exists: ${paymentModal ? '✅ OK' : '❌ MISSING'}`);
        
        if (paymentModal) {
            const requiredElements = [
                'payment-vehicle-id',
                'payment-vehicle-info',
                'payment-days',
                'payment-daily-cost',
                'payment-amount',
                'paymentForm'
            ];
            
            requiredElements.forEach(id => {
                const element = document.getElementById(id);
                console.log(`   📋 ${id}: ${element ? '✅ OK' : '❌ MISSING'}`);
            });
        }

        // Test 2: Check if app has payment methods
        console.log('\n2. Checking payment methods...');
        if (window.app) {
            const paymentMethods = [
                'processPayment',
                'populatePaymentForm',
                'handlePayment',
                'processPaymentFromView'
            ];
            
            paymentMethods.forEach(method => {
                console.log(`   📋 app.${method}: ${typeof window.app[method] === 'function' ? '✅ OK' : '❌ MISSING'}`);
            });
        }

        // Test 3: Test payment form fields
        console.log('\n3. Checking payment form fields...');
        const paymentForm = document.getElementById('paymentForm');
        if (paymentForm) {
            const requiredFields = [
                'vehicle_id',
                'amount',
                'payment_date',
                'receipt_number',
                'payment_method',
                'release_order_number',
                'release_date',
                'released_to_name'
            ];
            
            requiredFields.forEach(fieldName => {
                const field = paymentForm.querySelector(`[name="${fieldName}"]`);
                console.log(`   📋 Field ${fieldName}: ${field ? '✅ OK' : '❌ MISSING'}`);
                if (field) {
                    console.log(`      Type: ${field.type || field.tagName}, Required: ${field.required}`);
                }
            });
        }

        // Test 4: Test payment buttons in vehicle tables
        console.log('\n4. Checking payment buttons in tables...');
        const paymentButtons = document.querySelectorAll('button[onclick*="processPayment"]');
        console.log(`   📋 Found ${paymentButtons.length} payment buttons`);
        
        if (paymentButtons.length > 0) {
            console.log('   📋 Payment buttons found in:');
            paymentButtons.forEach((button, index) => {
                const table = button.closest('table');
                const tableId = table ? table.id : 'unknown table';
                console.log(`      ${index + 1}. ${tableId}`);
            });
        }

        // Test 5: Simulate payment process (if we have test data)
        console.log('\n5. Testing payment process simulation...');
        
        if (window.app && typeof window.app.processPayment === 'function') {
            console.log('   📋 Payment method available for testing');
            
            // Check if we can create a mock vehicle for testing
            const mockVehicle = {
                id: 999,
                depot_number: 'TEST-001',
                license_plate: 'TEST-PAY',
                vehicle_type_name: 'Voiture',
                brand: 'Toyota',
                entry_date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days ago
                storage_cost_per_day: 25,
                depositor: 'Police Municipale'
            };
            
            console.log('   📋 Testing populatePaymentForm with mock data...');
            try {
                await window.app.populatePaymentForm(mockVehicle);
                console.log('   ✅ populatePaymentForm executed successfully');
                
                // Check if fields were populated
                const vehicleInfo = document.getElementById('payment-vehicle-info');
                const daysField = document.getElementById('payment-days');
                const amountField = document.getElementById('payment-amount');
                
                console.log(`   📋 Vehicle info populated: ${vehicleInfo && vehicleInfo.innerHTML.length > 0 ? '✅ OK' : '❌ FAIL'}`);
                console.log(`   📋 Days calculated: ${daysField && daysField.value ? '✅ OK (' + daysField.value + ' days)' : '❌ FAIL'}`);
                console.log(`   📋 Amount calculated: ${amountField && amountField.value ? '✅ OK (' + amountField.value + ' DH)' : '❌ FAIL'}`);
                
            } catch (error) {
                console.log(`   ❌ populatePaymentForm failed: ${error.message}`);
            }
        }

        // Test 6: Test payment API endpoint
        console.log('\n6. Testing payment API endpoint...');
        try {
            // Test if the endpoint exists (without actually sending data)
            const testResponse = await fetch('/api/payments/process-release', {
                method: 'OPTIONS'
            });
            console.log(`   📋 Payment API endpoint: ${testResponse.status < 500 ? '✅ ACCESSIBLE' : '❌ ERROR'}`);
        } catch (error) {
            console.log(`   ❌ Payment API test failed: ${error.message}`);
        }

        console.log('\n🎉 Payment system test completed!');
        
        console.log('\n📋 Manual Testing Instructions:');
        console.log('1. Go to "Véhicules en fourrière" section');
        console.log('2. Click on a payment button (💳) for any vehicle');
        console.log('3. Verify that the modal opens with vehicle details');
        console.log('4. Check that days and total amount are calculated correctly');
        console.log('5. Fill in the required fields (receipt number, release order)');
        console.log('6. Submit the payment and verify the vehicle moves to "En attente de sortie"');

    } catch (error) {
        console.error('❌ Payment system test failed:', error);
    }
}

// Run tests when ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(testPaymentSystem, 3000);
    });
} else {
    setTimeout(testPaymentSystem, 3000);
}
