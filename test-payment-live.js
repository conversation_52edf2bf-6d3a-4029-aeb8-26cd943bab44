// Test payment in real-time with detailed logging
console.log('🔍 Live Payment Testing...\n');

function testPaymentLive() {
    console.log('=== LIVE PAYMENT TEST ===\n');
    
    // Get a real vehicle from the current page
    const paymentButtons = document.querySelectorAll('[onclick*="processPayment"]');
    
    if (paymentButtons.length === 0) {
        console.error('❌ No payment buttons found on this page');
        console.log('💡 Make sure you are on the "Véhicules en fourrière" section');
        return;
    }
    
    console.log(`✅ Found ${paymentButtons.length} payment buttons`);
    
    // Get the first vehicle ID
    const firstButton = paymentButtons[0];
    const onclickAttr = firstButton.getAttribute('onclick');
    const vehicleIdMatch = onclickAttr.match(/processPayment\((\d+)\)/);
    
    if (!vehicleIdMatch) {
        console.error('❌ Could not extract vehicle ID from button');
        return;
    }
    
    const vehicleId = vehicleIdMatch[1];
    console.log(`🚗 Testing with vehicle ID: ${vehicleId}`);
    
    // Test the payment process step by step
    testPaymentProcess(vehicleId);
}

async function testPaymentProcess(vehicleId) {
    console.log('\n🔄 Step 1: Testing vehicle fetch...');
    
    try {
        // Test vehicle fetch
        const vehicleResponse = await fetch(`/api/vehicles/${vehicleId}`);
        console.log(`Vehicle API status: ${vehicleResponse.status}`);
        
        if (!vehicleResponse.ok) {
            console.error('❌ Failed to fetch vehicle');
            const errorText = await vehicleResponse.text();
            console.error('Error:', errorText);
            return;
        }
        
        const vehicle = await vehicleResponse.json();
        console.log('✅ Vehicle fetched successfully:', {
            id: vehicle.id,
            depot_number: vehicle.depot_number,
            license_plate: vehicle.license_plate,
            status: vehicle.status
        });
        
        console.log('\n🔄 Step 2: Testing payment calculation...');
        
        // Calculate payment
        const entryDate = new Date(vehicle.entry_date);
        const currentDate = new Date();
        const daysInPound = Math.ceil((currentDate - entryDate) / (1000 * 60 * 60 * 24));
        const dailyCost = vehicle.storage_cost_per_day || 25;
        const totalAmount = daysInPound * dailyCost;
        
        console.log('✅ Payment calculated:', {
            days: daysInPound,
            dailyCost: dailyCost,
            total: totalAmount
        });
        
        console.log('\n🔄 Step 3: Testing payment submission...');
        
        // Create payment data
        const timestamp = Date.now().toString().slice(-6);
        const dateStr = currentDate.toISOString().slice(0, 10).replace(/-/g, '');
        
        const paymentData = {
            vehicle_id: vehicleId,
            amount: totalAmount.toString(),
            payment_date: currentDate.toISOString().split('T')[0],
            receipt_number: `QUI-${dateStr}-${timestamp}`,
            release_order_number: `ORD-${dateStr}-${timestamp}`,
            payment_method: 'cash',
            notes: 'Test payment'
        };
        
        console.log('Payment data to submit:', paymentData);
        
        // Submit payment
        const paymentResponse = await fetch('/api/payments/process-release', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(paymentData)
        });
        
        console.log(`Payment API status: ${paymentResponse.status}`);
        
        const responseText = await paymentResponse.text();
        console.log('Payment API response:', responseText);
        
        if (paymentResponse.ok) {
            console.log('🎉 PAYMENT SUCCESS!');
            const result = JSON.parse(responseText);
            console.log('Success details:', result);
        } else {
            console.error('❌ PAYMENT FAILED!');
            try {
                const errorResult = JSON.parse(responseText);
                console.error('Error details:', errorResult);
            } catch (e) {
                console.error('Raw error response:', responseText);
            }
        }
        
    } catch (error) {
        console.error('❌ Test failed with error:', error);
    }
}

function createLiveTestButton() {
    const testBtn = document.createElement('button');
    testBtn.innerHTML = '🔍 Test Payment Live';
    testBtn.className = 'bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded m-2';
    testBtn.onclick = testPaymentLive;
    
    // Add to page
    const header = document.querySelector('h1, h2, .text-2xl');
    if (header && header.parentNode) {
        header.parentNode.insertBefore(testBtn, header.nextSibling);
        console.log('🔍 Live payment test button added');
    }
}

// Auto-run setup
setTimeout(() => {
    console.log('🚀 Setting up live payment test...');
    createLiveTestButton();
    
    console.log('\n📋 Instructions:');
    console.log('1. Make sure you are on "Véhicules en fourrière" section');
    console.log('2. Click "🔍 Test Payment Live" to test with a real vehicle');
    console.log('3. Watch the console for detailed step-by-step results');
    console.log('4. This will show exactly where the payment process fails');
    
}, 9000); // Wait 9 seconds
