{"name": "connect-session-sequelize", "version": "7.1.7", "description": "Session store for connect-session using sequelize", "homepage": "https://github.com/mweibel/connect-session-sequelize", "bugs": "https://github.com/mweibel/connect-session-sequelize/issues", "main": "index.js", "scripts": {"test": "mocha ./test", "lint": "standard"}, "repository": {"type": "git", "url": "https://github.com/mweibel/connect-session-sequelize.git"}, "dependencies": {"debug": "^4.1.1"}, "devDependencies": {"express-session": "^1.17.1", "mocha": "^9.2.2", "sequelize": ">=6.1.0", "sqlite3": "^4.2.0", "standard": "^14.3.4"}, "peerDependencies": {"sequelize": ">= 6.1.0"}, "engines": {"node": ">= 10"}, "keywords": ["connect-session", "connect", "sequelize", "postgres", "mysql", "sqlite"], "author": "<PERSON> <<EMAIL>> (https://github.com/mweibel)", "license": "MIT", "files": ["lib", "index.js", "index.d.ts"]}