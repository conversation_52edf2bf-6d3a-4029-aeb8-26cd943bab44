// Script to check database structure using existing config
const db = require('./config/database');

async function checkDatabaseStructure() {
    try {
        console.log('🔍 Checking vehicles table structure...\n');
        
        // Get table structure
        const columns = await db.executeQuery('DESCRIBE vehicles');
        
        console.log('📊 VEHICLES TABLE STRUCTURE:');
        console.log('=====================================');
        console.log('Field\t\t\tType\t\t\tNull\tKey\tDefault\tExtra');
        console.log('-----\t\t\t----\t\t\t----\t---\t-------\t-----');
        
        columns.forEach(col => {
            const field = col.Field.padEnd(20);
            const type = col.Type.padEnd(20);
            const nullable = col.Null.padEnd(8);
            const key = col.Key.padEnd(8);
            const defaultVal = (col.Default || 'NULL').toString().padEnd(12);
            const extra = col.Extra || '';
            
            console.log(`${field}${type}${nullable}${key}${defaultVal}${extra}`);
        });
        
        console.log('\n🔍 EXISTING COLUMNS:');
        const existingColumns = columns.map(col => col.Field);
        existingColumns.forEach((col, index) => {
            console.log(`${index + 1}. ${col}`);
        });
        
        console.log('\n❌ COLUMNS REFERENCED IN CODE BUT MISSING:');
        const referencedColumns = [
            'depot_number', 'license_plate', 'vehicle_type_id', 'brand', 
            'year', 'vin', 'depositor', 'entry_date', 'entry_time', 
            'location_found', 'reason_impounded', 'observations', 
            'storage_cost_per_day', 'owner_id'
        ];
        
        const missingColumns = referencedColumns.filter(col => !existingColumns.includes(col));
        if (missingColumns.length > 0) {
            missingColumns.forEach((col, index) => {
                console.log(`${index + 1}. ${col} ❌`);
            });
        } else {
            console.log('None - all referenced columns exist! ✅');
        }
        
        console.log('\n✅ COLUMNS THAT EXIST AND ARE REFERENCED:');
        const presentColumns = referencedColumns.filter(col => existingColumns.includes(col));
        presentColumns.forEach((col, index) => {
            console.log(`${index + 1}. ${col} ✅`);
        });
        
        console.log('\n🔧 SUGGESTED SQL QUERY FOR INSERTION:');
        const validColumns = existingColumns.filter(col => 
            !['id', 'created_at', 'updated_at', 'status'].includes(col)
        );
        
        const insertColumns = validColumns.join(', ');
        const placeholders = validColumns.map(() => '?').join(', ');
        
        console.log(`INSERT INTO vehicles (${insertColumns})`);
        console.log(`VALUES (${placeholders})`);
        
        process.exit(0);
        
    } catch (error) {
        console.error('❌ Error checking table structure:', error.message);
        process.exit(1);
    }
}

checkDatabaseStructure();
