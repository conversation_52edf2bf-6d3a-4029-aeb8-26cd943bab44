{"version": 3, "sources": ["../../../src/dialects/postgres/data-types.js"], "sourcesContent": ["'use strict';\n\nconst _ = require('lodash');\nconst wkx = require('wkx');\n\nmodule.exports = BaseTypes => {\n  const warn = BaseTypes.ABSTRACT.warn.bind(undefined, 'http://www.postgresql.org/docs/9.4/static/datatype.html');\n\n  /**\n   * Removes unsupported Postgres options, i.e., LENGTH, UNSIGNED and ZEROFILL, for the integer data types.\n   *\n   * @param {object} dataType The base integer data type.\n   * @private\n   */\n  function removeUnsupportedIntegerOptions(dataType) {\n    if (dataType._length || dataType.options.length || dataType._unsigned || dataType._zerofill) {\n      warn(`PostgresSQL does not support '${dataType.key}' with LENGTH, UNSIGNED or ZEROFILL. Plain '${dataType.key}' will be used instead.`);\n      dataType._length = undefined;\n      dataType.options.length = undefined;\n      dataType._unsigned = undefined;\n      dataType._zerofill = undefined;\n    }\n  }\n\n  /**\n   * types:\n   * {\n   *   oids: [oid],\n   *   array_oids: [oid]\n   * }\n   *\n   * @see oid here https://github.com/lib/pq/blob/master/oid/types.go\n   */\n\n  BaseTypes.UUID.types.postgres = ['uuid'];\n  BaseTypes.CIDR.types.postgres = ['cidr'];\n  BaseTypes.INET.types.postgres = ['inet'];\n  BaseTypes.MACADDR.types.postgres = ['macaddr'];\n  BaseTypes.TSVECTOR.types.postgres = ['tsvector'];\n  BaseTypes.JSON.types.postgres = ['json'];\n  BaseTypes.JSONB.types.postgres = ['jsonb'];\n  BaseTypes.TIME.types.postgres = ['time'];\n\n  class DATEONLY extends BaseTypes.DATEONLY {\n    _stringify(value, options) {\n      if (value === Infinity) {\n        return 'Infinity';\n      }\n      if (value === -Infinity) {\n        return '-Infinity';\n      }\n      return super._stringify(value, options);\n    }\n    _sanitize(value, options) {\n      if ((!options || options && !options.raw) && value !== Infinity && value !== -Infinity) {\n        if (typeof value === 'string') {\n          const lower = value.toLowerCase();\n          if (lower === 'infinity') {\n            return Infinity;\n          }\n          if (lower === '-infinity') {\n            return -Infinity;\n          }\n        }\n        return super._sanitize(value);\n      }\n      return value;\n    }\n    static parse(value) {\n      if (value === 'infinity') {\n        return Infinity;\n      }\n      if (value === '-infinity') {\n        return -Infinity;\n      }\n      return value;\n    }\n  }\n\n  BaseTypes.DATEONLY.types.postgres = ['date'];\n\n  class DECIMAL extends BaseTypes.DECIMAL {\n    static parse(value) {\n      return value;\n    }\n  }\n\n  // numeric\n  BaseTypes.DECIMAL.types.postgres = ['numeric'];\n\n  class STRING extends BaseTypes.STRING {\n    toSql() {\n      if (this._binary) {\n        return 'BYTEA';\n      }\n      return super.toSql();\n    }\n  }\n\n  BaseTypes.STRING.types.postgres = ['varchar'];\n\n  class TEXT extends BaseTypes.TEXT {\n    toSql() {\n      if (this._length) {\n        warn('PostgreSQL does not support TEXT with options. Plain `TEXT` will be used instead.');\n        this._length = undefined;\n      }\n      return 'TEXT';\n    }\n  }\n\n  BaseTypes.TEXT.types.postgres = ['text'];\n\n  class CITEXT extends BaseTypes.CITEXT {\n    static parse(value) {\n      return value;\n    }\n  }\n\n  BaseTypes.CITEXT.types.postgres = ['citext'];\n\n  class CHAR extends BaseTypes.CHAR {\n    toSql() {\n      if (this._binary) {\n        return 'BYTEA';\n      }\n      return super.toSql();\n    }\n  }\n\n  BaseTypes.CHAR.types.postgres = ['char', 'bpchar'];\n\n  class BOOLEAN extends BaseTypes.BOOLEAN {\n    toSql() {\n      return 'BOOLEAN';\n    }\n    _sanitize(value) {\n      if (value !== null && value !== undefined) {\n        if (Buffer.isBuffer(value) && value.length === 1) {\n          // Bit fields are returned as buffers\n          value = value[0];\n        }\n        if (typeof value === 'string') {\n          // Only take action on valid boolean strings.\n          return ['true', 't'].includes(value) ? true : ['false', 'f'].includes(value) ? false : value;\n        }\n        if (typeof value === 'number') {\n          // Only take action on valid boolean integers.\n          return value === 1 ? true : value === 0 ? false : value;\n        }\n      }\n      return value;\n    }\n  }\n\n  BOOLEAN.parse = BOOLEAN.prototype._sanitize;\n\n  BaseTypes.BOOLEAN.types.postgres = ['bool'];\n\n  class DATE extends BaseTypes.DATE {\n    toSql() {\n      return 'TIMESTAMP WITH TIME ZONE';\n    }\n    validate(value) {\n      if (value !== Infinity && value !== -Infinity) {\n        return super.validate(value);\n      }\n      return true;\n    }\n    _stringify(value, options) {\n      if (value === Infinity) {\n        return 'Infinity';\n      }\n      if (value === -Infinity) {\n        return '-Infinity';\n      }\n      return super._stringify(value, options);\n    }\n    _sanitize(value, options) {\n      if ((!options || options && !options.raw) && !(value instanceof Date) && !!value && value !== Infinity && value !== -Infinity) {\n        if (typeof value === 'string') {\n          const lower = value.toLowerCase();\n          if (lower === 'infinity') {\n            return Infinity;\n          }\n          if (lower === '-infinity') {\n            return -Infinity;\n          }\n        }\n        return new Date(value);\n      }\n      return value;\n    }\n  }\n\n  BaseTypes.DATE.types.postgres = ['timestamptz'];\n\n  class TINYINT extends BaseTypes.TINYINT {\n    constructor(length) {\n      super(length);\n      removeUnsupportedIntegerOptions(this);\n    }\n  }\n  // int2\n  BaseTypes.TINYINT.types.postgres = ['int2'];\n\n  class SMALLINT extends BaseTypes.SMALLINT {\n    constructor(length) {\n      super(length);\n      removeUnsupportedIntegerOptions(this);\n    }\n  }\n  // int2\n  BaseTypes.SMALLINT.types.postgres = ['int2'];\n\n  class INTEGER extends BaseTypes.INTEGER {\n    constructor(length) {\n      super(length);\n      removeUnsupportedIntegerOptions(this);\n    }\n  }\n  INTEGER.parse = function parse(value) {\n    return parseInt(value, 10);\n  };\n\n  // int4\n  BaseTypes.INTEGER.types.postgres = ['int4'];\n\n  class BIGINT extends BaseTypes.BIGINT {\n    constructor(length) {\n      super(length);\n      removeUnsupportedIntegerOptions(this);\n    }\n  }\n  // int8\n  BaseTypes.BIGINT.types.postgres = ['int8'];\n\n  class REAL extends BaseTypes.REAL {\n    constructor(length) {\n      super(length);\n      removeUnsupportedIntegerOptions(this);\n    }\n  }\n  // float4\n  BaseTypes.REAL.types.postgres = ['float4'];\n\n  class DOUBLE extends BaseTypes.DOUBLE {\n    constructor(length) {\n      super(length);\n      removeUnsupportedIntegerOptions(this);\n    }\n  }\n  // float8\n  BaseTypes.DOUBLE.types.postgres = ['float8'];\n\n  class FLOAT extends BaseTypes.FLOAT {\n    constructor(length, decimals) {\n      super(length, decimals);\n      // POSTGRES does only support lengths as parameter.\n      // Values between 1-24 result in REAL\n      // Values between 25-53 result in DOUBLE PRECISION\n      // If decimals are provided remove these and print a warning\n      if (this._decimals) {\n        warn('PostgreSQL does not support FLOAT with decimals. Plain `FLOAT` will be used instead.');\n        this._length = undefined;\n        this.options.length = undefined;\n        this._decimals = undefined;\n      }\n      if (this._unsigned) {\n        warn('PostgreSQL does not support FLOAT unsigned. `UNSIGNED` was removed.');\n        this._unsigned = undefined;\n      }\n      if (this._zerofill) {\n        warn('PostgreSQL does not support FLOAT zerofill. `ZEROFILL` was removed.');\n        this._zerofill = undefined;\n      }\n    }\n  }\n  delete FLOAT.parse; // Float has no separate type in PG\n\n  class BLOB extends BaseTypes.BLOB {\n    toSql() {\n      if (this._length) {\n        warn('PostgreSQL does not support BLOB (BYTEA) with options. Plain `BYTEA` will be used instead.');\n        this._length = undefined;\n      }\n      return 'BYTEA';\n    }\n    _hexify(hex) {\n      // bytea hex format http://www.postgresql.org/docs/current/static/datatype-binary.html\n      return `E'\\\\\\\\x${hex}'`;\n    }\n  }\n\n  BaseTypes.BLOB.types.postgres = ['bytea'];\n\n  class GEOMETRY extends BaseTypes.GEOMETRY {\n    toSql() {\n      let result = this.key;\n      if (this.type) {\n        result += `(${this.type}`;\n        if (this.srid) {\n          result += `,${this.srid}`;\n        }\n        result += ')';\n      }\n      return result;\n    }\n    static parse(value) {\n      const b = Buffer.from(value, 'hex');\n      return wkx.Geometry.parse(b).toGeoJSON({ shortCrs: true });\n    }\n    _stringify(value, options) {\n      return `ST_GeomFromGeoJSON(${options.escape(JSON.stringify(value))})`;\n    }\n    _bindParam(value, options) {\n      return `ST_GeomFromGeoJSON(${options.bindParam(value)})`;\n    }\n  }\n\n  BaseTypes.GEOMETRY.types.postgres = ['geometry'];\n\n\n  class GEOGRAPHY extends BaseTypes.GEOGRAPHY {\n    toSql() {\n      let result = 'GEOGRAPHY';\n      if (this.type) {\n        result += `(${this.type}`;\n        if (this.srid) {\n          result += `,${this.srid}`;\n        }\n        result += ')';\n      }\n      return result;\n    }\n    static parse(value) {\n      const b = Buffer.from(value, 'hex');\n      return wkx.Geometry.parse(b).toGeoJSON({ shortCrs: true });\n    }\n    _stringify(value, options) {\n      return `ST_GeomFromGeoJSON(${options.escape(JSON.stringify(value))})`;\n    }\n    bindParam(value, options) {\n      return `ST_GeomFromGeoJSON(${options.bindParam(value)})`;\n    }\n  }\n\n  BaseTypes.GEOGRAPHY.types.postgres = ['geography'];\n\n  let hstore;\n\n  class HSTORE extends BaseTypes.HSTORE {\n    constructor() {\n      super();\n      if (!hstore) {\n        // All datatype files are loaded at import - make sure we don't load the hstore parser before a hstore is instantiated\n        hstore = require('./hstore');\n      }\n    }\n    _value(value) {\n      if (!hstore) {\n        // All datatype files are loaded at import - make sure we don't load the hstore parser before a hstore is instantiated\n        hstore = require('./hstore');\n      }\n      return hstore.stringify(value);\n    }\n    _stringify(value) {\n      return `'${this._value(value)}'`;\n    }\n    _bindParam(value, options) {\n      return options.bindParam(this._value(value));\n    }\n    static parse(value) {\n      if (!hstore) {\n        // All datatype files are loaded at import - make sure we don't load the hstore parser before a hstore is instantiated\n        hstore = require('./hstore');\n      }\n      return hstore.parse(value);\n    }\n  }\n\n  HSTORE.prototype.escape = false;\n\n  BaseTypes.HSTORE.types.postgres = ['hstore'];\n\n  class RANGE extends BaseTypes.RANGE {\n    _value(values, options) {\n      if (!Array.isArray(values)) {\n        return this.options.subtype.stringify(values, options);\n      }\n      const valueInclusivity = [true, false];\n      const valuesStringified = values.map((value, index) => {\n        if (_.isObject(value) && Object.prototype.hasOwnProperty.call(value, 'value')) {\n          if (Object.prototype.hasOwnProperty.call(value, 'inclusive')) {\n            valueInclusivity[index] = value.inclusive;\n          }\n          value = value.value;\n        }\n        if (value === null || value === -Infinity || value === Infinity) {\n          // Pass through \"unbounded\" bounds unchanged\n          return value;\n        }\n        if (this.options.subtype.stringify) {\n          return this.options.subtype.stringify(value, options);\n        }\n        return options.escape(value);\n      });\n      // Array.map does not preserve extra array properties\n      valuesStringified.inclusive = valueInclusivity;\n      return range.stringify(valuesStringified);\n    }\n    _stringify(values, options) {\n      const value = this._value(values, options);\n      if (!Array.isArray(values)) {\n        return `'${value}'::${this.toCastType()}`;\n      }\n      return `'${value}'`;\n    }\n    _bindParam(values, options) {\n      const value = this._value(values, options);\n      if (!Array.isArray(values)) {\n        return `${options.bindParam(value)}::${this.toCastType()}`;\n      }\n      return options.bindParam(value);\n    }\n    toSql() {\n      return BaseTypes.RANGE.types.postgres.subtypes[this._subtype.toLowerCase()];\n    }\n    toCastType() {\n      return BaseTypes.RANGE.types.postgres.castTypes[this._subtype.toLowerCase()];\n    }\n    static parse(value, options = { parser: val => val }) {\n      return range.parse(value, options.parser);\n    }\n  }\n  const range = require('./range');\n\n  RANGE.prototype.escape = false;\n\n  BaseTypes.RANGE.types.postgres = {\n    subtypes: {\n      integer: 'int4range',\n      decimal: 'numrange',\n      date: 'tstzrange',\n      dateonly: 'daterange',\n      bigint: 'int8range'\n    },\n    castTypes: {\n      integer: 'int4',\n      decimal: 'numeric',\n      date: 'timestamptz',\n      dateonly: 'date',\n      bigint: 'int8'\n    }\n  };\n\n  // TODO: Why are base types being manipulated??\n  BaseTypes.ARRAY.prototype.escape = false;\n  BaseTypes.ARRAY.prototype._value = function _value(values, options) {\n    return values.map(value => {\n      if (options && options.bindParam && this.type && this.type._value) {\n        return this.type._value(value, options);\n      }\n      if (this.type && this.type.stringify) {\n        value = this.type.stringify(value, options);\n\n        if (this.type.escape === false) {\n          return value;\n        }\n      }\n      return options.escape(value);\n    }, this);\n  };\n  BaseTypes.ARRAY.prototype._stringify = function _stringify(values, options) {\n    let str = `ARRAY[${this._value(values, options).join(',')}]`;\n\n    if (this.type) {\n      const Utils = require('../../utils');\n      let castKey = this.toSql();\n\n      if (this.type instanceof BaseTypes.ENUM) {\n        const table = options.field.Model.getTableName();\n        const useSchema = table.schema !== undefined;\n        const schemaWithDelimiter = useSchema ? `${Utils.addTicks(table.schema, '\"')}${table.delimiter}` : '';\n\n        castKey = `${Utils.addTicks(\n          Utils.generateEnumName(useSchema ? table.tableName : table, options.field.field),\n          '\"'\n        ) }[]`;\n\n        str += `::${schemaWithDelimiter}${castKey}`;\n      } else {\n        str += `::${castKey}`;\n      }\n    }\n\n    return str;\n  };\n  BaseTypes.ARRAY.prototype._bindParam = function _bindParam(values, options) {\n    return options.bindParam(this._value(values, options));\n  };\n\n  class ENUM extends BaseTypes.ENUM {\n    static parse(value) {\n      return value;\n    }\n  }\n\n  BaseTypes.ENUM.types.postgres = [null];\n\n  return {\n    DECIMAL,\n    BLOB,\n    STRING,\n    CHAR,\n    TEXT,\n    CITEXT,\n    TINYINT,\n    SMALLINT,\n    INTEGER,\n    BIGINT,\n    BOOLEAN,\n    DATE,\n    DATEONLY,\n    REAL,\n    'DOUBLE PRECISION': DOUBLE,\n    FLOAT,\n    GEOMETRY,\n    GEOGRAPHY,\n    HSTORE,\n    RANGE,\n    ENUM\n  };\n};\n"], "mappings": ";AAEA,MAAM,IAAI,QAAQ;AAClB,MAAM,MAAM,QAAQ;AAEpB,OAAO,UAAU,eAAa;AAC5B,QAAM,OAAO,UAAU,SAAS,KAAK,KAAK,QAAW;AAQrD,2CAAyC,UAAU;AACjD,QAAI,SAAS,WAAW,SAAS,QAAQ,UAAU,SAAS,aAAa,SAAS,WAAW;AAC3F,WAAK,iCAAiC,SAAS,kDAAkD,SAAS;AAC1G,eAAS,UAAU;AACnB,eAAS,QAAQ,SAAS;AAC1B,eAAS,YAAY;AACrB,eAAS,YAAY;AAAA;AAAA;AAczB,YAAU,KAAK,MAAM,WAAW,CAAC;AACjC,YAAU,KAAK,MAAM,WAAW,CAAC;AACjC,YAAU,KAAK,MAAM,WAAW,CAAC;AACjC,YAAU,QAAQ,MAAM,WAAW,CAAC;AACpC,YAAU,SAAS,MAAM,WAAW,CAAC;AACrC,YAAU,KAAK,MAAM,WAAW,CAAC;AACjC,YAAU,MAAM,MAAM,WAAW,CAAC;AAClC,YAAU,KAAK,MAAM,WAAW,CAAC;AAEjC,yBAAuB,UAAU,SAAS;AAAA,IACxC,WAAW,OAAO,SAAS;AACzB,UAAI,UAAU,UAAU;AACtB,eAAO;AAAA;AAET,UAAI,UAAU,WAAW;AACvB,eAAO;AAAA;AAET,aAAO,MAAM,WAAW,OAAO;AAAA;AAAA,IAEjC,UAAU,OAAO,SAAS;AACxB,UAAK,EAAC,WAAW,WAAW,CAAC,QAAQ,QAAQ,UAAU,YAAY,UAAU,WAAW;AACtF,YAAI,OAAO,UAAU,UAAU;AAC7B,gBAAM,QAAQ,MAAM;AACpB,cAAI,UAAU,YAAY;AACxB,mBAAO;AAAA;AAET,cAAI,UAAU,aAAa;AACzB,mBAAO;AAAA;AAAA;AAGX,eAAO,MAAM,UAAU;AAAA;AAEzB,aAAO;AAAA;AAAA,WAEF,MAAM,OAAO;AAClB,UAAI,UAAU,YAAY;AACxB,eAAO;AAAA;AAET,UAAI,UAAU,aAAa;AACzB,eAAO;AAAA;AAET,aAAO;AAAA;AAAA;AAIX,YAAU,SAAS,MAAM,WAAW,CAAC;AAErC,wBAAsB,UAAU,QAAQ;AAAA,WAC/B,MAAM,OAAO;AAClB,aAAO;AAAA;AAAA;AAKX,YAAU,QAAQ,MAAM,WAAW,CAAC;AAEpC,uBAAqB,UAAU,OAAO;AAAA,IACpC,QAAQ;AACN,UAAI,KAAK,SAAS;AAChB,eAAO;AAAA;AAET,aAAO,MAAM;AAAA;AAAA;AAIjB,YAAU,OAAO,MAAM,WAAW,CAAC;AAEnC,qBAAmB,UAAU,KAAK;AAAA,IAChC,QAAQ;AACN,UAAI,KAAK,SAAS;AAChB,aAAK;AACL,aAAK,UAAU;AAAA;AAEjB,aAAO;AAAA;AAAA;AAIX,YAAU,KAAK,MAAM,WAAW,CAAC;AAEjC,uBAAqB,UAAU,OAAO;AAAA,WAC7B,MAAM,OAAO;AAClB,aAAO;AAAA;AAAA;AAIX,YAAU,OAAO,MAAM,WAAW,CAAC;AAEnC,qBAAmB,UAAU,KAAK;AAAA,IAChC,QAAQ;AACN,UAAI,KAAK,SAAS;AAChB,eAAO;AAAA;AAET,aAAO,MAAM;AAAA;AAAA;AAIjB,YAAU,KAAK,MAAM,WAAW,CAAC,QAAQ;AAEzC,wBAAsB,UAAU,QAAQ;AAAA,IACtC,QAAQ;AACN,aAAO;AAAA;AAAA,IAET,UAAU,OAAO;AACf,UAAI,UAAU,QAAQ,UAAU,QAAW;AACzC,YAAI,OAAO,SAAS,UAAU,MAAM,WAAW,GAAG;AAEhD,kBAAQ,MAAM;AAAA;AAEhB,YAAI,OAAO,UAAU,UAAU;AAE7B,iBAAO,CAAC,QAAQ,KAAK,SAAS,SAAS,OAAO,CAAC,SAAS,KAAK,SAAS,SAAS,QAAQ;AAAA;AAEzF,YAAI,OAAO,UAAU,UAAU;AAE7B,iBAAO,UAAU,IAAI,OAAO,UAAU,IAAI,QAAQ;AAAA;AAAA;AAGtD,aAAO;AAAA;AAAA;AAIX,UAAQ,QAAQ,QAAQ,UAAU;AAElC,YAAU,QAAQ,MAAM,WAAW,CAAC;AAEpC,qBAAmB,UAAU,KAAK;AAAA,IAChC,QAAQ;AACN,aAAO;AAAA;AAAA,IAET,SAAS,OAAO;AACd,UAAI,UAAU,YAAY,UAAU,WAAW;AAC7C,eAAO,MAAM,SAAS;AAAA;AAExB,aAAO;AAAA;AAAA,IAET,WAAW,OAAO,SAAS;AACzB,UAAI,UAAU,UAAU;AACtB,eAAO;AAAA;AAET,UAAI,UAAU,WAAW;AACvB,eAAO;AAAA;AAET,aAAO,MAAM,WAAW,OAAO;AAAA;AAAA,IAEjC,UAAU,OAAO,SAAS;AACxB,UAAK,EAAC,WAAW,WAAW,CAAC,QAAQ,QAAQ,CAAE,kBAAiB,SAAS,CAAC,CAAC,SAAS,UAAU,YAAY,UAAU,WAAW;AAC7H,YAAI,OAAO,UAAU,UAAU;AAC7B,gBAAM,QAAQ,MAAM;AACpB,cAAI,UAAU,YAAY;AACxB,mBAAO;AAAA;AAET,cAAI,UAAU,aAAa;AACzB,mBAAO;AAAA;AAAA;AAGX,eAAO,IAAI,KAAK;AAAA;AAElB,aAAO;AAAA;AAAA;AAIX,YAAU,KAAK,MAAM,WAAW,CAAC;AAEjC,wBAAsB,UAAU,QAAQ;AAAA,IACtC,YAAY,QAAQ;AAClB,YAAM;AACN,sCAAgC;AAAA;AAAA;AAIpC,YAAU,QAAQ,MAAM,WAAW,CAAC;AAEpC,yBAAuB,UAAU,SAAS;AAAA,IACxC,YAAY,QAAQ;AAClB,YAAM;AACN,sCAAgC;AAAA;AAAA;AAIpC,YAAU,SAAS,MAAM,WAAW,CAAC;AAErC,wBAAsB,UAAU,QAAQ;AAAA,IACtC,YAAY,QAAQ;AAClB,YAAM;AACN,sCAAgC;AAAA;AAAA;AAGpC,UAAQ,QAAQ,eAAe,OAAO;AACpC,WAAO,SAAS,OAAO;AAAA;AAIzB,YAAU,QAAQ,MAAM,WAAW,CAAC;AAEpC,uBAAqB,UAAU,OAAO;AAAA,IACpC,YAAY,QAAQ;AAClB,YAAM;AACN,sCAAgC;AAAA;AAAA;AAIpC,YAAU,OAAO,MAAM,WAAW,CAAC;AAEnC,qBAAmB,UAAU,KAAK;AAAA,IAChC,YAAY,QAAQ;AAClB,YAAM;AACN,sCAAgC;AAAA;AAAA;AAIpC,YAAU,KAAK,MAAM,WAAW,CAAC;AAEjC,uBAAqB,UAAU,OAAO;AAAA,IACpC,YAAY,QAAQ;AAClB,YAAM;AACN,sCAAgC;AAAA;AAAA;AAIpC,YAAU,OAAO,MAAM,WAAW,CAAC;AAEnC,sBAAoB,UAAU,MAAM;AAAA,IAClC,YAAY,QAAQ,UAAU;AAC5B,YAAM,QAAQ;AAKd,UAAI,KAAK,WAAW;AAClB,aAAK;AACL,aAAK,UAAU;AACf,aAAK,QAAQ,SAAS;AACtB,aAAK,YAAY;AAAA;AAEnB,UAAI,KAAK,WAAW;AAClB,aAAK;AACL,aAAK,YAAY;AAAA;AAEnB,UAAI,KAAK,WAAW;AAClB,aAAK;AACL,aAAK,YAAY;AAAA;AAAA;AAAA;AAIvB,SAAO,MAAM;AAEb,qBAAmB,UAAU,KAAK;AAAA,IAChC,QAAQ;AACN,UAAI,KAAK,SAAS;AAChB,aAAK;AACL,aAAK,UAAU;AAAA;AAEjB,aAAO;AAAA;AAAA,IAET,QAAQ,KAAK;AAEX,aAAO,UAAU;AAAA;AAAA;AAIrB,YAAU,KAAK,MAAM,WAAW,CAAC;AAEjC,yBAAuB,UAAU,SAAS;AAAA,IACxC,QAAQ;AACN,UAAI,SAAS,KAAK;AAClB,UAAI,KAAK,MAAM;AACb,kBAAU,IAAI,KAAK;AACnB,YAAI,KAAK,MAAM;AACb,oBAAU,IAAI,KAAK;AAAA;AAErB,kBAAU;AAAA;AAEZ,aAAO;AAAA;AAAA,WAEF,MAAM,OAAO;AAClB,YAAM,IAAI,OAAO,KAAK,OAAO;AAC7B,aAAO,IAAI,SAAS,MAAM,GAAG,UAAU,EAAE,UAAU;AAAA;AAAA,IAErD,WAAW,OAAO,SAAS;AACzB,aAAO,sBAAsB,QAAQ,OAAO,KAAK,UAAU;AAAA;AAAA,IAE7D,WAAW,OAAO,SAAS;AACzB,aAAO,sBAAsB,QAAQ,UAAU;AAAA;AAAA;AAInD,YAAU,SAAS,MAAM,WAAW,CAAC;AAGrC,0BAAwB,UAAU,UAAU;AAAA,IAC1C,QAAQ;AACN,UAAI,SAAS;AACb,UAAI,KAAK,MAAM;AACb,kBAAU,IAAI,KAAK;AACnB,YAAI,KAAK,MAAM;AACb,oBAAU,IAAI,KAAK;AAAA;AAErB,kBAAU;AAAA;AAEZ,aAAO;AAAA;AAAA,WAEF,MAAM,OAAO;AAClB,YAAM,IAAI,OAAO,KAAK,OAAO;AAC7B,aAAO,IAAI,SAAS,MAAM,GAAG,UAAU,EAAE,UAAU;AAAA;AAAA,IAErD,WAAW,OAAO,SAAS;AACzB,aAAO,sBAAsB,QAAQ,OAAO,KAAK,UAAU;AAAA;AAAA,IAE7D,UAAU,OAAO,SAAS;AACxB,aAAO,sBAAsB,QAAQ,UAAU;AAAA;AAAA;AAInD,YAAU,UAAU,MAAM,WAAW,CAAC;AAEtC,MAAI;AAEJ,uBAAqB,UAAU,OAAO;AAAA,IACpC,cAAc;AACZ;AACA,UAAI,CAAC,QAAQ;AAEX,iBAAS,QAAQ;AAAA;AAAA;AAAA,IAGrB,OAAO,OAAO;AACZ,UAAI,CAAC,QAAQ;AAEX,iBAAS,QAAQ;AAAA;AAEnB,aAAO,OAAO,UAAU;AAAA;AAAA,IAE1B,WAAW,OAAO;AAChB,aAAO,IAAI,KAAK,OAAO;AAAA;AAAA,IAEzB,WAAW,OAAO,SAAS;AACzB,aAAO,QAAQ,UAAU,KAAK,OAAO;AAAA;AAAA,WAEhC,MAAM,OAAO;AAClB,UAAI,CAAC,QAAQ;AAEX,iBAAS,QAAQ;AAAA;AAEnB,aAAO,OAAO,MAAM;AAAA;AAAA;AAIxB,SAAO,UAAU,SAAS;AAE1B,YAAU,OAAO,MAAM,WAAW,CAAC;AAEnC,sBAAoB,UAAU,MAAM;AAAA,IAClC,OAAO,QAAQ,SAAS;AACtB,UAAI,CAAC,MAAM,QAAQ,SAAS;AAC1B,eAAO,KAAK,QAAQ,QAAQ,UAAU,QAAQ;AAAA;AAEhD,YAAM,mBAAmB,CAAC,MAAM;AAChC,YAAM,oBAAoB,OAAO,IAAI,CAAC,OAAO,UAAU;AACrD,YAAI,EAAE,SAAS,UAAU,OAAO,UAAU,eAAe,KAAK,OAAO,UAAU;AAC7E,cAAI,OAAO,UAAU,eAAe,KAAK,OAAO,cAAc;AAC5D,6BAAiB,SAAS,MAAM;AAAA;AAElC,kBAAQ,MAAM;AAAA;AAEhB,YAAI,UAAU,QAAQ,UAAU,aAAa,UAAU,UAAU;AAE/D,iBAAO;AAAA;AAET,YAAI,KAAK,QAAQ,QAAQ,WAAW;AAClC,iBAAO,KAAK,QAAQ,QAAQ,UAAU,OAAO;AAAA;AAE/C,eAAO,QAAQ,OAAO;AAAA;AAGxB,wBAAkB,YAAY;AAC9B,aAAO,MAAM,UAAU;AAAA;AAAA,IAEzB,WAAW,QAAQ,SAAS;AAC1B,YAAM,QAAQ,KAAK,OAAO,QAAQ;AAClC,UAAI,CAAC,MAAM,QAAQ,SAAS;AAC1B,eAAO,IAAI,WAAW,KAAK;AAAA;AAE7B,aAAO,IAAI;AAAA;AAAA,IAEb,WAAW,QAAQ,SAAS;AAC1B,YAAM,QAAQ,KAAK,OAAO,QAAQ;AAClC,UAAI,CAAC,MAAM,QAAQ,SAAS;AAC1B,eAAO,GAAG,QAAQ,UAAU,WAAW,KAAK;AAAA;AAE9C,aAAO,QAAQ,UAAU;AAAA;AAAA,IAE3B,QAAQ;AACN,aAAO,UAAU,MAAM,MAAM,SAAS,SAAS,KAAK,SAAS;AAAA;AAAA,IAE/D,aAAa;AACX,aAAO,UAAU,MAAM,MAAM,SAAS,UAAU,KAAK,SAAS;AAAA;AAAA,WAEzD,MAAM,OAAO,UAAU,EAAE,QAAQ,SAAO,OAAO;AACpD,aAAO,MAAM,MAAM,OAAO,QAAQ;AAAA;AAAA;AAGtC,QAAM,QAAQ,QAAQ;AAEtB,QAAM,UAAU,SAAS;AAEzB,YAAU,MAAM,MAAM,WAAW;AAAA,IAC/B,UAAU;AAAA,MACR,SAAS;AAAA,MACT,SAAS;AAAA,MACT,MAAM;AAAA,MACN,UAAU;AAAA,MACV,QAAQ;AAAA;AAAA,IAEV,WAAW;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,MAAM;AAAA,MACN,UAAU;AAAA,MACV,QAAQ;AAAA;AAAA;AAKZ,YAAU,MAAM,UAAU,SAAS;AACnC,YAAU,MAAM,UAAU,SAAS,gBAAgB,QAAQ,SAAS;AAClE,WAAO,OAAO,IAAI,WAAS;AACzB,UAAI,WAAW,QAAQ,aAAa,KAAK,QAAQ,KAAK,KAAK,QAAQ;AACjE,eAAO,KAAK,KAAK,OAAO,OAAO;AAAA;AAEjC,UAAI,KAAK,QAAQ,KAAK,KAAK,WAAW;AACpC,gBAAQ,KAAK,KAAK,UAAU,OAAO;AAEnC,YAAI,KAAK,KAAK,WAAW,OAAO;AAC9B,iBAAO;AAAA;AAAA;AAGX,aAAO,QAAQ,OAAO;AAAA,OACrB;AAAA;AAEL,YAAU,MAAM,UAAU,aAAa,oBAAoB,QAAQ,SAAS;AAC1E,QAAI,MAAM,SAAS,KAAK,OAAO,QAAQ,SAAS,KAAK;AAErD,QAAI,KAAK,MAAM;AACb,YAAM,QAAQ,QAAQ;AACtB,UAAI,UAAU,KAAK;AAEnB,UAAI,KAAK,gBAAgB,UAAU,MAAM;AACvC,cAAM,QAAQ,QAAQ,MAAM,MAAM;AAClC,cAAM,YAAY,MAAM,WAAW;AACnC,cAAM,sBAAsB,YAAY,GAAG,MAAM,SAAS,MAAM,QAAQ,OAAO,MAAM,cAAc;AAEnG,kBAAU,GAAG,MAAM,SACjB,MAAM,iBAAiB,YAAY,MAAM,YAAY,OAAO,QAAQ,MAAM,QAC1E;AAGF,eAAO,KAAK,sBAAsB;AAAA,aAC7B;AACL,eAAO,KAAK;AAAA;AAAA;AAIhB,WAAO;AAAA;AAET,YAAU,MAAM,UAAU,aAAa,oBAAoB,QAAQ,SAAS;AAC1E,WAAO,QAAQ,UAAU,KAAK,OAAO,QAAQ;AAAA;AAG/C,qBAAmB,UAAU,KAAK;AAAA,WACzB,MAAM,OAAO;AAClB,aAAO;AAAA;AAAA;AAIX,YAAU,KAAK,MAAM,WAAW,CAAC;AAEjC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,oBAAoB;AAAA,IACpB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA;", "names": []}