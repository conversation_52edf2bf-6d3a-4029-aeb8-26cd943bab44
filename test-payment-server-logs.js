// Test payment and monitor server logs
const express = require('express');
const fetch = require('node-fetch');

console.log('🔍 Payment Server Log Monitor\n');

async function testPaymentWithLogs() {
    console.log('=== TESTING PAYMENT WITH SERVER MONITORING ===\n');
    
    try {
        // First, get a list of vehicles
        console.log('1. Fetching vehicles...');
        const vehiclesResponse = await fetch('http://localhost:3000/api/vehicles?status=impounded');
        
        if (!vehiclesResponse.ok) {
            console.error('❌ Failed to fetch vehicles:', vehiclesResponse.status);
            return;
        }
        
        const vehicles = await vehiclesResponse.json();
        console.log(`✅ Found ${vehicles.length} vehicles in fourrière`);
        
        if (vehicles.length === 0) {
            console.log('💡 No vehicles found. Add a vehicle first.');
            return;
        }
        
        const testVehicle = vehicles[0];
        console.log('🚗 Testing with vehicle:', {
            id: testVehicle.id,
            depot_number: testVehicle.depot_number,
            license_plate: testVehicle.license_plate
        });
        
        // Calculate payment
        const entryDate = new Date(testVehicle.entry_date);
        const currentDate = new Date();
        const daysInPound = Math.ceil((currentDate - entryDate) / (1000 * 60 * 60 * 24));
        const dailyCost = testVehicle.storage_cost_per_day || 25;
        const totalAmount = daysInPound * dailyCost;
        
        console.log('\n2. Payment calculation:', {
            entry_date: testVehicle.entry_date,
            days_in_pound: daysInPound,
            daily_cost: dailyCost,
            total_amount: totalAmount
        });
        
        // Create payment data
        const timestamp = Date.now().toString().slice(-6);
        const dateStr = currentDate.toISOString().slice(0, 10).replace(/-/g, '');
        
        const paymentData = {
            vehicle_id: testVehicle.id,
            amount: totalAmount,
            payment_date: currentDate.toISOString().split('T')[0],
            receipt_number: `QUI-${dateStr}-${timestamp}`,
            release_order_number: `ORD-${dateStr}-${timestamp}`,
            payment_method: 'cash',
            notes: 'Automated test payment'
        };
        
        console.log('\n3. Submitting payment data:', paymentData);
        
        // Submit payment
        const paymentResponse = await fetch('http://localhost:3000/api/payments/process-release', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(paymentData)
        });
        
        console.log(`\n4. Payment response status: ${paymentResponse.status} ${paymentResponse.statusText}`);
        
        const responseText = await paymentResponse.text();
        
        if (paymentResponse.ok) {
            console.log('🎉 PAYMENT SUCCESS!');
            const result = JSON.parse(responseText);
            console.log('Success details:', result);
            
            // Verify vehicle status changed
            console.log('\n5. Verifying vehicle status...');
            const updatedVehicleResponse = await fetch(`http://localhost:3000/api/vehicles/${testVehicle.id}`);
            if (updatedVehicleResponse.ok) {
                const updatedVehicle = await updatedVehicleResponse.json();
                console.log(`✅ Vehicle status updated to: ${updatedVehicle.status}`);
            }
            
        } else {
            console.error('❌ PAYMENT FAILED!');
            console.error('Response body:', responseText);
            
            try {
                const errorResult = JSON.parse(responseText);
                console.error('Parsed error:', errorResult);
                
                if (errorResult.missing_fields) {
                    console.error('Missing fields:', errorResult.missing_fields);
                }
                if (errorResult.received_data) {
                    console.error('Data received by server:', errorResult.received_data);
                }
            } catch (e) {
                console.error('Could not parse error response as JSON');
            }
        }
        
    } catch (error) {
        console.error('❌ Test failed with error:', error.message);
        console.error('Stack trace:', error.stack);
    }
}

// Run the test
console.log('🚀 Starting payment test with server monitoring...\n');
testPaymentWithLogs();
