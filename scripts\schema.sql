-- Fourrière Vehicle Management Database Schema
-- Created for vehicle impound lot management system

-- Create database if not exists
CREATE DATABASE IF NOT EXISTS fourriere_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE fourriere_db;

-- Table: vehicle_types
CREATE TABLE IF NOT EXISTS vehicle_types (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table: vehicle_owners
CREATE TABLE IF NOT EXISTS vehicle_owners (
    id INT AUTO_INCREMENT PRIMARY KEY,
    first_name VARCHA<PERSON>(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    email VARCHAR(100),
    address TEXT,
    id_number VARCHA<PERSON>(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMES<PERSON>MP,
    INDEX idx_owner_name (last_name, first_name),
    INDEX idx_owner_phone (phone),
    INDEX idx_owner_id_number (id_number)
);

-- Table: vehicles
CREATE TABLE IF NOT EXISTS vehicles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    depot_number VARCHAR(50) NOT NULL UNIQUE,
    license_plate VARCHAR(20) NOT NULL,
    vehicle_type_id INT NOT NULL,
    brand VARCHAR(100),
    model VARCHAR(100),
    color VARCHAR(50),
    year YEAR,
    vin VARCHAR(50),
    depositor VARCHAR(200) NOT NULL,
    entry_date DATE NOT NULL,
    entry_time TIME,
    location_found TEXT,
    reason_impounded TEXT,
    observations TEXT,
    status ENUM('impounded', 'pending_release', 'released', 'overdue', 'destroyed') DEFAULT 'impounded',
    owner_id INT,
    storage_cost_per_day DECIMAL(10,2) DEFAULT 25.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (vehicle_type_id) REFERENCES vehicle_types(id),
    FOREIGN KEY (owner_id) REFERENCES vehicle_owners(id),
    INDEX idx_depot_number (depot_number),
    INDEX idx_license_plate (license_plate),
    INDEX idx_status (status),
    INDEX idx_entry_date (entry_date),
    INDEX idx_depositor (depositor)
);

-- Table: vehicle_photos
CREATE TABLE IF NOT EXISTS vehicle_photos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    vehicle_id INT NOT NULL,
    photo_path VARCHAR(500) NOT NULL,
    photo_type ENUM('entry', 'damage', 'general') DEFAULT 'general',
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (vehicle_id) REFERENCES vehicles(id) ON DELETE CASCADE,
    INDEX idx_vehicle_photos (vehicle_id)
);

-- Table: payments
CREATE TABLE IF NOT EXISTS payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    vehicle_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_date DATE NOT NULL,
    receipt_number VARCHAR(100) NOT NULL UNIQUE,
    payment_method ENUM('cash', 'check', 'bank_transfer', 'card') DEFAULT 'cash',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (vehicle_id) REFERENCES vehicles(id),
    INDEX idx_payment_date (payment_date),
    INDEX idx_receipt_number (receipt_number),
    INDEX idx_vehicle_payment (vehicle_id)
);

-- Table: vehicle_releases
CREATE TABLE IF NOT EXISTS vehicle_releases (
    id INT AUTO_INCREMENT PRIMARY KEY,
    vehicle_id INT NOT NULL,
    release_order_number VARCHAR(100) NOT NULL UNIQUE,
    release_date DATE NOT NULL,
    release_time TIME,
    released_to_name VARCHAR(200) NOT NULL,
    released_to_id VARCHAR(50),
    released_to_phone VARCHAR(20),
    total_storage_days INT NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    payment_id INT,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (vehicle_id) REFERENCES vehicles(id),
    FOREIGN KEY (payment_id) REFERENCES payments(id),
    INDEX idx_release_date (release_date),
    INDEX idx_release_order (release_order_number)
);

-- Table: system_settings
CREATE TABLE IF NOT EXISTS system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table: depositors
CREATE TABLE IF NOT EXISTS depositors (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(200) NOT NULL UNIQUE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_depositor_name (name),
    INDEX idx_depositor_active (is_active)
);

-- Insert default vehicle types
INSERT IGNORE INTO vehicle_types (name, description) VALUES
('Voiture', 'Véhicule de tourisme'),
('Moto', 'Motocyclette et scooter'),
('Camion', 'Véhicule utilitaire lourd'),
('Camionnette', 'Véhicule utilitaire léger'),
('Bus', 'Véhicule de transport en commun'),
('Autre', 'Autres types de véhicules');

-- Insert default depositors
INSERT IGNORE INTO depositors (name) VALUES
('Police Municipale'),
('Gendarmerie Royale'),
('Police de la Route'),
('Sûreté Nationale'),
('Police Judiciaire'),
('Autorités Locales');

-- Insert default system settings
INSERT IGNORE INTO system_settings (setting_key, setting_value, description) VALUES
('default_storage_cost', '25.00', 'Coût de stockage par défaut par jour en DH'),
('overdue_threshold_days', '365', 'Nombre de jours après lequel un véhicule est considéré en retard'),
('currency', 'DH', 'Devise utilisée'),
('company_name', 'Fourrière Communale', 'Nom de la fourrière'),
('company_address', 'Adresse de la fourrière', 'Adresse complète'),
('company_phone', '+212 XXX XXX XXX', 'Téléphone de contact'),
('company_email', '<EMAIL>', 'Email de contact');
