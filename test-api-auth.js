// Test API and authentication
console.log('🔧 Testing API and Authentication...\n');

async function testAPI() {
    try {
        // Test 1: Test basic API endpoint
        console.log('1. Testing basic API endpoint...');
        try {
            const response = await fetch('/api/test');
            const data = await response.json();
            console.log(`   📋 API test: ${response.ok ? '✅ OK' : '❌ FAIL'} (${response.status})`);
            console.log(`   📋 Response: ${JSON.stringify(data)}`);
        } catch (error) {
            console.log(`   ❌ API test error: ${error.message}`);
        }

        // Test 2: Test vehicle types endpoint (correct URL)
        console.log('\n2. Testing vehicle types endpoint...');
        try {
            const response = await fetch('/api/vehicles/types/list');
            console.log(`   📋 Vehicle types: ${response.status} ${response.statusText}`);

            if (response.ok) {
                const data = await response.json();
                console.log(`   📋 Found ${data.length} vehicle types`);
                data.forEach((type, index) => {
                    console.log(`      ${index + 1}. ${type.name} (ID: ${type.id})`);
                });
            } else {
                const errorText = await response.text();
                console.log(`   ❌ Error: ${errorText}`);
            }
        } catch (error) {
            console.log(`   ❌ Vehicle types error: ${error.message}`);
        }

        // Test 3: Test depositors endpoint
        console.log('\n3. Testing depositors endpoint...');
        try {
            const response = await fetch('/api/depositors');
            console.log(`   📋 Depositors: ${response.status} ${response.statusText}`);

            if (response.ok) {
                const data = await response.json();
                console.log(`   📋 Found ${data.length} depositors`);
                data.forEach((depositor, index) => {
                    console.log(`      ${index + 1}. ${depositor.name} (ID: ${depositor.id})`);
                });
            } else {
                const errorText = await response.text();
                console.log(`   ❌ Error: ${errorText}`);
            }
        } catch (error) {
            console.log(`   ❌ Depositors error: ${error.message}`);
        }

        // Test 4: Test vehicle creation with minimal data
        console.log('\n4. Testing vehicle creation...');
        const testVehicle = {
            depot_number: `TEST${Date.now()}`,
            license_plate: 'TEST-API',
            vehicle_type_id: 1,
            depositor: 'Police Municipale',
            entry_date: new Date().toISOString().split('T')[0]
        };

        try {
            const response = await fetch('/api/vehicles', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(testVehicle)
            });

            console.log(`   📋 Vehicle creation: ${response.status} ${response.statusText}`);

            if (response.ok) {
                const data = await response.json();
                console.log(`   ✅ Success: ${JSON.stringify(data)}`);
            } else {
                const errorText = await response.text();
                console.log(`   ❌ Error: ${errorText}`);

                // Try to parse as JSON for better error details
                try {
                    const errorData = JSON.parse(errorText);
                    if (errorData.errors) {
                        console.log('   📋 Validation errors:');
                        errorData.errors.forEach(err => {
                            console.log(`      - ${err.msg} (${err.param})`);
                        });
                    }
                } catch (parseError) {
                    // Error text is not JSON, that's fine
                }
            }
        } catch (error) {
            console.log(`   ❌ Vehicle creation error: ${error.message}`);
        }

        // Test 5: Check session/authentication status
        console.log('\n5. Testing authentication status...');
        try {
            const response = await fetch('/api/auth/status');
            console.log(`   📋 Auth status: ${response.status} ${response.statusText}`);

            if (response.ok) {
                const data = await response.json();
                console.log(`   📋 User: ${data.user ? data.user.username : 'Not logged in'}`);
                console.log(`   📋 Role: ${data.user ? data.user.role : 'N/A'}`);
            } else {
                console.log('   📋 Not authenticated or auth endpoint not available');
            }
        } catch (error) {
            console.log(`   ❌ Auth status error: ${error.message}`);
        }

        console.log('\n🎉 API testing completed!');
        console.log('\n📋 If vehicle creation failed:');
        console.log('1. Check if you are logged in');
        console.log('2. Check server console for detailed errors');
        console.log('3. Verify database connection');
        console.log('4. Check if required tables exist');

    } catch (error) {
        console.error('❌ Test failed:', error);
    }
}

// Run tests
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(testAPI, 1000);
    });
} else {
    setTimeout(testAPI, 1000);
}
